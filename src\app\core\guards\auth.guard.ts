// src/app/core/guards/auth.guard.ts
import { Injectable } from '@angular/core';
import { Router, ActivatedRouteSnapshot, RouterStateSnapshot, CanActivate } from '@angular/router';
import { AuthService } from '../services/auth.service';

@Injectable({
  providedIn: 'root'
})
export class AuthGuard implements CanActivate {
  constructor(
    private router: Router,
    private authService: AuthService
  ) {}

  canActivate(route: ActivatedRouteSnapshot, state: RouterStateSnapshot): boolean {
    if (this.authService.isLoggedIn()) {
      // Check if route has data.roles and user has one of the required roles
      if (route.data['roles'] && !this.checkRoles(route.data['roles'])) {
        // Role not authorized, redirect to home page
        this.router.navigate(['/']);
        return false;
      }
      
      // Authorized, return true
      return true;
    }

    // Not logged in, redirect to login page with return url
    this.router.navigate(['/auth/signin'], { queryParams: { returnUrl: state.url } });
    return false;
  }

  private checkRoles(roles: string[]): boolean {
    const userRole = this.authService.currentUserValue?.role;
    return roles.includes(userRole);
  }
}