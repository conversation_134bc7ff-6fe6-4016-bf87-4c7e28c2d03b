import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { EmployeeSurveyComponent } from './employee-survey.component';
import { SurveyStartComponent } from './survey-start/survey-start.component';
import { SurveyQuizComponent } from './survey-quiz/survey-quiz.component';
import { SurveyCompleteComponent } from './survey-complete/survey-complete.component';

const routes: Routes = [
  {
    path: '',
    component: EmployeeSurveyComponent,
    children: [
      {
        path: '',
        redirectTo: 'start',
        pathMatch: 'full'
      },
      {
        path: 'start',
        component: SurveyStartComponent
      },
      {
        path: 'quiz',
        component: SurveyQuizComponent
      },
      {
        path: 'complete',
        component: SurveyCompleteComponent
      }
    ]
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class EmployeeSurveyRoutingModule { }
