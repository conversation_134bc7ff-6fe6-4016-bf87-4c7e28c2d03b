<!-- Survey Start Interface -->
<div class="survey-start">
  <!-- Employee Name Display -->
  <div class="name-display">
    <h2 class="name-title">{{ getDisplayName() }}</h2>
    <div class="name-underline" [class.active]="employeeName.length > 0"></div>
  </div>

  <!-- Virtual Keyboard -->
  <div class="virtual-keyboard">
    <!-- Letter Rows -->
    <div class="keyboard-row" *ngFor="let row of keyboardRows">
      <button 
        class="key-button letter-key"
        *ngFor="let letter of row"
        (click)="onLetterClick(letter)"
        [disabled]="employeeName.length >= 20">
        {{ letter }}
      </button>
    </div>

    <!-- Action Buttons Row -->
    <div class="keyboard-row action-row">
      <button 
        class="key-button action-key space-key"
        (click)="onSpace()"
        [disabled]="employeeName.length === 0 || employeeName.endsWith(' ')">
        <i class="feather icon-minus"></i>
        SPACE
      </button>
      
      <button 
        class="key-button action-key backspace-key"
        (click)="onBackspace()"
        [disabled]="employeeName.length === 0">
        <i class="feather icon-delete"></i>
        BACK
      </button>
      
      <button 
        class="key-button action-key clear-key"
        (click)="onClear()"
        [disabled]="employeeName.length === 0">
        <i class="feather icon-x"></i>
        CLEAR
      </button>
    </div>
  </div>

  <!-- Character Counter -->
  <div class="character-counter">
    <span class="counter-text">{{ employeeName.length }} / 20</span>
  </div>

  <!-- Start Survey Button -->
  <div class="start-section" *ngIf="showStartButton">
    <button 
      class="start-button"
      (click)="startSurvey()"
      [disabled]="!isNameComplete">
      <i class="feather icon-play-circle"></i>
      Start Survey
    </button>
    
    <p class="start-instruction">
      Ready to begin? Click the button above to start your survey.
    </p>
  </div>

  <!-- Instructions -->
  <div class="instructions" *ngIf="!showStartButton">
    <div class="instruction-card">
      <i class="feather icon-info instruction-icon"></i>
      <h3>Welcome to the Employee Survey</h3>
      <p>Please enter your name using the virtual keyboard above to get started.</p>
      <ul class="instruction-list">
        <li>Tap letters to spell your name</li>
        <li>Use SPACE for spaces between names</li>
        <li>Use BACK to delete characters</li>
        <li>Use CLEAR to start over</li>
      </ul>
    </div>
  </div>
</div>
