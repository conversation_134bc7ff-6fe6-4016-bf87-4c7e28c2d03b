import { Category } from './category.model';

export interface MultiLanguageText {
  en: string;
  [key: string]: string; // Support for additional languages
}

export interface QuestionOption {
  id?: string;
  option_text: string;
  option_value: number;
  categoryId?: string;
  category?: Category;
}

export interface Question {
  id?: string;
  question_text: string;
  options: QuestionOption[];
  isActive?: boolean;
  createdAt?: string;
  updatedAt?: string;
  createdBy?: string;
}
