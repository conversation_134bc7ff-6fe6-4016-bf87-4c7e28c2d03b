// src/app/core/services/question.service.ts
import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from '../../../environments/environment';
import { Question, QuestionOption } from '../models/question.model';

export interface QuestionFilters {
  page?: number;
  limit?: number;
  search?: string;
  status?: 'all' | 'active' | 'inactive';
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface QuestionResponse {
  questions: Question[];
  totalQuestions: number;
  currentPage: number;
  totalPages: number;
  pageSize: number;
}

@Injectable({
  providedIn: 'root'
})
export class QuestionService {
  private apiUrl = environment.apiUrl;

  constructor(private http: HttpClient) {}

  getQuestions(filters: QuestionFilters = {}): Observable<QuestionResponse> {
    let params = new HttpParams();

    if (filters.page) {
      params = params.set('page', filters.page.toString());
    }
    if (filters.limit) {
      params = params.set('limit', filters.limit.toString());
    }
    if (filters.search) {
      params = params.set('search', filters.search);
    }
    if (filters.status && filters.status !== 'all') {
      params = params.set('status', filters.status);
    }
    if (filters.sortBy) {
      params = params.set('sortBy', filters.sortBy);
    }
    if (filters.sortOrder) {
      params = params.set('sortOrder', filters.sortOrder);
    }

    return this.http.get<QuestionResponse>(`${this.apiUrl}/product-owner/questions`, { params });
  }

  // Keep the old method for backward compatibility
  getAllQuestions(): Observable<Question[]> {
    return this.http.get<Question[]>(`${this.apiUrl}/product-owner/questions`);
  }

  getQuestion(id: string): Observable<Question> {
    return this.http.get<Question>(`${this.apiUrl}/product-owner/questions/${id}`);
  }

  createQuestion(question: Question): Observable<Question> {
    return this.http.post<Question>(`${this.apiUrl}/product-owner/questions`, question);
  }

  updateQuestion(id: string, question: Partial<Question>): Observable<Question> {
    return this.http.put<Question>(`${this.apiUrl}/product-owner/questions/${id}`, question);
  }

  deleteQuestion(id: string): Observable<any> {
    return this.http.delete(`${this.apiUrl}/product-owner/questions/${id}`);
  }

  // Question options
  addOption(questionId: string, option: QuestionOption): Observable<QuestionOption> {
    return this.http.post<QuestionOption>(`${this.apiUrl}/product-owner/questions/${questionId}/options`, option);
  }

  updateOption(questionId: string, optionId: string, option: QuestionOption): Observable<QuestionOption> {
    return this.http.put<QuestionOption>(`${this.apiUrl}/product-owner/questions/${questionId}/options/${optionId}`, option);
  }

  deleteOption(questionId: string, optionId: string): Observable<any> {
    return this.http.delete(`${this.apiUrl}/product-owner/questions/${questionId}/options/${optionId}`);
  }
}