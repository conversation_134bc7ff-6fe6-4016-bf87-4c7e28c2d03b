// src/app/core/services/question.service.ts
import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from '../../../environments/environment';
import { Question, QuestionOption } from '../models/question.model';

@Injectable({
  providedIn: 'root'
})
export class QuestionService {
  private apiUrl = environment.apiUrl;

  constructor(private http: HttpClient) {}

  getQuestions(): Observable<Question[]> {
    return this.http.get<Question[]>(`${this.apiUrl}/product-owner/questions`);
  }

  getQuestion(id: string): Observable<Question> {
    return this.http.get<Question>(`${this.apiUrl}/product-owner/questions/${id}`);
  }

  createQuestion(question: Question): Observable<Question> {
    return this.http.post<Question>(`${this.apiUrl}/product-owner/questions`, question);
  }

  updateQuestion(id: string, question: Partial<Question>): Observable<Question> {
    return this.http.put<Question>(`${this.apiUrl}/product-owner/questions/${id}`, question);
  }

  deleteQuestion(id: string): Observable<any> {
    return this.http.delete(`${this.apiUrl}/product-owner/questions/${id}`);
  }

  // Question options
  addOption(questionId: string, option: QuestionOption): Observable<QuestionOption> {
    return this.http.post<QuestionOption>(`${this.apiUrl}/product-owner/questions/${questionId}/options`, option);
  }

  updateOption(questionId: string, optionId: string, option: QuestionOption): Observable<QuestionOption> {
    return this.http.put<QuestionOption>(`${this.apiUrl}/product-owner/questions/${questionId}/options/${optionId}`, option);
  }

  deleteOption(questionId: string, optionId: string): Observable<any> {
    return this.http.delete(`${this.apiUrl}/product-owner/questions/${questionId}/options/${optionId}`);
  }
}