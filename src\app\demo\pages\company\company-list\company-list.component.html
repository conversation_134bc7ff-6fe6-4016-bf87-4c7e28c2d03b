<div class="companies-container">
  <!-- Header Section -->
  <div class="row mb-4">
    <div class="col-12">
      <div class="page-header">
        <div class="d-flex justify-content-between align-items-center">
          <div>
            <h2 class="page-title">
              <i class="feather icon-briefcase me-2"></i>
              Company Management
            </h2>
            <p class="page-subtitle">Manage your companies and their information</p>
          </div>
          <div class="header-actions">
            <button class="btn btn-outline-secondary me-2" (click)="refreshList()" [disabled]="loading">
              <i class="feather icon-refresh-cw me-2"></i>
              Refresh
            </button>
            <button class="btn btn-primary btn-create" (click)="createCompany()">
              <i class="feather icon-plus me-2"></i>
              Create New Company
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Search and Filter Section -->
  <div *ngIf="!loading && !error" class="row mb-4">
    <div class="col-12">
      <div class="search-filter-card">
        <div class="row align-items-center">
          <div class="col-md-6">
            <div class="search-container">
              <div class="search-input-wrapper">
                <i class="feather icon-search search-icon"></i>
                <input
                  type="text"
                  class="form-control search-input"
                  placeholder="Search companies, contacts, or locations..."
                  [(ngModel)]="searchTerm"
                  (input)="onSearchChange(searchTerm)"
                />
                <button
                  *ngIf="searchTerm"
                  class="btn btn-clear"
                  (click)="clearSearch()"
                  title="Clear search">
                  <i class="feather icon-x"></i>
                </button>
              </div>
            </div>
          </div>
          <div class="col-md-3">
            <div class="filter-container">
              <label class="filter-label">Status Filter</label>
              <select
                class="form-select filter-select"
                [(ngModel)]="statusFilter"
                (change)="onStatusFilterChange(statusFilter)">
                <option value="all">All Companies</option>
                <option value="active">Active Only</option>
                <option value="inactive">Inactive Only</option>
              </select>
            </div>
          </div>
          <div class="col-md-3">
            <div class="search-results">
              <span class="results-text">
                <i class="feather icon-list me-1"></i>
                Showing {{ getStartRange() }} to {{ getEndRange() }} of {{ getTotalCompaniesCount() }} companies
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Loading State -->
  <div *ngIf="loading" class="loading-container">
    <div class="row">
      <div class="col-12">
        <div class="card">
          <div class="card-body text-center py-5">
            <div class="spinner-border text-primary" role="status">
              <span class="visually-hidden">Loading...</span>
            </div>
            <p class="mt-3 mb-0">Loading companies...</p>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Error State -->
  <div *ngIf="error && !loading" class="error-container">
    <div class="row">
      <div class="col-12">
        <div class="alert alert-danger" role="alert">
          <i class="feather icon-alert-circle me-2"></i>
          {{ error }}
          <button class="btn btn-sm btn-outline-danger ms-3" (click)="refreshList()">
            <i class="feather icon-refresh-cw me-1"></i>
            Try Again
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- Companies Table -->
  <div *ngIf="!loading && !error" class="row">
    <div class="col-12">
      <div class="card">
        <div class="card-body">
          <div class="table-responsive">
            <table class="table table-hover">
              <thead>
                <tr>
                  <th>Company Name</th>
                  <th>Contact Person</th>
                  <th>Email</th>
                  <th>Location</th>
                  <th>Status</th>
                  <th>Created Date</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let company of filteredCompanies; trackBy: trackByCompanyId">
                  <td>
                    <div class="company-info">
                      <h6 class="company-name mb-1">{{ company.name }}</h6>
                      <small class="text-muted">ID: {{ company.id.substring(0, 8) }}...</small>
                    </div>
                  </td>
                  <td>
                    <div class="contact-info">
                      <span class="contact-name">{{ getFullContactName(company) }}</span>
                      <br>
                      <small class="text-muted" *ngIf="company.contact_person_phone">
                        <i class="feather icon-phone me-1"></i>
                        {{ company.contact_person_phone }}
                      </small>
                    </div>
                  </td>
                  <td>
                    <a href="mailto:{{ company.contact_person_email }}" class="email-link">
                      {{ company.contact_person_email }}
                    </a>
                  </td>
                  <td>
                    <div class="location-info">
                      <span class="city-state">{{ company.city }}, {{ company.state }}</span>
                      <br>
                      <small class="text-muted">{{ company.country }}</small>
                    </div>
                  </td>
                  <td>
                    <span class="badge" [ngClass]="getStatusBadgeClass(company)">
                      {{ getStatusText(company) }}
                    </span>
                  </td>
                  <td>
                    <span class="created-date">{{ formatDate(company.createdAt) }}</span>
                  </td>
                  <td>
                    <div class="action-buttons">
                      <button
                        class="btn btn-sm btn-outline-primary me-1"
                        (click)="viewCompany(company.id)"
                        title="View Details">
                        <i class="feather icon-eye"></i>
                      </button>
                      <button
                        class="btn btn-sm btn-outline-secondary me-1"
                        (click)="editCompany(company.id)"
                        title="Edit Company">
                        <i class="feather icon-edit"></i>
                      </button>
                      <button
                        class="btn btn-sm btn-outline-danger"
                        (click)="deleteCompany(company.id)"
                        title="Delete Company"
                        [disabled]="company.isDeleted">
                        <i class="feather icon-trash-2"></i>
                      </button>
                    </div>
                  </td>
                </tr>
                <tr *ngIf="filteredCompanies.length === 0">
                  <td colspan="7" class="text-center py-4">
                    <div class="no-data">
                      <i class="feather icon-inbox text-muted mb-2" style="font-size: 2rem;"></i>
                      <p class="text-muted mb-0">
                        {{ searchTerm ? 'No companies found matching your search.' : 'No companies found.' }}
                      </p>
                      <button *ngIf="!searchTerm" class="btn btn-primary mt-2" (click)="createCompany()">
                        <i class="feather icon-plus me-2"></i>
                        Create Your First Company
                      </button>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>

          <!-- Pagination Controls -->
          <div *ngIf="totalPages > 1" class="pagination-container">
            <div class="row align-items-center">
              <div class="col-md-6">
                <div class="page-size-selector">
                  <label class="form-label me-2">Items per page:</label>
                  <select class="form-select form-select-sm d-inline-block w-auto"
                          [(ngModel)]="pageSize"
                          (change)="onPageSizeChange(pageSize)">
                    <option [value]="5">5</option>
                    <option [value]="10">10</option>
                    <option [value]="20">20</option>
                    <option [value]="50">50</option>
                  </select>
                </div>
              </div>
              <div class="col-md-6">
                <nav aria-label="Companies pagination">
                  <ul class="pagination justify-content-end mb-0">
                    <li class="page-item" [class.disabled]="currentPage === 1">
                      <a class="page-link" (click)="onPageChange(currentPage - 1)" [attr.aria-disabled]="currentPage === 1">
                        <i class="feather icon-chevron-left"></i>
                      </a>
                    </li>
                    <li *ngFor="let page of getPaginationArray()"
                        class="page-item"
                        [class.active]="page === currentPage">
                      <a class="page-link" (click)="onPageChange(page)">{{ page }}</a>
                    </li>
                    <li class="page-item" [class.disabled]="currentPage === totalPages">
                      <a class="page-link" (click)="onPageChange(currentPage + 1)" [attr.aria-disabled]="currentPage === totalPages">
                        <i class="feather icon-chevron-right"></i>
                      </a>
                    </li>
                  </ul>
                </nav>
              </div>
            </div>
            <div class="pagination-info text-center mt-3">
              <small class="text-muted">
                Showing {{ getStartRange() }} to {{ getEndRange() }} of {{ getTotalCompaniesCount() }} companies
              </small>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
