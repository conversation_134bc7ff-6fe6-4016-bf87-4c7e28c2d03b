import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from '../../../environments/environment';
import { CreateCompanyDto } from '../models/create-company.dto';

export interface CompanyFilters {
  page?: number;
  limit?: number;
  search?: string;
  status?: 'all' | 'active' | 'inactive';
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

interface Pagination {
  limit: number;
  page: number;
  total: number;
  totalPages: number;
}

export interface CompanyResponse {
  companies: any[];
  pagination: Pagination;
}

@Injectable({
  providedIn: 'root'
})
export class CompanyService {
  private apiUrl = environment.apiUrl;

  constructor(private http: HttpClient) {}

  /**
   * Create a new company
   */
  createCompany(companyData: CreateCompanyDto): Observable<any> {
    return this.http.post<any>(`${this.apiUrl}/product-owner/companies`, companyData);
  }

  /**
   * Get companies with pagination and filters
   */
  getCompanies(filters: CompanyFilters = {}): Observable<CompanyResponse> {
    let params = new HttpParams();

    if (filters.page) {
      params = params.set('page', filters.page.toString());
    }
    if (filters.limit) {
      params = params.set('limit', filters.limit.toString());
    }
    if (filters.search) {
      params = params.set('search', filters.search);
    }
    if (filters.status && filters.status !== 'all') {
      params = params.set('status', filters.status);
    }
    if (filters.sortBy) {
      params = params.set('sortBy', filters.sortBy);
    }
    if (filters.sortOrder) {
      params = params.set('sortOrder', filters.sortOrder);
    }

    return this.http.get<CompanyResponse>(`${this.apiUrl}/product-owner/companies`, { params });
  }

  /**
   * Get all companies (backward compatibility)
   */
  getAllCompanies(): Observable<any[]> {
    return this.http.get<any[]>(`${this.apiUrl}/product-owner/companies`);
  }

  /**
   * Get a specific company by ID
   */
  getCompanyById(id: string): Observable<any> {
    return this.http.get<any>(`${this.apiUrl}/product-owner/companies/${id}`);
  }

  /**
   * Update a company
   */
  updateCompany(id: string, companyData: Partial<CreateCompanyDto>): Observable<any> {
    return this.http.put<any>(`${this.apiUrl}/product-owner/companies/${id}`, companyData);
  }

  /**
   * Delete a company
   */
  deleteCompany(id: string): Observable<any> {
    return this.http.delete(`${this.apiUrl}/product-owner/companies/${id}`);
  }
}
