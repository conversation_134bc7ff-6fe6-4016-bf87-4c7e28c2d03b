import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from '../../../environments/environment';
import { CreateCompanyDto } from '../models/create-company.dto';

@Injectable({
  providedIn: 'root'
})
export class CompanyService {
  private apiUrl = environment.apiUrl;

  constructor(private http: HttpClient) {}

  /**
   * Create a new company
   */
  createCompany(companyData: CreateCompanyDto): Observable<any> {
    return this.http.post<any>(`${this.apiUrl}/product-owner/companies`, companyData);
  }

  /**
   * Get all companies
   */
  getCompanies(): Observable<any[]> {
    return this.http.get<any[]>(`${this.apiUrl}/product-owner/companies`);
  }

  /**
   * Get a specific company by ID
   */
  getCompanyById(id: string): Observable<any> {
    return this.http.get<any>(`${this.apiUrl}/product-owner/companies/${id}`);
  }

  /**
   * Update a company
   */
  updateCompany(id: string, companyData: Partial<CreateCompanyDto>): Observable<any> {
    return this.http.put<any>(`${this.apiUrl}/product-owner/companies/${id}`, companyData);
  }

  /**
   * Delete a company
   */
  deleteCompany(id: string): Observable<any> {
    return this.http.delete(`${this.apiUrl}/product-owner/companies/${id}`);
  }
}
