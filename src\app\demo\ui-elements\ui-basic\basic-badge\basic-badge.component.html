<div class="row">
  <div class="col-sm-12">
    <app-card cardTitle="Basic Badges" [options]="false">
      <h1>
        Example heading
        <span class="badge bg-secondary">New</span>
      </h1>
      <h2>
        Example heading
        <span class="badge bg-secondary">New</span>
      </h2>
      <h3>
        Example heading
        <span class="badge bg-secondary">New</span>
      </h3>
      <h4>
        Example heading
        <span class="badge bg-secondary">New</span>
      </h4>
      <h5>
        Example heading
        <span class="badge bg-secondary">New</span>
      </h5>
      <h6>
        Example heading
        <span class="badge bg-secondary">New</span>
      </h6>
    </app-card>
  </div>
  <div class="col-sm-12">
    <app-card cardTitle="Button Badges" [options]="false">
      <button type="button" class="btn btn-primary">
        primary
        <span class="badge bg-light text-dark ms-2">4</span>
      </button>
      <button type="button" class="btn btn-secondary">
        secondary
        <span class="badge bg-light text-dark ms-2">4</span>
      </button>
      <button type="button" class="btn btn-success">
        success
        <span class="badge bg-light text-dark ms-2">4</span>
      </button>
      <button type="button" class="btn btn-danger">
        danger
        <span class="badge bg-light text-dark ms-2">4</span>
      </button>
      <button type="button" class="btn btn-warning">
        warning
        <span class="badge bg-light text-dark ms-2">4</span>
      </button>
      <button type="button" class="btn btn-info">
        info
        <span class="badge bg-light text-dark ms-2">4</span>
      </button>
    </app-card>
  </div>
  <div class="col-sm-12">
    <app-card cardTitle="Contextual Variations" [options]="false">
      <span class="badge m-r-5 bg-primary">Primary</span>
      <span class="badge m-r-5 bg-secondary">Secondary</span>
      <span class="badge m-r-5 bg-success">Success</span>
      <span class="badge m-r-5 bg-danger">Danger</span>
      <span class="badge m-r-5 bg-warning">Warning</span>
      <span class="badge m-r-5 bg-info">Info</span>
      <span class="badge m-r-5 bg-light text-dark">Light</span>
      <span class="badge m-r-5 bg-dark">Dark</span>
    </app-card>
  </div>
  <div class="col-sm-12">
    <app-card cardTitle="Pill Badges" [options]="false">
      <span class="badge m-r-5 badge-pill bg-primary">Primary</span>
      <span class="badge m-r-5 badge-pill bg-secondary">Secondary</span>
      <span class="badge m-r-5 badge-pill bg-success">Success</span>
      <span class="badge m-r-5 badge-pill bg-danger">Danger</span>
      <span class="badge m-r-5 badge-pill bg-warning">Warning</span>
      <span class="badge m-r-5 badge-pill bg-info">Info</span>
      <span class="badge m-r-5 badge-pill bg-light text-dark">Light</span>
      <span class="badge m-r-5 badge-pill bg-dark">Dark</span>
    </app-card>
  </div>
  <div class="col-sm-12">
    <app-card cardTitle="Links" [options]="false">
      <a href="javascript:" class="badge m-r-5 bg-primary">Primary</a>
      <a href="javascript:" class="badge m-r-5 bg-secondary">Secondary</a>
      <a href="javascript:" class="badge m-r-5 bg-success">Success</a>
      <a href="javascript:" class="badge m-r-5 bg-danger">Danger</a>
      <a href="javascript:" class="badge m-r-5 bg-warning">Warning</a>
      <a href="javascript:" class="badge m-r-5 bg-info">Info</a>
      <a href="javascript:" class="badge m-r-5 bg-light text-dark">Light</a>
      <a href="javascript:" class="badge m-r-5 bg-dark">Dark</a>
    </app-card>
  </div>
</div>
