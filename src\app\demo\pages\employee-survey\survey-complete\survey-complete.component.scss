// Survey Complete Styling
.survey-complete {
  padding: 1rem 0;
  text-align: center;
}

// Success Header
.success-header {
  margin-bottom: 2rem;

  .success-icon {
    margin-bottom: 1rem;

    i {
      font-size: 4rem;
      color: #00b894;
      animation: bounceIn 0.6s ease;
    }
  }

  .success-title {
    font-size: 2rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 0.5rem;
    animation: fadeInUp 0.6s ease 0.2s both;
  }

  .success-subtitle {
    font-size: 1.1rem;
    color: #7f8c8d;
    margin: 0;
    animation: fadeInUp 0.6s ease 0.4s both;
  }
}

// Employee Card
.employee-card {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  margin-bottom: 2rem;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  animation: fadeInUp 0.6s ease 0.6s both;

  .employee-info {
    .employee-name {
      font-size: 1.5rem;
      font-weight: 600;
      color: #2c3e50;
      margin-bottom: 1rem;
    }

    .completion-details {
      display: grid;
      gap: 0.75rem;

      .detail-item {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.5rem;
        font-size: 0.9rem;

        i {
          color: #667eea;
          font-size: 1rem;
        }

        .detail-label {
          color: #7f8c8d;
        }

        .detail-value {
          color: #2c3e50;
          font-weight: 600;
        }
      }
    }
  }
}

// Score Card
.score-card {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  margin-bottom: 2rem;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  animation: fadeInUp 0.6s ease 0.8s both;

  .score-header {
    margin-bottom: 1.5rem;

    h3 {
      color: #2c3e50;
      margin: 0;
      font-size: 1.2rem;
      font-weight: 600;
    }
  }

  .score-display {
    .score-circle {
      width: 6rem;
      height: 6rem;
      border: 4px solid;
      border-radius: 50%;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      margin: 0 auto 1rem;
      position: relative;

      .score-value {
        font-size: 1.8rem;
        font-weight: 700;
        line-height: 1;
      }

      .score-max {
        font-size: 1rem;
        color: #7f8c8d;
        line-height: 1;
      }
    }

    .score-label {
      font-size: 1.1rem;
      font-weight: 600;
    }
  }
}

// Details Section
.details-section {
  margin-bottom: 2rem;
  animation: fadeInUp 0.6s ease 1s both;

  .details-toggle {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin: 0 auto 1.5rem;

    &:hover {
      background: linear-gradient(135deg, #764ba2 0%, #667eea 100%);
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
    }

    i {
      font-size: 1rem;
    }
  }

  .answer-summary {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    animation: slideDown 0.3s ease;

    .answer-item {
      border-bottom: 1px solid #e0e0e0;
      padding: 1rem 0;
      text-align: left;

      &:last-child {
        border-bottom: none;
        padding-bottom: 0;
      }

      .answer-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 0.75rem;

        .question-number {
          background: #f8f9fa;
          color: #495057;
          padding: 0.25rem 0.75rem;
          border-radius: 12px;
          font-size: 0.8rem;
          font-weight: 600;
        }

        .answer-score {
          color: white;
          padding: 0.25rem 0.75rem;
          border-radius: 12px;
          font-size: 0.8rem;
          font-weight: 600;
        }
      }

      .answer-content {
        .question-text {
          font-size: 0.95rem;
          color: #2c3e50;
          font-weight: 600;
          margin-bottom: 0.5rem;
          line-height: 1.3;
        }

        .answer-text {
          font-size: 0.9rem;
          color: #7f8c8d;
          margin: 0;
          line-height: 1.3;
        }
      }
    }
  }
}

// Action Section
.action-section {
  margin-bottom: 2rem;
  animation: fadeInUp 0.6s ease 1.2s both;

  .action-button {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    padding: 1rem 2rem;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    margin: 0 auto 1rem;
    border: none;
    font-size: 1rem;
    width: 100%;
    max-width: 250px;

    &.primary {
      background: linear-gradient(135deg, #00b894 0%, #00a085 100%);
      color: white;

      &:hover {
        background: linear-gradient(135deg, #00a085 0%, #00b894 100%);
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(0, 184, 148, 0.3);
      }
    }

    &.secondary {
      background: transparent;
      border: 2px solid #667eea;
      color: #667eea;

      &:hover {
        background: #667eea;
        color: white;
      }
    }

    i {
      font-size: 1rem;
    }
  }
}

// Thank You Section
.thank-you-section {
  animation: fadeInUp 0.6s ease 1.4s both;

  .thank-you-card {
    background: linear-gradient(135deg, #f8f9ff 0%, #e8ecff 100%);
    border: 2px solid #667eea;
    border-radius: 12px;
    padding: 2rem;

    .thank-you-icon {
      font-size: 2rem;
      color: #e74c3c;
      margin-bottom: 1rem;
      animation: heartbeat 1.5s ease-in-out infinite;
    }

    h4 {
      color: #2c3e50;
      margin-bottom: 1rem;
      font-size: 1.3rem;
      font-weight: 600;
    }

    p {
      color: #7f8c8d;
      margin: 0;
      line-height: 1.5;
      font-size: 0.95rem;
    }
  }
}

// Error State
.error-state {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;

  .error-card {
    background: white;
    border-radius: 12px;
    padding: 2rem;
    text-align: center;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);

    .error-icon {
      font-size: 3rem;
      color: #e17055;
      margin-bottom: 1rem;
    }

    h3 {
      color: #2c3e50;
      margin-bottom: 1rem;
    }

    p {
      color: #7f8c8d;
      margin-bottom: 2rem;
    }
  }
}

// Animations
@keyframes bounceIn {
  0% {
    opacity: 0;
    transform: scale(0.3);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes heartbeat {
  0% {
    transform: scale(1);
  }
  14% {
    transform: scale(1.1);
  }
  28% {
    transform: scale(1);
  }
  42% {
    transform: scale(1.1);
  }
  70% {
    transform: scale(1);
  }
}

// Responsive Design
@media (max-width: 480px) {
  .success-header .success-title {
    font-size: 1.7rem;
  }

  .employee-card .employee-info .completion-details {
    gap: 0.5rem;

    .detail-item {
      font-size: 0.85rem;
    }
  }

  .score-card .score-display .score-circle {
    width: 5rem;
    height: 5rem;

    .score-value {
      font-size: 1.5rem;
    }
  }

  .action-section .action-button {
    padding: 0.875rem 1.5rem;
    font-size: 0.95rem;
  }

  .auto-redirect-section .countdown-card {
    padding: 1rem;
  }

  .auto-redirect-section .countdown-content {
    flex-direction: column;
    text-align: center;
    gap: 0.5rem;

    .countdown-icon {
      font-size: 1.5rem;
    }

    .countdown-text h4 {
      font-size: 1rem;
    }

    .countdown-text p {
      font-size: 0.85rem;
    }
  }
}

// Auto-redirect Section
.auto-redirect-section {
  margin-bottom: 2rem;
  animation: fadeInUp 0.6s ease 1.2s both;

  .countdown-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 12px;
    padding: 1.5rem;
    color: white;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
    position: relative;
    overflow: hidden;

    .countdown-content {
      display: flex;
      align-items: center;
      gap: 1rem;
      margin-bottom: 1rem;

      .countdown-icon {
        font-size: 2rem;
        opacity: 0.9;
        flex-shrink: 0;
      }

      .countdown-text {
        flex: 1;
        text-align: left;

        h4 {
          font-size: 1.2rem;
          font-weight: 600;
          margin: 0 0 0.25rem 0;
        }

        p {
          font-size: 1rem;
          margin: 0;
          opacity: 0.9;

          .countdown-number {
            font-weight: 700;
            font-size: 1.2rem;
            color: #ffeaa7;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
          }
        }
      }
    }

    .countdown-progress {
      height: 4px;
      background: rgba(255, 255, 255, 0.2);
      border-radius: 2px;
      overflow: hidden;

      .progress-bar {
        height: 100%;
        background: linear-gradient(90deg, #00b894 0%, #55efc4 100%);
        border-radius: 2px;
        transition: width 1s linear;
        box-shadow: 0 0 10px rgba(0, 184, 148, 0.5);
      }
    }
  }
}
