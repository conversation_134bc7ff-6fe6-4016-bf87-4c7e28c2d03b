// Company List Component Styles

.companies-container {
  padding: 20px;
  background-color: #f8f9fa;
  min-height: 100vh;
}

// Page Header
.page-header {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
  border: 1px solid #e9ecef;
  margin-bottom: 0;

  .page-title {
    font-size: 1.75rem;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;

    i {
      color: #3498db;
      font-size: 1.5rem;
    }
  }

  .page-subtitle {
    color: #6c757d;
    font-size: 0.95rem;
    margin-bottom: 0;
  }

  .header-actions {
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }

  .btn-create {
    background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
    border: none;
    border-radius: 8px;
    padding: 0.75rem 1.5rem;
    font-weight: 500;
    box-shadow: 0 2px 8px rgba(52, 152, 219, 0.3);
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-1px);
      box-shadow: 0 4px 15px rgba(52, 152, 219, 0.4);
    }
  }
}

// Search and Filter Card
.search-filter-card {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
  border: 1px solid #e9ecef;

  .search-container {
    .search-input-wrapper {
      position: relative;
      display: flex;
      align-items: center;

      .search-icon {
        position: absolute;
        left: 12px;
        color: #6c757d;
        z-index: 2;
      }

      .search-input {
        padding-left: 40px;
        padding-right: 40px;
        border: 2px solid #e9ecef;
        border-radius: 8px;
        font-size: 0.95rem;
        transition: all 0.3s ease;

        &:focus {
          border-color: #3498db;
          box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
        }
      }

      .btn-clear {
        position: absolute;
        right: 8px;
        background: none;
        border: none;
        color: #6c757d;
        padding: 4px;
        border-radius: 4px;
        z-index: 2;

        &:hover {
          color: #dc3545;
          background-color: #f8f9fa;
        }
      }
    }
  }

  .filter-container {
    .filter-label {
      font-size: 0.85rem;
      font-weight: 500;
      color: #495057;
      margin-bottom: 0.5rem;
      display: block;
    }

    .filter-select {
      border: 2px solid #e9ecef;
      border-radius: 8px;
      font-size: 0.95rem;
      transition: all 0.3s ease;

      &:focus {
        border-color: #3498db;
        box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
      }
    }
  }

  .search-results {
    display: flex;
    justify-content: flex-end;
    align-items: flex-end;
    height: 100%;

    .results-text {
      background: linear-gradient(135deg, #e8f4fd 0%, #d6eaf8 100%);
      color: #2980b9;
      padding: 0.5rem 1rem;
      border-radius: 20px;
      font-size: 0.85rem;
      font-weight: 500;
      border: 1px solid #bde0ff;
    }
  }
}

// Loading Container
.loading-container {
  .card {
    border: none;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  }

  .spinner-border {
    width: 3rem;
    height: 3rem;
  }
}

// Error Container
.error-container {
  .alert {
    border-radius: 12px;
    border: none;
    box-shadow: 0 2px 10px rgba(220, 53, 69, 0.15);
  }
}

// Companies Table
.card {
  border: none;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
  border: 1px solid #e9ecef;

  .card-body {
    padding: 0;
  }
}

.table {
  margin-bottom: 0;

  thead {
    th {
      background-color: #f8f9fa;
      border-bottom: 2px solid #e9ecef;
      font-weight: 600;
      color: #495057;
      padding: 1rem;
      font-size: 0.9rem;
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }
  }

  tbody {
    tr {
      transition: all 0.2s ease;

      &:hover {
        background-color: #f8f9fa;
      }

      td {
        padding: 1rem;
        vertical-align: middle;
        border-bottom: 1px solid #e9ecef;
      }
    }
  }
}

// Table Content Styles
.company-info {
  .company-name {
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 0.25rem;
  }
}

.contact-info {
  .contact-name {
    font-weight: 500;
    color: #495057;
  }
}

.email-link {
  color: #3498db;
  text-decoration: none;
  font-weight: 500;

  &:hover {
    color: #2980b9;
    text-decoration: underline;
  }
}

.location-info {
  .city-state {
    font-weight: 500;
    color: #495057;
  }
}

// Status Badges
.badge {
  font-size: 0.75rem;
  font-weight: 500;
  padding: 0.5rem 0.75rem;
  border-radius: 20px;

  &.badge-success {
    background-color: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
  }

  &.badge-warning {
    background-color: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
  }

  &.badge-danger {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
  }
}

// Action Buttons
.action-buttons {
  display: flex;
  gap: 0.25rem;

  .btn {
    border-radius: 6px;
    padding: 0.375rem 0.5rem;
    font-size: 0.875rem;
    transition: all 0.2s ease;

    &:hover {
      transform: translateY(-1px);
    }
  }
}

// No Data State
.no-data {
  padding: 2rem;
  text-align: center;

  i {
    display: block;
    margin: 0 auto 1rem;
  }

  p {
    font-size: 1rem;
    margin-bottom: 1rem;
  }
}

// Responsive Design
@media (max-width: 768px) {
  .companies-container {
    padding: 10px;
  }

  .page-header {
    padding: 1.5rem;

    .page-title {
      font-size: 1.5rem;
    }

    .d-flex {
      flex-direction: column;
      align-items: flex-start !important;
      gap: 1rem;
    }

    .header-actions {
      width: 100%;
      justify-content: flex-start;

      .btn-create {
        flex: 1;
        justify-content: center;
      }
    }
  }

  .search-filter-card {
    padding: 1rem;

    .row {
      row-gap: 1rem;
    }

    .search-results {
      justify-content: flex-start;

      .results-text {
        font-size: 0.8rem;
        padding: 0.4rem 0.8rem;
      }
    }
  }

  .table-responsive {
    font-size: 0.875rem;
  }

  .action-buttons {
    flex-direction: column;
    gap: 0.25rem;

    .btn {
      width: 100%;
      justify-content: center;
    }
  }

  .pagination-container {
    padding: 1rem;

    .row {
      flex-direction: column;
      gap: 1rem;
    }

    .page-size-selector {
      justify-content: center;
    }

    .pagination {
      justify-content: center !important;

      .page-item .page-link {
        padding: 0.4rem 0.6rem;
        min-width: 35px;
        height: 35px;
        font-size: 0.85rem;
      }
    }

    .pagination-info {
      text-align: center;

      small {
        font-size: 0.8rem;
        padding: 0.4rem 0.8rem;
      }
    }
  }
}

// Animation for smooth transitions
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.card {
  animation: fadeInUp 0.3s ease-out;
}

// Pagination Styles
.pagination-container {
  padding: 1.5rem;
  border-top: 1px solid #e9ecef;
  background-color: #f8f9fa;

  .page-size-selector {
    display: flex;
    align-items: center;
    gap: 0.5rem;

    .form-label {
      font-weight: 600;
      color: #495057;
      margin-bottom: 0;
      font-size: 0.9rem;
    }

    .form-select {
      border: 2px solid #e9ecef;
      border-radius: 8px;
      font-size: 0.9rem;
      transition: all 0.3s ease;
      cursor: pointer;

      &:focus {
        border-color: #3498db;
        box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
        outline: none;
      }
    }
  }

  .pagination {
    margin-bottom: 0;

    .page-item {
      .page-link {
        border: none;
        color: #6c757d;
        padding: 0.5rem 0.75rem;
        margin: 0 0.125rem;
        border-radius: 8px;
        transition: all 0.2s ease;
        font-weight: 500;
        display: flex;
        align-items: center;
        justify-content: center;
        min-width: 40px;
        height: 40px;

        &:hover {
          background-color: #3498db;
          color: white;
          transform: translateY(-1px);
          box-shadow: 0 2px 8px rgba(52, 152, 219, 0.3);
        }

        &:focus {
          box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
        }

        i {
          font-size: 0.9rem;
        }
      }

      &.active .page-link {
        background-color: #3498db;
        color: white;
        box-shadow: 0 2px 8px rgba(52, 152, 219, 0.4);
        transform: translateY(-1px);

        &:hover {
          background-color: #2980b9;
        }
      }

      &.disabled .page-link {
        color: #adb5bd;
        cursor: not-allowed;
        background: #f8f9fa;

        &:hover {
          background: #f8f9fa;
          color: #adb5bd;
          transform: none;
          box-shadow: none;
        }
      }
    }
  }

  .pagination-info {
    color: #6c757d;
    font-size: 0.875rem;
    font-weight: 500;

    small {
      background: rgba(52, 152, 219, 0.1);
      padding: 0.5rem 1rem;
      border-radius: 20px;
      color: #3498db;
      font-weight: 600;
    }
  }
}

// Custom scrollbar for better UX
.companies-container {
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;

    &:hover {
      background: #a8a8a8;
    }
  }
}
