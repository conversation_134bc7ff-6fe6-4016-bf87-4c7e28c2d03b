{"name": "datta-able-free-angular-admin-template", "version": "6.1.0", "author": "CodedThemes", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "build-prod": "ng build --configuration production --base-href /demos/admin-templates/datta-able/angular/free/", "watch": "ng build --watch --configuration development", "test": "ng test", "lint": "ng lint", "lint:fix": "ng lint --fix", "prettier": "prettier --write ./src"}, "private": false, "dependencies": {"@angular/animations": "20.0.0-next.8", "@angular/cdk": "19.2.17", "@angular/common": "20.0.0-next.8", "@angular/compiler": "20.0.0-next.8", "@angular/core": "20.0.0-next.8", "@angular/forms": "20.0.0-next.8", "@angular/localize": "20.0.0-next.8", "@angular/platform-browser": "20.0.0-next.8", "@angular/platform-browser-dynamic": "20.0.0-next.8", "@angular/router": "20.0.0-next.8", "@ng-bootstrap/ng-bootstrap": "18.0.0", "@popperjs/core": "2.11.8", "apexcharts": "4.7.0", "bootstrap": "5.3.6", "ng-apexcharts": "1.15.0", "ngx-scrollbar": "18.0.0", "rxjs": "~7.8.2", "screenfull": "6.0.2", "tslib": "2.8.1", "zone.js": "~0.15.0"}, "devDependencies": {"@angular-devkit/build-angular": "20.0.0-next.8", "@angular/cli": "20.0.0-next.8", "@angular/compiler-cli": "20.0.0-next.8", "@eslint/eslintrc": "3.3.1", "@eslint/js": "9.27.0", "@types/jasmine": "~5.1.8", "@types/lodash": "4.17.17", "@types/node": "22.15.21", "angular-eslint": "19.4.0", "eslint": "^9.27.0", "prettier": "3.5.3", "typescript": "5.8.3", "typescript-eslint": "8.32.1"}, "packageManager": "yarn@1.22.22+sha512.a6b2f7906b721bba3d67d4aff083df04dad64c399707841b7acf00f6b133b7ac24255f2652fa22ae3534329dc6180534e98d17432037ff6fd140556e2bb3137e"}