// ============================
//    5. footer css start
// ============================

.pc-footer {
  position: relative;
  z-index: 995;
  margin-left: $Menu-width;
  padding: 15px 0;

  a {
    color: var(--bs-body-color);

    &:hover {
      color: var(--bs-primary);
    }
  }

  .footer-wrapper {
    padding-left: 30px;
    padding-right: 30px;
  }

  .footer-link {
    .list-inline-item:not(:last-child) {
      margin-right: 0.9rem;
    }
  }

  @media (max-width: 1024px) {
    margin-left: 0;
  }
}

app-footer.navbar-collapsed {
  .pc-footer {
    margin-left: 80px;
  }
}
// ============================
//    5. footer css end
// ============================
