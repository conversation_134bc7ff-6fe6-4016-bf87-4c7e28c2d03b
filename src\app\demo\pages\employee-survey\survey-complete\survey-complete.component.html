<!-- Survey Complete Interface -->
<div class="survey-complete" *ngIf="surveySession">
  <!-- Success Header -->
  <div class="success-header">
    <div class="success-icon">
      <i class="feather icon-check-circle"></i>
    </div>
    <h1 class="success-title">Survey Complete!</h1>
    <p class="success-subtitle">Thank you for your valuable feedback</p>
  </div>

  <!-- Employee Info -->
  <div class="employee-card">
    <div class="employee-info">
      <h2 class="employee-name">{{ getEmployeeName() }}</h2>
      <div class="completion-details">
        <div class="detail-item">
          <i class="feather icon-clock"></i>
          <span class="detail-label">Completed at:</span>
          <span class="detail-value">{{ getCompletionTime() }}</span>
        </div>
        <div class="detail-item">
          <i class="feather icon-timer"></i>
          <span class="detail-label">Duration:</span>
          <span class="detail-value">{{ getDuration() }}</span>
        </div>
        <div class="detail-item">
          <i class="feather icon-help-circle"></i>
          <span class="detail-label">Questions:</span>
          <span class="detail-value">{{ surveySession.questions.length }}</span>
        </div>
      </div>
    </div>
  </div>

  <!-- Score Summary -->
  <!-- <div class="score-card">
    <div class="score-header">
      <h3>Your Average Score</h3>
    </div>
    <div class="score-display">
      <div class="score-circle" [style.border-color]="getScoreColor()">
        <span class="score-value" [style.color]="getScoreColor()">{{ getAverageScore() }}</span>
        <span class="score-max">/5</span>
      </div>
      <div class="score-label" [style.color]="getScoreColor()">
        {{ getScoreLabel() }}
      </div>
    </div>
  </div> -->

  <!-- Answer Details Toggle -->
  <div class="details-section">
    <!-- <button class="details-toggle" (click)="toggleDetails()">
      <i class="feather" [class.icon-chevron-down]="!showDetails" [class.icon-chevron-up]="showDetails"></i>
      {{ showDetails ? 'Hide' : 'View' }} Answer Details
    </button> -->

    <!-- Answer Summary -->
    <!-- <div class="answer-summary" *ngIf="showDetails">
      <div class="answer-item" *ngFor="let item of getAnswerSummary(); let i = index">
        <div class="answer-header">
          <span class="question-number">Q{{ i + 1 }}</span>
          <span class="answer-score" [style.background-color]="getScoreColor()">{{ item.value }}</span>
        </div>
        <div class="answer-content">
          <p class="question-text">{{ item.question }}</p>
          <p class="answer-text">{{ item.answer }}</p>
        </div>
      </div>
    </div> -->
  </div>

  <!-- Auto-redirect Notice -->
  <div class="auto-redirect-section">
    <div class="countdown-card">
      <div class="countdown-content">
        <i class="feather icon-clock countdown-icon"></i>
        <div class="countdown-text">
          <h4>Starting New Survey</h4>
          <p>Automatically redirecting in <span class="countdown-number">{{ countdown }}</span> seconds</p>
        </div>
      </div>
      <div class="countdown-progress">
        <div class="progress-bar" [style.width.%]="((5 - countdown) / 5) * 100"></div>
      </div>
    </div>
  </div>

  <!-- Action Buttons -->
  <!-- <div class="action-section">
    <button class="action-button primary" (click)="startNewSurvey()">
      <i class="feather icon-refresh-cw"></i>
      Take Another Survey Now
    </button>
  </div> -->

  <!-- Thank You Message -->
  <div class="thank-you-section">
    <div class="thank-you-card">
      <i class="feather icon-heart thank-you-icon"></i>
      <h4>Thank You!</h4>
      <p>Your feedback helps us improve the workplace experience for everyone. We appreciate your time and honest responses.</p>
    </div>
  </div>
</div>

<!-- Error State -->
<div class="error-state" *ngIf="!surveySession">
  <div class="error-card">
    <i class="feather icon-alert-circle error-icon"></i>
    <h3>No Survey Data Found</h3>
    <p>It looks like you haven't completed a survey yet.</p>
    <button class="action-button primary" (click)="startNewSurvey()">
      <i class="feather icon-play-circle"></i>
      Start Survey
    </button>
  </div>
</div>
