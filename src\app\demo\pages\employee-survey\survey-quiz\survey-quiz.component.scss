// Survey Quiz Styling
.survey-quiz {
  padding: 1rem 0;
}

// Progress Header
.progress-header {
  margin-bottom: 2rem;

  .employee-info {
    text-align: center;
    margin-bottom: 1rem;

    .employee-name {
      font-size: 1.3rem;
      font-weight: 600;
      color: #2c3e50;
      margin: 0;
    }
  }

  .progress-info {
    text-align: center;

    .progress-bar {
      width: 100%;
      height: 6px;
      background: #e0e0e0;
      border-radius: 3px;
      overflow: hidden;
      margin-bottom: 0.5rem;

      .progress-fill {
        height: 100%;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 3px;
        transition: width 0.5s ease;
      }
    }

    .progress-text {
      font-size: 0.9rem;
      color: #7f8c8d;
      font-weight: 500;
    }
  }
}

// Question Section
.question-section {
  margin-bottom: 2rem;

  .question-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 1rem;

    .question-number {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      padding: 0.5rem 1rem;
      border-radius: 20px;
      font-size: 0.9rem;
      font-weight: 600;
      display: inline-block;
    }

    .header-actions {
      .next-button {
        background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
        color: white;
        border: none;
        padding: 0.6rem 1.2rem;
        border-radius: 25px;
        font-weight: 600;
        font-size: 0.9rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
        cursor: pointer;
        transition: all 0.3s ease;
        box-shadow: 0 3px 12px rgba(17, 153, 142, 0.3);

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 5px 20px rgba(17, 153, 142, 0.4);
        }

        &:active {
          transform: translateY(0);
        }

        i {
          font-size: 1rem;
        }
      }
    }
  }

  .question-content {
    .question-text {
      font-size: 1.2rem;
      font-weight: 600;
      color: #2c3e50;
      text-align: center;
      line-height: 1.4;
      margin: 0;
    }
  }
}

// Options Section
.options-section {
  margin-bottom: 2rem;

  .options-grid {
    display: grid;
    gap: 0.75rem;
    grid-template-columns: 1fr;

    &.compact {
      gap: 0.5rem;
    }
  }

  .option-button {
    background: white;
    border: 2px solid #e0e0e0;
    border-radius: 12px;
    padding: 0.75rem;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: left;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    &.compact {
      padding: 0.6rem;
      border-radius: 10px;
    }

    &:hover:not(:disabled) {
      border-color: #667eea;
      transform: translateY(-2px);
      box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
    }

    &.selected {
      border-color: #667eea;
      background: linear-gradient(135deg, #f8f9ff 0%, #e8ecff 100%);
      box-shadow: 0 4px 15px rgba(102, 126, 234, 0.2);
    }

    &:disabled {
      opacity: 0.6;
      cursor: not-allowed;
    }

    .option-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 0.5rem;

      .option-letter {
        background: #f8f9fa;
        color: #495057;
        width: 1.75rem;
        height: 1.75rem;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 600;
        font-size: 0.85rem;
        transition: all 0.3s ease;
      }

      .option-indicator {
        width: 1.25rem;
        height: 1.25rem;
        border: 2px solid #e0e0e0;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.3s ease;

        &.active {
          background: #667eea;
          border-color: #667eea;
          color: white;
        }

        i {
          font-size: 0.7rem;
        }
      }
    }

    .option-content {
      .option-text {
        font-size: 0.9rem;
        color: #2c3e50;
        font-weight: 500;
        line-height: 1.3;
      }
    }

    &.selected .option-header .option-letter {
      background: #667eea;
      color: white;
    }
  }
}

// Selected Answer Section
.selected-answer-section {
  margin-bottom: 1.5rem;

  .selected-answer-card {
    background: white;
    border: 2px solid #00b894;
    border-radius: 12px;
    padding: 1rem;
    box-shadow: 0 4px 15px rgba(0, 184, 148, 0.2);
    animation: slideInUp 0.3s ease;

    .selected-answer-content {
      display: flex;
      align-items: center;
      gap: 1rem;

      .answer-icon {
        font-size: 1.5rem;
        color: #00b894;
        flex-shrink: 0;
      }

      .answer-info {
        flex: 1;

        .answer-label {
          display: block;
          font-size: 0.85rem;
          color: #6c757d;
          margin-bottom: 0.25rem;
        }

        .answer-text {
          font-size: 1rem;
          color: #2c3e50;
          font-weight: 600;
        }
      }
    }
  }
}

// Confirmation Section (Legacy)
.confirmation-section {
  margin-bottom: 2rem;

  .confirmation-card {
    background: white;
    border: 2px solid #00b894;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 4px 15px rgba(0, 184, 148, 0.2);
    animation: slideInUp 0.3s ease;

    .confirmation-header {
      text-align: center;
      margin-bottom: 1rem;

      .confirmation-icon {
        font-size: 2rem;
        color: #00b894;
        margin-bottom: 0.5rem;
        display: block;
      }

      h4 {
        color: #2c3e50;
        margin: 0;
        font-size: 1.1rem;
        font-weight: 600;
      }
    }

    .selected-answer {
      background: #f8fff9;
      border-radius: 8px;
      padding: 1rem;
      margin-bottom: 1.5rem;
      text-align: center;

      .answer-label {
        display: block;
        font-size: 0.9rem;
        color: #7f8c8d;
        margin-bottom: 0.5rem;
      }

      .answer-text {
        font-size: 1rem;
        font-weight: 600;
        color: #00b894;
      }
    }

    .confirmation-actions {
      display: flex;
      gap: 1rem;
      justify-content: center;

      .btn {
        padding: 0.75rem 1.5rem;
        border-radius: 8px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        gap: 0.5rem;
        font-size: 0.9rem;
        border: none;

        &.btn-outline {
          background: transparent;
          border: 2px solid #7f8c8d;
          color: #7f8c8d;

          &:hover {
            background: #7f8c8d;
            color: white;
          }
        }

        &.btn-primary {
          background: linear-gradient(135deg, #00b894 0%, #00a085 100%);
          color: white;

          &:hover {
            background: linear-gradient(135deg, #00a085 0%, #00b894 100%);
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 184, 148, 0.3);
          }
        }

        i {
          font-size: 0.9rem;
        }
      }
    }
  }
}

// Submitting Section
.submitting-section {
  margin-bottom: 2rem;

  .submitting-card {
    background: white;
    border-radius: 12px;
    padding: 2rem;
    text-align: center;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);

    .submitting-text {
      color: #7f8c8d;
      margin: 1rem 0 0;
      font-size: 1rem;
    }
  }
}

// Instructions
.instructions {
  text-align: center;

  .instruction-text {
    color: #7f8c8d;
    font-size: 0.9rem;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;

    .instruction-icon {
      font-size: 1rem;
      color: #667eea;
    }
  }
}

// Loading State
.loading-state {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 300px;

  .loading-card {
    background: white;
    border-radius: 12px;
    padding: 2rem;
    text-align: center;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);

    .loading-text {
      color: #7f8c8d;
      margin: 1rem 0 0;
      font-size: 1rem;
    }
  }
}

// Spinner Animation
.spinner {
  width: 2rem;
  height: 2rem;
  border: 3px solid #e0e0e0;
  border-top: 3px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// Responsive Design
@media (max-width: 480px) {
  .confirmation-section .confirmation-card .confirmation-actions {
    flex-direction: column;
    gap: 0.75rem;

    .btn {
      width: 100%;
      justify-content: center;
    }
  }

  .question-section .question-content .question-text {
    font-size: 1.1rem;
  }

  .options-section .option-button {
    padding: 0.75rem;

    .option-content .option-text {
      font-size: 0.95rem;
    }
  }
}
