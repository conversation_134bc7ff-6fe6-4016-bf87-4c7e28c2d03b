import { Component, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Subscription } from 'rxjs';
import { PwaService } from '../../../core/services/pwa.service';

@Component({
  selector: 'app-pwa-install',
  standalone: true,
  imports: [CommonModule],
  template: `
    <!-- PWA Install Banner (shows when installable) -->
    <div class="pwa-install-banner" *ngIf="showInstallBanner">
      <div class="install-content">
        <div class="install-icon">
          <i class="feather icon-download"></i>
        </div>
        <div class="install-text">
          <h4>Install Survey App</h4>
          <p>Get the full app experience with offline access</p>
        </div>
        <div class="install-actions">
          <button class="btn btn-primary" (click)="installApp()">
            <i class="feather icon-plus"></i>
            Install
          </button>
          <button class="btn btn-outline" (click)="dismissBanner()">
            <i class="feather icon-x"></i>
            Not Now
          </button>
        </div>
      </div>
    </div>

    <!-- Manual Install Button (always visible for testing) -->
    <div class="pwa-manual-install" *ngIf="!showInstallBanner && showManualButton">
      <button class="manual-install-btn" (click)="showInstallInfo()">
        <i class="feather icon-download"></i>
        <span>Install App</span>
      </button>
    </div>

    <!-- Install Instructions Modal -->
    <div class="install-modal" *ngIf="showInstructions" (click)="hideInstallInfo()">
      <div class="modal-content" (click)="$event.stopPropagation()">
        <div class="modal-header">
          <h3>Install Survey App</h3>
          <button class="close-btn" (click)="hideInstallInfo()">
            <i class="feather icon-x"></i>
          </button>
        </div>
        <div class="modal-body">
          <div class="install-steps">
            <div class="step">
              <div class="step-icon">
                <i class="feather icon-more-vertical"></i>
              </div>
              <div class="step-text">
                <h4>Chrome/Edge</h4>
                <p>Click the menu (⋮) → "Install Survey App"</p>
              </div>
            </div>
            <div class="step">
              <div class="step-icon">
                <i class="feather icon-share"></i>
              </div>
              <div class="step-text">
                <h4>Safari (iOS)</h4>
                <p>Tap Share → "Add to Home Screen"</p>
              </div>
            </div>
            <div class="step">
              <div class="step-icon">
                <i class="feather icon-smartphone"></i>
              </div>
              <div class="step-text">
                <h4>Android</h4>
                <p>Tap menu → "Add to Home screen"</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .pwa-install-banner {
      position: fixed;
      bottom: 20px;
      left: 20px;
      right: 20px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      border-radius: 12px;
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
      z-index: 1000;
      animation: slideUp 0.3s ease;
    }

    .install-content {
      display: flex;
      align-items: center;
      padding: 1rem;
      gap: 1rem;
    }

    .install-icon {
      font-size: 2rem;
      opacity: 0.9;
    }

    .install-text {
      flex: 1;
      
      h4 {
        margin: 0 0 0.25rem 0;
        font-size: 1.1rem;
        font-weight: 600;
      }
      
      p {
        margin: 0;
        font-size: 0.9rem;
        opacity: 0.9;
      }
    }

    .install-actions {
      display: flex;
      gap: 0.5rem;
      
      .btn {
        padding: 0.5rem 1rem;
        border-radius: 8px;
        font-size: 0.85rem;
        font-weight: 600;
        border: none;
        cursor: pointer;
        transition: all 0.2s ease;
        display: flex;
        align-items: center;
        gap: 0.25rem;
        
        &.btn-primary {
          background: rgba(255, 255, 255, 0.2);
          color: white;
          
          &:hover {
            background: rgba(255, 255, 255, 0.3);
          }
        }
        
        &.btn-outline {
          background: transparent;
          color: white;
          border: 1px solid rgba(255, 255, 255, 0.3);
          
          &:hover {
            background: rgba(255, 255, 255, 0.1);
          }
        }
      }
    }

    @keyframes slideUp {
      from {
        transform: translateY(100%);
        opacity: 0;
      }
      to {
        transform: translateY(0);
        opacity: 1;
      }
    }

    @media (max-width: 480px) {
      .pwa-install-banner {
        left: 10px;
        right: 10px;
        bottom: 10px;
      }
      
      .install-content {
        flex-direction: column;
        text-align: center;
        gap: 0.75rem;
      }
      
      .install-actions {
        width: 100%;
        
        .btn {
          flex: 1;
        }
      }
    }

    // Manual Install Button
    .pwa-manual-install {
      position: fixed;
      bottom: 20px;
      right: 20px;
      z-index: 1000;
    }

    .manual-install-btn {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      border: none;
      border-radius: 50px;
      padding: 1rem 1.5rem;
      font-size: 0.9rem;
      font-weight: 600;
      cursor: pointer;
      box-shadow: 0 4px 20px rgba(102, 126, 234, 0.4);
      transition: all 0.3s ease;
      display: flex;
      align-items: center;
      gap: 0.5rem;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 25px rgba(102, 126, 234, 0.6);
      }

      i {
        font-size: 1.1rem;
      }
    }

    // Install Instructions Modal
    .install-modal {
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(0, 0, 0, 0.7);
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 2000;
      animation: fadeIn 0.3s ease;
    }

    .modal-content {
      background: white;
      border-radius: 12px;
      max-width: 500px;
      width: 90%;
      max-height: 80vh;
      overflow-y: auto;
      animation: slideUp 0.3s ease;
    }

    .modal-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 1.5rem;
      border-bottom: 1px solid #eee;

      h3 {
        margin: 0;
        color: #2c3e50;
        font-size: 1.3rem;
      }

      .close-btn {
        background: none;
        border: none;
        font-size: 1.5rem;
        cursor: pointer;
        color: #7f8c8d;
        padding: 0.25rem;
        border-radius: 4px;
        transition: all 0.2s ease;

        &:hover {
          background: #f8f9fa;
          color: #2c3e50;
        }
      }
    }

    .modal-body {
      padding: 1.5rem;
    }

    .install-steps {
      display: flex;
      flex-direction: column;
      gap: 1.5rem;
    }

    .step {
      display: flex;
      align-items: flex-start;
      gap: 1rem;

      .step-icon {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        width: 3rem;
        height: 3rem;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-shrink: 0;
        font-size: 1.2rem;
      }

      .step-text {
        flex: 1;

        h4 {
          margin: 0 0 0.5rem 0;
          color: #2c3e50;
          font-size: 1.1rem;
          font-weight: 600;
        }

        p {
          margin: 0;
          color: #7f8c8d;
          line-height: 1.5;
        }
      }
    }

    @keyframes fadeIn {
      from { opacity: 0; }
      to { opacity: 1; }
    }

    @keyframes slideUp {
      from {
        transform: translateY(30px);
        opacity: 0;
      }
      to {
        transform: translateY(0);
        opacity: 1;
      }
    }

    @media (max-width: 480px) {
      .pwa-manual-install {
        bottom: 10px;
        right: 10px;
      }

      .manual-install-btn {
        padding: 0.875rem 1.25rem;
        font-size: 0.85rem;
      }

      .modal-content {
        margin: 1rem;
        width: calc(100% - 2rem);
      }

      .modal-header,
      .modal-body {
        padding: 1rem;
      }

      .step {
        .step-icon {
          width: 2.5rem;
          height: 2.5rem;
          font-size: 1rem;
        }
      }
    }
  `]
})
export class PwaInstallComponent implements OnInit, OnDestroy {
  showInstallBanner = false;
  showManualButton = true;
  showInstructions = false;
  private subscription = new Subscription();

  constructor(private pwaService: PwaService) {}

  ngOnInit(): void {
    this.subscription.add(
      this.pwaService.isInstallable$.subscribe(isInstallable => {
        this.showInstallBanner = isInstallable;
      })
    );
  }

  ngOnDestroy(): void {
    this.subscription.unsubscribe();
  }

  async installApp(): Promise<void> {
    const installed = await this.pwaService.installPwa();
    if (installed) {
      this.showInstallBanner = false;
    }
  }

  dismissBanner(): void {
    this.showInstallBanner = false;
    // Store dismissal in localStorage to avoid showing again for a while
    localStorage.setItem('pwa-install-dismissed', Date.now().toString());
  }

  showInstallInfo(): void {
    this.showInstructions = true;
  }

  hideInstallInfo(): void {
    this.showInstructions = false;
  }
}
