import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormsModule } from '@angular/forms';
import { RouterModule } from '@angular/router';

import { EmployeeSurveyRoutingModule } from './employee-survey-routing.module';
import { EmployeeSurveyComponent } from './employee-survey.component';
import { SurveyStartComponent } from './survey-start/survey-start.component';
import { SurveyQuizComponent } from './survey-quiz/survey-quiz.component';
import { SurveyCompleteComponent } from './survey-complete/survey-complete.component';

@NgModule({
  declarations: [],
  imports: [
    CommonModule,
    ReactiveFormsModule,
    FormsModule,
    RouterModule,
    EmployeeSurveyRoutingModule
  ]
})
export class EmployeeSurveyModule { }
