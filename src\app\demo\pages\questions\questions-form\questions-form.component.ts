// src/app/demo/pages/questions/question-form/question-form.component.ts
import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, FormArray, Validators, ReactiveFormsModule } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { QuestionService } from '../../../../core/services/question.service';
import { CategoryService } from '../../../../core/services/category.service';
import { LanguageService } from '../../../../core/services/language.service';
import { AuthService } from '../../../../core/services/auth.service';
import { Question } from '../../../../core/models/question.model';
import { Category } from '../../../../core/models/category.model';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-question-form',
  standalone: true,
  templateUrl: './question-form.component.html',
  styleUrls: ['./question-form.component.scss'],
  imports: [ReactiveFormsModule, CommonModule],
})
export class QuestionFormComponent implements OnInit {
  questionForm: FormGroup;
  questionId: string | null = null;
  isEditMode = false;
  loading = false;
  submitting = false;
  error = '';
  categories: Category[] = [];
  loadingCategories = false;

  // Multi-language support
  supportedLanguages = this.languageService.getSupportedLanguages();
  activeLanguageTab = 'en';
  showPreview = false;

  constructor(
    private fb: FormBuilder,
    private route: ActivatedRoute,
    public router: Router,
    private questionService: QuestionService,
    private categoryService: CategoryService,
    private languageService: LanguageService,
    private authService: AuthService
  ) {
    this.questionForm = this.fb.group({
      question_text: this.createMultiLanguageFormGroup(),
      options: this.fb.array([])
    });
  }

  ngOnInit(): void {
    this.questionId = this.route.snapshot.paramMap.get('id');
    this.isEditMode = !!this.questionId;

    // Load categories first
    this.loadCategories();

    if (this.isEditMode && this.questionId) {
      this.loadQuestion(this.questionId);
    } else {
      // Add at least two options for a new question
      this.addOption();
      this.addOption();
    }
  }

  get options(): FormArray {
    return this.questionForm.get('options') as FormArray;
  }

  // Multi-language helper methods
  createMultiLanguageFormGroup(): FormGroup {
    const group: any = {};
    this.supportedLanguages.forEach(lang => {
      group[lang.code] = ['', lang.code === 'en' ? [Validators.required, Validators.minLength(5)] : []];
    });
    return this.fb.group(group);
  }

  createMultiLanguageOptionFormGroup(): FormGroup {
    const textGroup: any = {};
    this.supportedLanguages.forEach(lang => {
      textGroup[lang.code] = ['', lang.code === 'en' ? Validators.required : []];
    });

    return this.fb.group({
      option_text: this.fb.group(textGroup),
      option_value: [5], // Default value, will be set based on category
      categoryId: ['', Validators.required] // Make category required
    });
  }

  switchLanguageTab(languageCode: string): void {
    this.activeLanguageTab = languageCode;
  }

  getQuestionTextControl(languageCode: string): any {
    return this.questionForm.get(['question_text', languageCode]);
  }

  getOptionTextControl(optionIndex: number, languageCode: string): any {
    return this.options.at(optionIndex).get(['option_text', languageCode]);
  }

  addOption(): void {
    this.options.push(this.createMultiLanguageOptionFormGroup());
  }

  removeOption(index: number): void {
    if (this.options.length > 2) {
      this.options.removeAt(index);
    } else {
      alert('A question must have at least 2 options');
    }
  }

  loadCategories(): void {
    this.loadingCategories = true;
    this.categoryService.getCategories()
      .subscribe({
        next: categories => {
          this.categories = categories;
          this.loadingCategories = false;
        },
        error: error => {
          console.error('Error loading categories:', error);
          this.loadingCategories = false;
          // Continue without categories - they are optional
        }
      });
  }

  // Method to handle category change and auto-set option value
  onCategoryChange(optionIndex: number, categoryId: string): void {
    const categoryValues: { [key: string]: number } = {
      'VERY_POSITIVE': 5,
      'POSITIVE': 4,
      'NEUTRAL': 3,
      'NEGATIVE': 2,
      'VERY_NEGATIVE': 1
    };

    const selectedCategory = this.categories.find(cat => cat.id === categoryId);
    if (selectedCategory) {
      const optionValue = categoryValues[selectedCategory.name] || 3;
      this.options.at(optionIndex).patchValue({
        option_value: optionValue
      });
    }
  }

  loadQuestion(id: string): void {
    this.loading = true;
    this.questionService.getQuestion(id)
      .subscribe({
        next: question => {
          // Handle both old format (string) and new format (multi-language object)
          if (typeof question.question_text === 'string') {
            // Convert old format to new format
            const questionTextGroup: any = {};
            this.supportedLanguages.forEach(lang => {
              questionTextGroup[lang.code] = lang.code === 'en' ? question.question_text : '';
            });
            this.questionForm.patchValue({
              question_text: questionTextGroup
            });
          } else {
            // New multi-language format
            this.questionForm.patchValue({
              question_text: question.question_text
            });
          }

          // Clear default options
          while (this.options.length) {
            this.options.removeAt(0);
          }

          // Add existing options
          question.options.forEach(option => {
            const optionFormGroup = this.createMultiLanguageOptionFormGroup();

            // Handle multi-language format
            optionFormGroup.patchValue({
              id: option.id,
              option_text: option.option_text,
              option_value: option.option_value,
              categoryId: option.categoryId || ''
            });

            this.options.push(optionFormGroup);
          });

          this.loading = false;
        },
        error: error => {
          this.error = 'Failed to load question';
          this.loading = false;
          console.error('Error loading question:', error);
        }
      });
  }

  onSubmit(): void {
    if (this.questionForm.invalid) {
      this.markFormGroupTouched(this.questionForm);
      return;
    }

    // Validate that at least English is filled for question and all options
    if (!this.validateMultiLanguageForm()) {
      this.error = 'Please fill in at least the English text for the question and all options.';
      return;
    }

    this.submitting = true;
    this.error = '';
    const formValue = this.questionForm.value;

    // Prepare question data with createdBy field
    const questionData: Question = {
      ...formValue,
      createdBy: this.getCurrentUserId() // Add current user ID
    };

    // Log the data being sent to API for debugging
    console.log('=== QUESTION DATA BEING SENT TO API ===');
    console.log(JSON.stringify(questionData, null, 2));

    if (this.isEditMode && this.questionId) {
      this.questionService.updateQuestion(this.questionId, questionData)
        .subscribe({
          next: () => {
            this.router.navigate(['/questions']);
          },
          error: error => {
            this.error = 'Failed to update question';
            this.submitting = false;
            console.error('Error updating question:', error);
          }
        });
    } else {
      this.questionService.createQuestion(questionData)
        .subscribe({
          next: () => {
            this.router.navigate(['/questions']);
          },
          error: error => {
            this.error = 'Failed to create question';
            this.submitting = false;
            console.error('Error creating question:', error);
          }
        });
    }
  }

  // Validation methods
  validateMultiLanguageForm(): boolean {
    // Check question text
    const questionTextControl = this.questionForm.get('question_text');
    if (!questionTextControl || !questionTextControl.get('en')?.value?.trim()) {
      return false;
    }

    // Check all options
    for (let i = 0; i < this.options.length; i++) {
      const optionTextControl = this.options.at(i).get('option_text');
      if (!optionTextControl || !optionTextControl.get('en')?.value?.trim()) {
        return false;
      }
    }

    return true;
  }

  markFormGroupTouched(formGroup: FormGroup): void {
    Object.keys(formGroup.controls).forEach(key => {
      const control = formGroup.get(key);
      if (control instanceof FormGroup) {
        this.markFormGroupTouched(control);
      } else if (control instanceof FormArray) {
        control.controls.forEach(arrayControl => {
          if (arrayControl instanceof FormGroup) {
            this.markFormGroupTouched(arrayControl);
          } else {
            arrayControl.markAsTouched();
          }
        });
      } else {
        control?.markAsTouched();
      }
    });
  }

  // Helper method to get completion status for each language
  getLanguageCompletionStatus(languageCode: string): { question: boolean; options: boolean } {
    const questionText = this.questionForm.get(['question_text', languageCode])?.value?.trim();
    const questionComplete = !!questionText;

    let optionsComplete = true;
    for (let i = 0; i < this.options.length; i++) {
      const optionText = this.options.at(i).get(['option_text', languageCode])?.value?.trim();
      if (!optionText) {
        optionsComplete = false;
        break;
      }
    }

    return {
      question: questionComplete,
      options: optionsComplete
    };
  }

  // Helper method to get overall completion percentage
  getOverallCompletionPercentage(): number {
    let totalFields = 0;
    let completedFields = 0;

    this.supportedLanguages.forEach(lang => {
      const status = this.getLanguageCompletionStatus(lang.code);
      totalFields += 1 + this.options.length; // 1 question + n options
      if (status.question) completedFields++;
      if (status.options) completedFields += this.options.length;
    });

    return totalFields > 0 ? Math.round((completedFields / totalFields) * 100) : 0;
  }

  // Preview method to show JSON structure
  getFormDataAsJson(): string {
    try {
      const formData = this.questionForm.value;
      const dataWithUser = {
        ...formData,
        createdBy: this.getCurrentUserId()
      };
      return JSON.stringify(dataWithUser, null, 2);
    } catch (error) {
      return 'Error generating preview';
    }
  }

  // Get current user ID from auth service
  getCurrentUserId(): string {
    const currentUser = this.authService.currentUserValue;
    return currentUser?.id || 'USER_UUID_HERE'; // Fallback for demo
  }
}