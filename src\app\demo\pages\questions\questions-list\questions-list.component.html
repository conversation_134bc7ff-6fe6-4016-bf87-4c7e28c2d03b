<div class="questions-container">
  <!-- Header Section -->
  <div class="row mb-4">
    <div class="col-12">
      <div class="page-header">
        <div class="d-flex justify-content-between align-items-center">
          <div>
            <h2 class="page-title">
              <i class="feather icon-help-circle me-2"></i>
              Question Management
            </h2>
            <p class="page-subtitle">Manage survey questions and response options</p>
          </div>
          <button class="btn btn-primary btn-create" (click)="createQuestion()">
            <i class="feather icon-plus me-2"></i>
            Create New Question
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- Search and Filter Section -->
  <div *ngIf="!loading && !error" class="row mb-4">
    <div class="col-12">
      <div class="search-filter-card">
        <div class="row align-items-center">
          <div class="col-md-6">
            <div class="search-container">
              <div class="search-input-wrapper">
                <i class="feather icon-search search-icon"></i>
                <input
                  type="text"
                  class="form-control search-input"
                  placeholder="Search questions or options..."
                  [(ngModel)]="searchTerm"
                  (input)="onSearchChange(searchTerm)"
                />
                <div *ngIf="loading" class="search-loading">
                  <div class="spinner-border spinner-border-sm text-primary" role="status">
                    <span class="visually-hidden">Searching...</span>
                  </div>
                </div>
                <button
                  *ngIf="searchTerm"
                  class="btn btn-clear"
                  (click)="clearSearch()"
                  title="Clear search">
                  <i class="feather icon-x"></i>
                </button>
              </div>
            </div>
          </div>
          <div class="col-md-3">
            <div class="filter-container">
              <label class="filter-label">Status Filter:</label>
              <select
                class="form-select status-filter"
                [(ngModel)]="selectedStatus"
                (change)="onStatusFilterChange(selectedStatus)">
                <option value="all">All Questions</option>
                <option value="active">Active Only</option>
                <option value="inactive">Inactive Only</option>
              </select>
            </div>
          </div>
          <div class="col-md-3">
            <div class="search-results">
              <span class="results-text">
                <i class="feather icon-filter me-1"></i>
                Showing {{ getStartRange() }} to {{ getEndRange() }} of {{ getTotalQuestionsCount() }} questions
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Loading State -->
  <div *ngIf="loading" class="loading-container">
    <div class="card">
      <div class="card-body text-center py-5">
        <div class="spinner-border text-primary mb-3" role="status">
          <span class="visually-hidden">Loading...</span>
        </div>
        <p class="text-muted">Loading questions...</p>
      </div>
    </div>
  </div>

  <!-- Error State -->
  <div *ngIf="error && !loading" class="row">
    <div class="col-12">
      <div class="alert alert-danger d-flex align-items-center" role="alert">
        <i class="feather icon-alert-triangle me-2"></i>
        <div>{{ error }}</div>
      </div>
    </div>
  </div>

  <!-- Questions List -->
  <div *ngIf="!loading && !error" class="questions-list">
    <!-- Stats Summary -->
    <div class="row mb-4">
      <div class="col-md-3 col-sm-6">
        <div class="stats-card">
          <div class="stats-icon bg-primary">
            <i class="feather icon-list"></i>
          </div>
          <div class="stats-content">
            <h3>{{ questions.length }}</h3>
            <p>Total Questions</p>
          </div>
        </div>
      </div>
      <div class="col-md-3 col-sm-6">
        <div class="stats-card">
          <div class="stats-icon bg-success">
            <i class="feather icon-check-circle"></i>
          </div>
          <div class="stats-content">
            <h3>{{ getActiveQuestionsCount() }}</h3>
            <p>Active Questions</p>
          </div>
        </div>
      </div>
      <div class="col-md-3 col-sm-6">
        <div class="stats-card">
          <div class="stats-icon bg-warning">
            <i class="feather icon-pause-circle"></i>
          </div>
          <div class="stats-content">
            <h3>{{ getInactiveQuestionsCount() }}</h3>
            <p>Inactive Questions</p>
          </div>
        </div>
      </div>
      <div class="col-md-3 col-sm-6">
        <div class="stats-card">
          <div class="stats-icon bg-info">
            <i class="feather icon-bar-chart-2"></i>
          </div>
          <div class="stats-content">
            <h3>{{ getAverageOptionsCount() }}</h3>
            <p>Avg Options</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Questions Cards -->
    <div class="row">
      <div class="col-12" *ngFor="let question of filteredQuestions; let i = index">
        <div class="question-card" [class.inactive]="!question.isActive">
          <div class="question-header">
            <div class="question-meta">
              <span class="question-number">#{{ (currentPage - 1) * pageSize + i + 1 }}</span>
              <span class="question-status" [class]="question.isActive ? 'active' : 'inactive'">
                <i class="feather" [class.icon-check-circle]="question.isActive" [class.icon-pause-circle]="!question.isActive"></i>
                {{ question.isActive ? 'Active' : 'Inactive' }}
              </span>
            </div>
            <div class="question-actions">
              <button class="btn btn-sm btn-outline-primary me-2" (click)="editQuestion(question.id)" title="Edit Question">
                <i class="feather icon-edit-2"></i>
              </button>
              <button class="btn btn-sm btn-outline-secondary" (click)="toggleQuestionStatus(question)"
                      [title]="question.isActive ? 'Deactivate Question' : 'Activate Question'">
                <i class="feather" [class.icon-pause]="question.isActive" [class.icon-play]="!question.isActive"></i>
              </button>
            </div>
          </div>

          <div class="question-content">
            <h4 class="question-text" [innerHTML]="highlightSearchTerm(question.question_text)"></h4>

            <div class="options-container">
              <h6 class="options-title">
                <i class="feather icon-list me-2"></i>
                Response Options ({{ question.options.length }})
              </h6>
              <div class="options-grid">
                <div class="option-item" *ngFor="let option of question.options">
                  <div class="option-content">
                    <span class="option-text" [innerHTML]="highlightSearchTerm(option.option_text)"></span>
                    <!-- <span class="option-value">{{ option.option_value }}</span> -->
                    <span *ngIf="option.categoryId"
                          class="option-category badge"
                          [style.background-color]="getCategoryColor(option.categoryId)"
                          [title]="'Category: ' + getCategoryName(option.categoryId)">
                      {{ getCategoryName(option.categoryId) }}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="question-footer">
            <div class="question-dates">
              <small class="text-muted">
                <i class="feather icon-calendar me-1"></i>
                Created: {{ formatDate(question.createdAt) }}
                <span *ngIf="question.updatedAt !== question.createdAt" class="ms-3">
                  <i class="feather icon-edit me-1"></i>
                  Updated: {{ formatDate(question.updatedAt) }}
                </span>
              </small>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Pagination Controls -->
    <div *ngIf="totalPages > 1" class="pagination-container">
      <div class="row align-items-center">
        <div class="col-md-6">
          <div class="page-size-selector">
            <label class="form-label me-2">Items per page:</label>
            <select class="form-select form-select-sm d-inline-block w-auto"
                    [(ngModel)]="pageSize"
                    (change)="onPageSizeChange(pageSize)">
              <option [value]="5">5</option>
              <option [value]="10">10</option>
              <option [value]="20">20</option>
              <option [value]="50">50</option>
            </select>
          </div>
        </div>
        <div class="col-md-6">
          <nav aria-label="Questions pagination">
            <ul class="pagination justify-content-end mb-0">
              <li class="page-item" [class.disabled]="currentPage === 1">
                <a class="page-link" (click)="onPageChange(currentPage - 1)" [attr.aria-disabled]="currentPage === 1">
                  <i class="feather icon-chevron-left"></i>
                </a>
              </li>
              <li *ngFor="let page of getPaginationArray()"
                  class="page-item"
                  [class.active]="page === currentPage">
                <a class="page-link" (click)="onPageChange(page)">{{ page }}</a>
              </li>
              <li class="page-item" [class.disabled]="currentPage === totalPages">
                <a class="page-link" (click)="onPageChange(currentPage + 1)" [attr.aria-disabled]="currentPage === totalPages">
                  <i class="feather icon-chevron-right"></i>
                </a>
              </li>
            </ul>
          </nav>
        </div>
      </div>
      <div class="pagination-info text-center mt-3">
        <small class="text-muted">
          Showing {{ getStartRange() }} to {{ getEndRange() }} of {{ getTotalQuestionsCount() }} questions
        </small>
      </div>
    </div>

    <!-- Empty State -->
    <div *ngIf="filteredQuestions.length === 0 && questions.length > 0" class="empty-state">
      <div class="card">
        <div class="card-body text-center py-5">
          <i class="feather icon-search empty-icon"></i>
          <h4>No Questions Match Your Search</h4>
          <p class="text-muted mb-4">Try adjusting your search terms or filters</p>
          <button class="btn btn-outline-primary" (click)="clearSearch()">
            <i class="feather icon-refresh-cw me-2"></i>
            Clear Search & Filters
          </button>
        </div>
      </div>
    </div>

    <!-- No Questions at All -->
    <div *ngIf="questions.length === 0" class="empty-state">
      <div class="card">
        <div class="card-body text-center py-5">
          <i class="feather icon-help-circle empty-icon"></i>
          <h4>No Questions Found</h4>
          <p class="text-muted mb-4">Get started by creating your first survey question</p>
          <button class="btn btn-primary" (click)="createQuestion()">
            <i class="feather icon-plus me-2"></i>
            Create Your First Question
          </button>
        </div>
      </div>
    </div>
  </div>
</div>