<!-- src/app/auth/login/login.component.html -->
<div class="auth-wrapper">
  <div class="auth-content">
    <div class="auth-bg">
      <span class="r"></span>
      <span class="r s"></span>
      <span class="r s"></span>
      <span class="r"></span>
    </div>
    <div class="card">
      <div class="card-body text-center">
        <div class="mb-4">
          <i class="feather icon-unlock auth-icon"></i>
        </div>
        <h3 class="mb-4">Login</h3>
        
        <form [formGroup]="loginForm" (ngSubmit)="onSubmit()">
          <div class="input-group mb-3">
            <input 
              type="email" 
              class="form-control" 
              placeholder="Email" 
              formControlName="email"
              [ngClass]="{ 'is-invalid': loginForm.controls['email'].touched && loginForm.controls['email'].errors }"
            />
            <div *ngIf="loginForm.controls['email'].touched && loginForm.controls['email'].errors" class="invalid-feedback">
              <div *ngIf="loginForm.controls['email'].errors['required']">Email is required</div>
              <div *ngIf="loginForm.controls['email'].errors['email']">Enter a valid email address</div>
            </div>
          </div>
          
          <div class="input-group mb-4">
            <input 
              type="password" 
              class="form-control" 
              placeholder="Password" 
              formControlName="password"
              [ngClass]="{ 'is-invalid': loginForm.controls['password'].touched && loginForm.controls['password'].errors }"
            />
            <div *ngIf="loginForm.controls['password'].touched && loginForm.controls['password'].errors" class="invalid-feedback">
              <div *ngIf="loginForm.controls['password'].errors['required']">Password is required</div>
            </div>
          </div>
          
          <div *ngIf="error" class="alert alert-danger mb-3">{{ error }}</div>
          
          <button [disabled]="loading || loginForm.invalid" class="btn btn-primary mb-4">
            <span *ngIf="loading" class="spinner-border spinner-border-sm mr-1"></span>
            Login
          </button>
        </form>
        
        <p class="mb-2 text-muted">
          Forgot password?
          <a [routerLink]="['/auth/reset-password']">Reset</a>
        </p>
      </div>
    </div>
  </div>
</div>