// src/app/demo/pages/questions/question-list/question-list.component.ts
import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { QuestionService, QuestionFilters } from '../../../../core/services/question.service';
import { CategoryService } from '../../../../core/services/category.service';
import { Question } from '../../../../core/models/question.model';
import { Category } from '../../../../core/models/category.model';
import { ChangeDetectorRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

@Component({
  selector: 'app-questions-list',
  templateUrl: './questions-list.component.html',
  styleUrls: ['./questions-list.component.scss'],
  imports: [CommonModule, FormsModule]
})
export class QuestionsListComponent implements OnInit {
  questions: Question[] = [];
  filteredQuestions: Question[] = [];
  loading = false;
  error = '';
  searchTerm = '';
  selectedStatus = 'all'; // 'all', 'active', 'inactive'
  categories: Category[] = [];
  categoriesMap: Map<string, Category> = new Map();

  // Pagination properties
  currentPage = 1;
  pageSize = 10;
  totalQuestions = 0;
  totalPages = 0;

  // Filters for API
  filters: QuestionFilters = {
    page: 1,
    limit: 10,
    search: '',
    status: 'all'
  };

  // Dummy data for questions
  private dummyQuestions: Question[] = [
    {
      id: '1',
      question_text: 'How satisfied are you with your current work-life balance?',
      options: [
        { id: '1a', option_text: 'Very Satisfied', option_value: 5 },
        { id: '1b', option_text: 'Satisfied', option_value: 4 },
        { id: '1c', option_text: 'Neutral', option_value: 3 },
        { id: '1d', option_text: 'Dissatisfied', option_value: 2 },
        { id: '1e', option_text: 'Very Dissatisfied', option_value: 1 }
      ],
      isActive: true,
      createdAt: '2024-01-15T10:30:00Z',
      updatedAt: '2024-01-15T10:30:00Z'
    },
    {
      id: '2',
      question_text: 'How would you rate the communication within your team?',
      options: [
        { id: '2a', option_text: 'Excellent', option_value: 5 },
        { id: '2b', option_text: 'Good', option_value: 4 },
        { id: '2c', option_text: 'Average', option_value: 3 },
        { id: '2d', option_text: 'Poor', option_value: 2 },
        { id: '2e', option_text: 'Very Poor', option_value: 1 }
      ],
      isActive: true,
      createdAt: '2024-01-16T14:20:00Z',
      updatedAt: '2024-01-16T14:20:00Z'
    },
    {
      id: '3',
      question_text: 'How likely are you to recommend this company as a great place to work?',
      options: [
        { id: '3a', option_text: 'Extremely Likely', option_value: 10 },
        { id: '3b', option_text: 'Very Likely', option_value: 8 },
        { id: '3c', option_text: 'Likely', option_value: 6 },
        { id: '3d', option_text: 'Neutral', option_value: 5 },
        { id: '3e', option_text: 'Unlikely', option_value: 3 },
        { id: '3f', option_text: 'Very Unlikely', option_value: 1 }
      ],
      isActive: true,
      createdAt: '2024-01-17T09:15:00Z',
      updatedAt: '2024-01-17T09:15:00Z'
    },
    {
      id: '4',
      question_text: 'How do you feel about your current workload?',
      options: [
        { id: '4a', option_text: 'Perfect balance', option_value: 5 },
        { id: '4b', option_text: 'Manageable', option_value: 4 },
        { id: '4c', option_text: 'Slightly overwhelming', option_value: 3 },
        { id: '4d', option_text: 'Too much work', option_value: 2 },
        { id: '4e', option_text: 'Completely overwhelmed', option_value: 1 }
      ],
      isActive: true,
      createdAt: '2024-01-18T11:45:00Z',
      updatedAt: '2024-01-18T11:45:00Z'
    },
    {
      id: '5',
      question_text: 'How satisfied are you with the professional development opportunities?',
      options: [
        { id: '5a', option_text: 'Very Satisfied', option_value: 5 },
        { id: '5b', option_text: 'Satisfied', option_value: 4 },
        { id: '5c', option_text: 'Neutral', option_value: 3 },
        { id: '5d', option_text: 'Dissatisfied', option_value: 2 },
        { id: '5e', option_text: 'Very Dissatisfied', option_value: 1 }
      ],
      isActive: true,
      createdAt: '2024-01-19T16:30:00Z',
      updatedAt: '2024-01-19T16:30:00Z'
    },
    {
      id: '6',
      question_text: 'How would you rate your relationship with your direct supervisor?',
      options: [
        { id: '6a', option_text: 'Excellent', option_value: 5 },
        { id: '6b', option_text: 'Good', option_value: 4 },
        { id: '6c', option_text: 'Fair', option_value: 3 },
        { id: '6d', option_text: 'Poor', option_value: 2 },
        { id: '6e', option_text: 'Very Poor', option_value: 1 }
      ],
      isActive: true,
      createdAt: '2024-01-20T13:10:00Z',
      updatedAt: '2024-01-20T13:10:00Z'
    },
    {
      id: '7',
      question_text: 'How comfortable do you feel expressing your opinions at work?',
      options: [
        { id: '7a', option_text: 'Very Comfortable', option_value: 5 },
        { id: '7b', option_text: 'Comfortable', option_value: 4 },
        { id: '7c', option_text: 'Somewhat Comfortable', option_value: 3 },
        { id: '7d', option_text: 'Uncomfortable', option_value: 2 },
        { id: '7e', option_text: 'Very Uncomfortable', option_value: 1 }
      ],
      isActive: false,
      createdAt: '2024-01-21T08:25:00Z',
      updatedAt: '2024-01-21T08:25:00Z'
    },
    {
      id: '8',
      question_text: 'How satisfied are you with your compensation package?',
      options: [
        { id: '8a', option_text: 'Very Satisfied', option_value: 5 },
        { id: '8b', option_text: 'Satisfied', option_value: 4 },
        { id: '8c', option_text: 'Neutral', option_value: 3 },
        { id: '8d', option_text: 'Dissatisfied', option_value: 2 },
        { id: '8e', option_text: 'Very Dissatisfied', option_value: 1 }
      ],
      isActive: true,
      createdAt: '2024-01-22T15:40:00Z',
      updatedAt: '2024-01-22T15:40:00Z'
    }
  ];

  constructor(
    private questionService: QuestionService,
    private categoryService: CategoryService,
    private router: Router,
    private cdRef: ChangeDetectorRef
  ) { }

  ngOnInit(): void {
    this.loadCategories();
    this.loadQuestions();
  }

  loadQuestions(): void {
    this.loading = true;
    this.error = '';

    // Update filters with current search and status
    this.filters = {
      page: this.currentPage,
      limit: this.pageSize,
      search: this.searchTerm.trim(),
      status: this.selectedStatus as 'all' | 'active' | 'inactive'
    };

    this.questionService.getQuestions(this.filters)
      .subscribe({
        next: (response) => {
          console.log('Questions from API:', response);
          this.questions = response.questions;
          this.totalQuestions = response.totalQuestions;
          this.currentPage = response.currentPage;
          this.totalPages = response.totalPages;
          this.pageSize = response.pageSize;

          // For paginated API, we don't need client-side filtering
          this.filteredQuestions = [...this.questions];
          this.loading = false;
          this.cdRef.detectChanges();
        },
        error: (error) => {
          console.warn('API failed, using dummy data:', error);
          // Simulate pagination with dummy data
          this.simulatePaginationWithDummyData();
          this.loading = false;
          this.cdRef.detectChanges();
        }
      });
  }

  private simulatePaginationWithDummyData(): void {
    // Apply client-side filtering for dummy data
    let filtered = [...this.dummyQuestions];

    // Apply search filter
    if (this.searchTerm.trim()) {
      const searchLower = this.searchTerm.toLowerCase().trim();
      filtered = filtered.filter(question => {
        const questionMatch = question.question_text.toLowerCase().includes(searchLower);
        const optionMatch = question.options.some(option =>
          option.option_text.toLowerCase().includes(searchLower)
        );
        return questionMatch || optionMatch;
      });
    }

    // Apply status filter
    if (this.selectedStatus !== 'all') {
      const isActive = this.selectedStatus === 'active';
      filtered = filtered.filter(question => question.isActive === isActive);
    }

    // Calculate pagination
    this.totalQuestions = filtered.length;
    this.totalPages = Math.ceil(this.totalQuestions / this.pageSize);

    // Get current page data
    const startIndex = (this.currentPage - 1) * this.pageSize;
    const endIndex = startIndex + this.pageSize;

    this.questions = filtered.slice(startIndex, endIndex);
    this.filteredQuestions = [...this.questions];
  }

  loadCategories(): void {
    this.categoryService.getCategories()
      .subscribe({
        next: categories => {
          this.categories = categories;
          // Create a map for quick category lookup
          this.categoriesMap.clear();
          categories.forEach(category => {
            this.categoriesMap.set(category.id, category);
          });
        },
        error: error => {
          console.error('Error loading categories:', error);
          // Continue without categories - they are optional
        }
      });
  }

  createQuestion(): void {
    this.router.navigate(['/questions/new']);
  }

  editQuestion(id: string): void {
    this.router.navigate(['/questions/edit', id]);
  }

  // Search and Filter Methods
  onSearchChange(searchTerm: string): void {
    this.searchTerm = searchTerm;
    this.currentPage = 1; // Reset to first page when searching
    this.loadQuestions();
  }

  onStatusFilterChange(status: string): void {
    this.selectedStatus = status;
    this.currentPage = 1; // Reset to first page when filtering
    this.loadQuestions();
  }

  clearSearch(): void {
    this.searchTerm = '';
    this.selectedStatus = 'all';
    this.currentPage = 1;
    this.loadQuestions();
  }

  // Pagination Methods
  onPageChange(page: number): void {
    if (page >= 1 && page <= this.totalPages && page !== this.currentPage) {
      this.currentPage = page;
      this.loadQuestions();
    }
  }

  onPageSizeChange(newPageSize: number): void {
    this.pageSize = newPageSize;
    this.currentPage = 1; // Reset to first page when changing page size
    this.loadQuestions();
  }

  getPaginationArray(): number[] {
    const pages: number[] = [];
    const maxVisiblePages = 5;

    if (this.totalPages <= maxVisiblePages) {
      // Show all pages if total pages is less than or equal to max visible
      for (let i = 1; i <= this.totalPages; i++) {
        pages.push(i);
      }
    } else {
      // Show pages around current page
      const startPage = Math.max(1, this.currentPage - Math.floor(maxVisiblePages / 2));
      const endPage = Math.min(this.totalPages, startPage + maxVisiblePages - 1);

      for (let i = startPage; i <= endPage; i++) {
        pages.push(i);
      }
    }

    return pages;
  }

  getStartRange(): number {
    return (this.currentPage - 1) * this.pageSize + 1;
  }

  getEndRange(): number {
    return Math.min(this.currentPage * this.pageSize, this.totalQuestions);
  }

  // Helper methods for template
  getActiveQuestionsCount(): number {
    // For paginated data, we'll show the count from current page
    // In a real implementation, you'd get this from the API response
    return this.questions.filter(q => q.isActive).length;
  }

  getInactiveQuestionsCount(): number {
    // For paginated data, we'll show the count from current page
    // In a real implementation, you'd get this from the API response
    return this.questions.filter(q => !q.isActive).length;
  }

  getAverageOptionsCount(): number {
    if (this.questions.length === 0) return 0;
    const totalOptions = this.questions.reduce((sum, q) => sum + q.options.length, 0);
    return Math.round(totalOptions / this.questions.length);
  }

  getFilteredQuestionsCount(): number {
    return this.totalQuestions; // Total filtered questions across all pages
  }

  getTotalQuestionsCount(): number {
    return this.totalQuestions;
  }

  // Method to highlight search terms in text
  highlightSearchTerm(text: string): string {
    if (!this.searchTerm.trim()) {
      return text;
    }

    const searchTerm = this.searchTerm.trim();
    const regex = new RegExp(`(${searchTerm})`, 'gi');
    return text.replace(regex, '<mark class="search-highlight">$1</mark>');
  }

  formatDate(dateString: string | undefined): string {
    if (!dateString) return 'N/A';
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  }

  toggleQuestionStatus(question: Question): void {
    question.isActive = !question.isActive;
    question.updatedAt = new Date().toISOString();
    console.log(`Question ${question.id} status changed to: ${question.isActive ? 'Active' : 'Inactive'}`);

    // Reload questions to reflect the change
    this.loadQuestions();

    // Here you would typically call an API to update the question status
  }

  getCategoryName(categoryId: string | undefined): string {
    if (!categoryId) return '';
    const category = this.categoriesMap.get(categoryId);
    return category ? category.name : '';
  }

  getCategoryColor(categoryId: string | undefined): string {
    if (!categoryId) return '#6c757d'; // Default gray color
    const category = this.categoriesMap.get(categoryId);
    return category?.color || '#6c757d';
  }
}
