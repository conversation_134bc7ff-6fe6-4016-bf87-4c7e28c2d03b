import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Router } from '@angular/router';
import { SurveyService } from '../../../../core/services/survey.service';

@Component({
  selector: 'app-survey-start',
  standalone: true,
  imports: [CommonModule, FormsModule],
  templateUrl: './survey-start.component.html',
  styleUrls: ['./survey-start.component.scss']
})
export class SurveyStartComponent implements OnInit {
  employeeName = '';
  selectedLetters: string[] = [];
  isNameComplete = false;
  showStartButton = false;

  // Virtual Keyboard Layout
  keyboardRows = [
    ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I'],
    ['J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R'],
    ['S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z']
  ];

  constructor(
    private router: Router,
    private surveyService: SurveyService
  ) {}

  ngOnInit(): void {
    // Reset any previous survey data
    this.surveyService.resetSurvey();
  }

  onLetterClick(letter: string): void {
    if (this.employeeName.length < 20) { // Limit name length
      this.employeeName += letter;
      this.selectedLetters.push(letter);
      this.checkNameComplete();
    }
  }

  onBackspace(): void {
    if (this.employeeName.length > 0) {
      this.employeeName = this.employeeName.slice(0, -1);
      this.selectedLetters.pop();
      this.checkNameComplete();
    }
  }

  onSpace(): void {
    if (this.employeeName.length > 0 && !this.employeeName.endsWith(' ')) {
      this.employeeName += ' ';
      this.selectedLetters.push('SPACE');
      this.checkNameComplete();
    }
  }

  onClear(): void {
    this.employeeName = '';
    this.selectedLetters = [];
    this.checkNameComplete();
  }

  checkNameComplete(): void {
    const trimmedName = this.employeeName.trim();
    this.isNameComplete = trimmedName.length >= 2;
    
    // Show start button after a short delay for better UX
    if (this.isNameComplete && !this.showStartButton) {
      setTimeout(() => {
        this.showStartButton = true;
      }, 500);
    } else if (!this.isNameComplete) {
      this.showStartButton = false;
    }
  }

  startSurvey(): void {
    if (this.isNameComplete) {
      // Save employee name to survey service
      this.surveyService.setEmployeeName(this.employeeName.trim());
      
      // Navigate to quiz
      this.router.navigate(['/employee-survey/quiz']);
    }
  }

  // Get display name with proper formatting
  getDisplayName(): string {
    return this.employeeName || 'Employee Name';
  }
}
