<div class="row">
  <div class="col-sm-12">
    <div class="card">
      <div class="card-header">
        <h5>Create Company Employee</h5>
      </div>
      <div class="card-body">
        <form [formGroup]="employeeForm" (ngSubmit)="onSubmit()">
          
          <!-- Employee Information Section -->
          <div class="row">
            <div class="col-md-12">
              <h6 class="mb-3 text-muted">Employee Information</h6>
            </div>
          </div>

          <div class="row">
            <div class="col-md-6">
              <div class="form-group">
                <label for="email" class="form-label">Email Address *</label>
                <input 
                  id="email"
                  type="email" 
                  class="form-control"
                  formControlName="email"
                  placeholder="Enter employee email"
                  [class.is-invalid]="isFieldInvalid('email')"
                />
                <div *ngIf="isFieldInvalid('email')" class="invalid-feedback">
                  {{ getFieldError('email') }}
                </div>
              </div>
            </div>
          </div>

          <!-- Password Section -->
          <div class="row">
            <div class="col-md-12">
              <h6 class="mb-3 mt-4 text-muted">Password Setup</h6>
            </div>
          </div>

          <div class="row">
            <div class="col-md-6">
              <div class="form-group">
                <label for="password" class="form-label">Password *</label>
                <input 
                  id="password"
                  type="password" 
                  class="form-control"
                  formControlName="password_hash"
                  placeholder="Enter password (min 8 characters)"
                  [class.is-invalid]="isFieldInvalid('password_hash')"
                />
                <div *ngIf="isFieldInvalid('password_hash')" class="invalid-feedback">
                  {{ getFieldError('password_hash') }}
                </div>
              </div>
            </div>
            
            <div class="col-md-6">
              <div class="form-group">
                <label for="confirmPassword" class="form-label">Confirm Password *</label>
                <input 
                  id="confirmPassword"
                  type="password" 
                  class="form-control"
                  formControlName="confirmPassword"
                  placeholder="Confirm password"
                  [class.is-invalid]="isFieldInvalid('confirmPassword')"
                />
                <div *ngIf="isFieldInvalid('confirmPassword')" class="invalid-feedback">
                  {{ getFieldError('confirmPassword') }}
                </div>
              </div>
            </div>
          </div>

          <!-- Role Section -->
          <div class="row">
            <div class="col-md-12">
              <h6 class="mb-3 mt-4 text-muted">Role & Hierarchy</h6>
            </div>
          </div>

          <div class="row">
            <div class="col-md-6">
              <div class="form-group">
                <div class="form-check">
                  <input 
                    class="form-check-input" 
                    type="checkbox" 
                    id="isSupervisor"
                    formControlName="isSupervisor"
                  />
                  <label class="form-check-label" for="isSupervisor">
                    This employee is a supervisor
                  </label>
                </div>
                <small class="form-text text-muted">
                  Check this if the employee will supervise other employees
                </small>
              </div>
            </div>
          </div>

          <div class="row" *ngIf="!isSupervisor">
            <div class="col-md-6">
              <div class="form-group">
                <label for="supervisor" class="form-label">Select Supervisor</label>
                <select 
                  id="supervisor"
                  class="form-control"
                  formControlName="supervisor_id"
                  [class.is-invalid]="isFieldInvalid('supervisor_id')"
                >
                  <option value="">Select a supervisor (optional)</option>
                  <option *ngFor="let supervisor of supervisors" [value]="supervisor.id">
                    {{ supervisor.name }} ({{ supervisor.email }})
                  </option>
                </select>
                <small class="form-text text-muted">
                  Select who will supervise this employee
                </small>
                <div *ngIf="isFieldInvalid('supervisor_id')" class="invalid-feedback">
                  {{ getFieldError('supervisor_id') }}
                </div>
              </div>
            </div>
          </div>

          <!-- Account Settings Section -->
          <div class="row">
            <div class="col-md-12">
              <h6 class="mb-3 mt-4 text-muted">Account Settings</h6>
            </div>
          </div>

          <div class="row">
            <div class="col-md-6">
              <div class="form-group">
                <div class="form-check">
                  <input 
                    class="form-check-input" 
                    type="checkbox" 
                    id="isActive"
                    formControlName="isActive"
                  />
                  <label class="form-check-label" for="isActive">
                    Account is active
                  </label>
                </div>
              </div>
            </div>
            
            <div class="col-md-6">
              <div class="form-group">
                <div class="form-check">
                  <input 
                    class="form-check-input" 
                    type="checkbox" 
                    id="forcePasswordChange"
                    formControlName="force_password_change"
                  />
                  <label class="form-check-label" for="forcePasswordChange">
                    Force password change on first login
                  </label>
                </div>
              </div>
            </div>
          </div>

          <!-- Form Actions -->
          <div class="row">
            <div class="col-md-12">
              <hr class="mt-4 mb-4">
              <div class="form-group">
                <button 
                  type="submit" 
                  class="btn btn-primary me-2"
                  [disabled]="employeeForm.invalid || isSubmitting">
                  <span *ngIf="isSubmitting" class="spinner-border spinner-border-sm me-1"></span>
                  {{ isSubmitting ? 'Creating...' : 'Create Employee' }}
                </button>
                
                <button 
                  type="button" 
                  class="btn btn-secondary"
                  (click)="resetForm()"
                  [disabled]="isSubmitting">
                  Reset
                </button>
              </div>
            </div>
          </div>

        </form>
      </div>
    </div>
  </div>
</div>
