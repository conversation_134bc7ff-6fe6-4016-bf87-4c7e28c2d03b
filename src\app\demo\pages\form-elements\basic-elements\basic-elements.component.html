<div class="row">
  <div class="col-sm-12">
    <app-card cardTitle="Basic Component" [options]="false">
      <h5>Form controls</h5>
      <hr />
      <div class="row">
        <div class="col-md-6">
          <form action="javascript:">
            <div class="form-group">
              <label for="exampleInputEmail1">Email address</label>
              <input type="email" class="form-control" id="exampleInputEmail1" aria-describedby="emailHelp" placeholder="Enter email" />
              <small id="emailHelp" class="form-text text-muted">We'll never share your email with anyone else.</small>
            </div>
            <div class="form-group">
              <label for="exampleInputPassword1">Password</label>
              <input type="password" class="form-control" id="exampleInputPassword1" placeholder="Password" />
            </div>
            <div class="form-group form-check">
              <input type="checkbox" class="form-check-input" id="exampleCheck1" />
              <label class="form-check-label" for="exampleCheck1">Check me out</label>
            </div>
            <button type="submit" class="btn btn-primary">Submit</button>
          </form>
        </div>
        <div class="col-md-6">
          <form action="javascript:">
            <div class="form-group">
              <label for="text">Text</label>
              <input type="text" class="form-control" placeholder="Text" />
            </div>
            <div class="form-group">
              <label for="exampleFormControlSelect1">Example select</label>
              <select class="form-control" id="exampleFormControlSelect1">
                <option>1</option>
                <option>2</option>
                <option>3</option>
                <option>4</option>
                <option>5</option>
              </select>
            </div>
            <div class="form-group">
              <label for="exampleFormControlTextarea1">Example textarea</label>
              <textarea class="form-control" id="exampleFormControlTextarea1" rows="3"></textarea>
            </div>
          </form>
        </div>
      </div>
      <h5 class="mt-5">Sizing</h5>
      <hr />
      <div class="row">
        <div class="col-md-6">
          <input class="mb-3 form-control form-control-lg" type="text" placeholder=".form-control-lg" />
          <input class="mb-3 form-control" type="text" placeholder="Default input" />
          <input class="mb-3 form-control form-control-sm" type="text" placeholder=".form-control-sm" />
        </div>
        <div class="col-md-6">
          <select class="mb-3 form-control form-control-lg">
            <option>Large select</option>
          </select>
          <select class="mb-3 form-control" title="select">
            <option>Default select</option>
          </select>
        </div>
      </div>
      <div class="col-sm-12">
        <h5 class="mt-5">Datalist</h5>
        <hr />
        <form action="">
          <div class="form-group">
            <label for="exampleDataList" class="form-label">Datalist example</label>
            <input class="form-control" list="datalistOptions" id="exampleDataList" placeholder="Type to search..." />
          </div>
        </form>
      </div>
      <div class="row">
        <div class="col-md-6">
          <h5 class="mt-5">Readonly</h5>
          <hr />
          <input class="form-control" type="text" placeholder="Readonly input here…" readonly />
        </div>
        <div class="col-md-6">
          <h5 class="mt-5">Readonly plain Text</h5>
          <hr />
          <form action="javascript:">
            <div class="form-group row">
              <label for="staticEmail" class="col-sm-3 col-form-label">Email</label>
              <div class="col-sm-9">
                <input type="text" readonly class="form-control-plaintext" id="staticEmail" value="<EMAIL>" />
              </div>
            </div>
            <div class="form-group row">
              <label for="inputPassword" class="col-sm-3 col-form-label">Password</label>
              <div class="col-sm-9">
                <input type="password" class="form-control" id="inputPassword" placeholder="Password" />
              </div>
            </div>
          </form>
        </div>
      </div>
      <h5 class="mt-5">Inline</h5>
      <hr />
      <form action="javascript:" class="row row-cols-md-auto g-3 align-items-center">
        <div class="col-12">
          <label for="inlineFormInputName" class="sr-only">Name</label>
          <input type="text" readonly class="form-control" id="inlineFormInputName" />
        </div>
        <div class="col-12">
          <label for="inlineFormInputGroupUsername" class="sr-only">Username</label>
          <div class="input-group">
            <div class="input-group-text">&#64;</div>
            <input type="text" readonly class="form-control" id="inlineFormInputGroupUsername" placeholder="Username" />
          </div>
        </div>
        <div class="col-12">
          <label for="inlineFormSelectPref" class="sr-only">Preference</label>
          <select class="form-select" id="inlineFormSelectPref">
            <option selected>Choose...</option>
            <option value="1">One</option>
            <option value="2">Two</option>
            <option value="3">Three</option>
          </select>
        </div>
        <div class="col-12">
          <input type="checkbox" class="form-check-input" id="inlineFormCheck" />
          <label for="inlineFormCheck" class="form-check-label px-2">Remember me</label>
        </div>
        <div class="col-12">
          <button type="button" class="btn btn-primary">Submit</button>
        </div>
      </form>
      <h5 class="mt-5">Form Grid</h5>
      <hr />
      <form action="javascript:">
        <div class="row">
          <div class="form-group col-md-6">
            <label for="inputEmail4">Email</label>
            <input type="email" class="form-control" id="inputEmail4" placeholder="Email" />
          </div>
          <div class="form-group col-md-6">
            <label for="inputPassword4">Password</label>
            <input type="password" class="form-control" id="inputPassword4" placeholder="Password" />
          </div>
        </div>
        <div class="form-group">
          <label for="inputAddress">Address</label>
          <input type="text" class="form-control" id="inputAddress" placeholder="1234 Main St" />
        </div>
        <div class="form-group">
          <label for="inputAddress2">Address 2</label>
          <input type="text" class="form-control" id="inputAddress2" placeholder="Apartment, studio, or floor" />
        </div>
        <div class="row">
          <div class="form-group col-md-6">
            <label for="inputCity">City</label>
            <input type="text" class="form-control" id="inputCity" />
          </div>
          <div class="form-group col-md-4">
            <label for="inputState">State</label>
            <select id="inputState" class="form-control">
              <option selected>select</option>
              <option>Large select</option>
            </select>
          </div>
          <div class="form-group col-md-2">
            <label for="inputZip">Zip</label>
            <input type="text" class="form-control" id="inputZip" />
          </div>
        </div>
        <div class="form-group">
          <div class="form-check">
            <input class="form-check-input" type="checkbox" id="gridCheck" />
            <label class="form-check-label" for="gridCheck">Check me out</label>
          </div>
        </div>
        <button type="submit" class="btn btn-primary">Sign in</button>
      </form>
      <div class="row">
        <div class="col-md-6">
          <h5 class="mt-5">Horizontal Form</h5>
          <hr />
          <form action="javascript:">
            <div class="form-group row">
              <label for="inputEmail3" class="col-sm-3 col-form-label">Email</label>
              <div class="col-sm-9">
                <input type="email" class="form-control" id="inputEmail3" placeholder="Email" />
              </div>
            </div>
            <div class="form-group row">
              <label for="inputPassword3" class="col-sm-3 col-form-label">Password</label>
              <div class="col-sm-9">
                <input type="password" class="form-control" id="inputPassword3" placeholder="Password" />
              </div>
            </div>
            <fieldset class="form-group">
              <div class="row">
                <label for="inputPassword3" class="col-sm-3 col-form-label">Radios</label>
                <div class="col-sm-9">
                  <div class="form-check">
                    <input class="form-check-input" type="radio" name="gridRadios" id="gridRadios1" value="option1" checked />
                    <label class="form-check-label" for="gridRadios1">First radio</label>
                  </div>
                  <div class="form-check">
                    <input class="form-check-input" type="radio" name="gridRadios" id="gridRadios2" value="option2" />
                    <label class="form-check-label" for="gridRadios2">Second radio</label>
                  </div>
                  <div class="form-check disabled">
                    <input class="form-check-input" type="radio" name="gridRadios" id="gridRadios3" value="option3" disabled />
                    <label class="form-check-label" for="gridRadios3">Third disabled radio</label>
                  </div>
                </div>
              </div>
            </fieldset>
            <div class="form-group row">
              <div class="col-sm-3">Checkbox</div>
              <div class="col-sm-9">
                <div class="form-check">
                  <input class="form-check-input" type="checkbox" id="gridCheck1" />
                  <label class="form-check-label" for="gridCheck1">Example checkbox</label>
                </div>
              </div>
            </div>
            <div class="form-group row">
              <div class="col-sm-10">
                <button type="submit" class="btn btn-primary">Sign in</button>
              </div>
            </div>
          </form>
        </div>
        <div class="col-md-6">
          <h5 class="mt-5">Horizontal Form Label Sizing</h5>
          <hr />
          <form action="javascript:">
            <div class="form-group row">
              <label for="colFormLabelSm" class="col-sm-3 col-form-label col-form-label-sm">Email</label>
              <div class="col-sm-9">
                <input type="email" class="form-control form-control-sm" id="colFormLabelSm" placeholder="col-form-label-sm" />
              </div>
            </div>
            <div class="form-group row">
              <label for="colFormLabel" class="col-sm-3 col-form-label">Email</label>
              <div class="col-sm-9">
                <input type="email" class="form-control" id="colFormLabel" placeholder="col-form-label" />
              </div>
            </div>
            <div class="form-group row">
              <label for="colFormLabelLg" class="col-sm-3 col-form-label col-form-label-lg">Email</label>
              <div class="col-sm-9">
                <input type="email" class="form-control form-control-lg" id="colFormLabelLg" placeholder="col-form-label-lg" />
              </div>
            </div>
          </form>
        </div>
      </div>
      <h5 class="mt-5">Help Text</h5>
      <hr />
      <label for="inputPassword5">Password</label>
      <input type="password" id="inputPassword5" class="form-control" aria-describedby="passwordHelpBlock" />
      <small id="passwordHelpBlock" class="form-text text-muted">
        Your password must be 8-20 characters long, contain letters and numbers, and must not contain spaces, special characters, or emoji.
      </small>
      <form action="javascript:" class="form-inline">
        <div class="form-group mt-3">
          <label for="inputPassword6">Password</label>
          <input type="password" id="inputPassword6" class="form-control" aria-describedby="passwordHelpInline" />
          <small id="passwordHelpInline" class="text-muted">Must be 8-20 characters long.</small>
        </div>
      </form>
      <h5 class="mt-5">Validation</h5>
      <hr />
      <form action="javascript:" class="needs-validation" novalidate>
        <div class="row">
          <div class="col-md-4 mb-3">
            <label for="validationCustom01">First name</label>
            <input type="text" class="form-control" id="validationCustom01" placeholder="First name" value="Mark" required />
            <div class="valid-feedback">Looks good!</div>
          </div>
          <div class="col-md-4 mb-3">
            <label for="validationCustom02">Last name</label>
            <input type="text" class="form-control" id="validationCustom02" placeholder="Last name" value="Otto" required />
            <div class="valid-feedback">Looks good!</div>
          </div>
          <div class="col-md-4 mb-3">
            <label for="validationTooltipUsername">Username</label>
            <div class="input-group">
              <span class="input-group-text" id="validationTooltipUsernamePrepend">&#64;</span>
              <input
                type="text"
                class="form-control"
                id="validationTooltipUsername"
                placeholder="Username"
                aria-describedby="validationTooltipUsernamePrepend"
                required
              />
              <div class="invalid-feedback">Please choose a username.</div>
            </div>
          </div>
        </div>
        <div class="row">
          <div class="col-md-6 mb-3">
            <label for="validationCustom03">City</label>
            <input type="text" class="form-control" id="validationCustom03" placeholder="City" required />
            <div class="invalid-feedback">Please provide a valid city.</div>
          </div>
          <div class="col-md-3 mb-3">
            <label for="validationCustom04">State</label>
            <input type="text" class="form-control" id="validationCustom04" placeholder="State" required />
            <div class="invalid-feedback">Please provide a valid state.</div>
          </div>
          <div class="col-md-3 mb-3">
            <label for="validationCustom05">Zip</label>
            <input type="text" class="form-control" id="validationCustom05" placeholder="Zip" required />
            <div class="invalid-feedback">Please provide a valid zip.</div>
          </div>
        </div>
        <div class="form-group">
          <div class="form-check">
            <input class="form-check-input" type="checkbox" value="" id="invalidCheck" required />
            <label class="form-check-label" for="invalidCheck">Agree to terms and conditions</label>
            <div class="invalid-feedback">You must agree before submitting.</div>
          </div>
        </div>
        <button class="btn btn-primary" type="submit">Submit form</button>
      </form>
      <h5 class="mt-5">Supported Elements</h5>
      <hr />
      <form class="was-validated">
        <div class="mb-3">
          <label for="validationTextarea" class="form-label">Textarea</label>
          <textarea class="form-control" id="validationTextarea" placeholder="Required example textarea" required></textarea>
          <div class="invalid-feedback">Please enter a message in the textarea.</div>
        </div>

        <div class="form-check mb-3">
          <input type="checkbox" class="form-check-input" id="validationFormCheck1" required />
          <label class="form-check-label" for="validationFormCheck1">Check this checkbox</label>
          <div class="invalid-feedback">Example invalid feedback text</div>
        </div>

        <div class="form-check">
          <input type="radio" class="form-check-input" id="validationFormCheck2" name="radio-stacked" required />
          <label class="form-check-label" for="validationFormCheck2">Toggle this radio</label>
        </div>
        <div class="form-check mb-3">
          <input type="radio" class="form-check-input" id="validationFormCheck3" name="radio-stacked" required />
          <label class="form-check-label" for="validationFormCheck3">Or toggle this other radio</label>
          <div class="invalid-feedback">More example invalid feedback text</div>
        </div>

        <div class="mb-3">
          <select class="form-select" required aria-label="select example">
            <option value="">Open this select menu</option>
            <option value="1">One</option>
            <option value="2">Two</option>
            <option value="3">Three</option>
          </select>
          <div class="invalid-feedback">Example invalid select feedback</div>
        </div>

        <div class="mb-3">
          <input type="file" class="form-control" aria-label="file example" required />
          <div class="invalid-feedback">Example invalid form file feedback</div>
        </div>

        <div class="mb-3">
          <button class="btn btn-primary" type="submit" disabled>Submit form</button>
        </div>
      </form>
      <div class="mb-3">
        <button type="button" class="btn btn-primary" type="submit" disabled>Submitform</button>
      </div>
      <h5 class="mt-5">Tooltips</h5>
      <hr />
      <form action="javascript:" class="needs-validation" novalidate>
        <div class="row">
          <div class="col-md-4 mb-3">
            <label for="validationTooltip01">First name</label>
            <input type="text" class="form-control" id="validationTooltip01" placeholder="First name" value="Mark" required />
            <div class="valid-tooltip">Looks good!</div>
          </div>
          <div class="col-md-4 mb-3">
            <label for="validationTooltip02">Last name</label>
            <input type="text" class="form-control" id="validationTooltip02" placeholder="Last name" value="Otto" required />
            <div class="valid-tooltip">Looks good!</div>
          </div>
          <div class="col-md-4 mb-3">
            <label for="validationTooltipUsername">Username</label>
            <div class="input-group">
              <div class="input-group">
                <span class="input-group-text" id="validationTooltipUsernamePrepend">&#64;</span>
                <input
                  type="text"
                  class="form-control"
                  id="validationTooltipUsername"
                  placeholder="Username"
                  aria-describedby="validationTooltipUsernamePrepend"
                  required
                />
              </div>
              <div class="invalid-tooltip">Please choose a unique and valid username.</div>
            </div>
          </div>
        </div>
        <div class="row">
          <div class="col-md-6 mb-3">
            <label for="validationTooltip03">City</label>
            <input type="text" class="form-control" id="validationTooltip03" placeholder="City" required />
            <div class="invalid-tooltip">Please provide a valid city.</div>
          </div>
          <div class="col-md-3 mb-3">
            <label for="validationTooltip04">State</label>
            <input type="text" class="form-control" id="validationTooltip04" placeholder="State" required />
            <div class="invalid-tooltip">Please provide a valid state.</div>
          </div>
          <div class="col-md-3 mb-3">
            <label for="validationTooltip05">Zip</label>
            <input type="text" class="form-control" id="validationTooltip05" placeholder="Zip" required />
            <div class="invalid-tooltip">Please provide a valid zip.</div>
          </div>
        </div>
        <button class="btn btn-primary" type="submit">Submit form</button>
      </form>
      <h3 class="mt-5">Checkboxes and Radios</h3>
      <h5 class="mt-3">Checkboxes</h5>
      <hr />
      <div class="custom-control custom-checkbox">
        <input type="checkbox" class="custom-control-input" id="customCheck1" />
        <label class="custom-control-label ps-2" for="customCheck1">Check this custom checkbox</label>
      </div>
      <div class="row">
        <div class="col-md-6">
          <h5 class="mt-5">Radios</h5>
          <hr />
          <div class="custom-control custom-radio mb-1">
            <input type="radio" id="customRadio1" name="customRadio" class="custom-control-input" />
            <label class="custom-control-label ps-2" for="customRadio1">Toggle this custom radio</label>
          </div>
          <div class="custom-control custom-radio">
            <input type="radio" id="customRadio2" name="customRadio" class="custom-control-input" />
            <label class="custom-control-label ps-2" for="customRadio2">Or toggle this other custom radio</label>
          </div>
          <h5 class="mt-5">Inline</h5>
          <hr />
          <div class="custom-control custom-radio custom-control-inline mb-1">
            <input type="radio" id="customRadioInline1" name="customRadioInline1" class="custom-control-input" />
            <label class="custom-control-label ps-2" for="customRadioInline1">Toggle this custom radio</label>
          </div>
          <div class="custom-control custom-radio custom-control-inline">
            <input type="radio" id="customRadioInline2" name="customRadioInline1" class="custom-control-input" />
            <label class="custom-control-label ps-2" for="customRadioInline2">Or toggle this other custom radio</label>
          </div>
        </div>
        <div class="col-md-6">
          <h5 class="mt-5">Range</h5>
          <hr />
          <label for="customRange1">Example range</label>
          <input type="range" class="form-range d-block" id="customRange1" />
          <label for="customRange2">Example range</label>
          <input type="range" class="form-range d-block" min="0" max="5" id="customRange2" />
          <label for="customRange3">Example range</label>
          <input type="range" class="form-range d-block" min="0" max="5" step="0.5" id="customRange3" />
        </div>
      </div>
    </app-card>
  </div>
  <div class="col-sm-12">
    <app-card cardTitle="Input Group" [options]="false">
      <div class="input-group mb-3">
        <span class="input-group-text" id="basic-addon1">&#64;</span>
        <input type="text" class="form-control" placeholder="Username" aria-label="Username" aria-describedby="basic-addon1" />
      </div>
      <div class="input-group mb-3">
        <input
          type="text"
          class="form-control"
          placeholder="Recipient's username"
          aria-label="Recipient's username"
          aria-describedby="basic-addon2"
        />
        <span class="input-group-text" id="basic-addon2">&#64;example.com</span>
      </div>
      <label for="basic-url" class="form-label">Your vanity URL</label>
      <div class="input-group mb-3">
        <span class="input-group-text" id="basic-addon3">https://example.com/users/</span>
        <input type="text" class="form-control" id="basic-url" aria-describedby="basic-addon3" />
      </div>
      <div class="input-group mb-3">
        <span class="input-group-text">$</span>
        <input type="text" class="form-control" aria-label="Amount (to the nearest dollar)" />
        <span class="input-group-text">.00</span>
      </div>
      <div class="input-group">
        <span class="input-group-text">With textarea</span>
        <textarea class="form-control" aria-label="With textarea"></textarea>
      </div>
      <div class="row">
        <div class="col-md-6">
          <h5 class="mt-5">Sizing</h5>
          <hr />
          <div class="input-group input-group-sm mb-3">
            <span class="input-group-text" id="inputGroup-sizing-sm">Small</span>
            <input type="text" class="form-control" aria-label="Sizing example input" aria-describedby="inputGroup-sizing-sm" />
          </div>
          <div class="input-group mb-3">
            <span class="input-group-text" id="inputGroup-sizing-default">Default</span>
            <input type="text" class="form-control" aria-label="Sizing example input" aria-describedby="inputGroup-sizing-default" />
          </div>
          <div class="input-group input-group-lg">
            <span class="input-group-text" id="inputGroup-sizing-lg">Large</span>
            <input type="text" class="form-control" aria-label="Sizing example input" aria-describedby="inputGroup-sizing-lg" />
          </div>
        </div>
        <div class="col-md-6">
          <h5 class="mt-5">Checkboxes and radios</h5>
          <hr />
          <div class="input-group mb-3">
            <div class="input-group-text">
              <input class="form-check-input mt-0" type="checkbox" value="" aria-label="Checkbox for following text input" />
            </div>
            <input type="text" class="form-control" aria-label="Text input with checkbox" />
          </div>
          <div class="input-group">
            <div class="input-group-text">
              <input class="form-check-input mt-0" type="radio" value="" aria-label="Radio button for following text input" />
            </div>
            <input type="text" class="form-control" aria-label="Text input with radio button" />
          </div>
        </div>
        <div class="col-md-6">
          <h5 class="mt-5">Multiple inputs</h5>
          <hr />
          <div class="input-group">
            <span class="input-group-text">First and last name</span>
            <input type="text" aria-label="First name" class="form-control" />
            <input type="text" aria-label="Last name" class="form-control" />
          </div>
        </div>
        <div class="col-md-6">
          <h5 class="mt-5">Multiple addons</h5>
          <hr />
          <div class="input-group mb-3">
            <span class="input-group-text">$</span>
            <span class="input-group-text">0.00</span>
            <input type="text" class="form-control" aria-label="Dollar amount (with dot and two decimal places)" />
          </div>

          <div class="input-group">
            <input type="text" class="form-control" aria-label="Dollar amount (with dot and two decimal places)" />
            <span class="input-group-text">$</span>
            <span class="input-group-text">0.00</span>
          </div>
        </div>
      </div>
      <h5 class="mt-5">Button Addons</h5>
      <hr />
      <div class="row">
        <div class="col-md-6">
          <div class="input-group mb-3">
            <button class="btn btn-outline-secondary" type="button" id="button-addon1">Button</button>
            <input
              type="text"
              class="form-control"
              placeholder=""
              aria-label="Example text with button addon"
              aria-describedby="button-addon1"
            />
          </div>

          <div class="input-group mb-3">
            <input
              type="text"
              class="form-control"
              placeholder="Recipient's username"
              aria-label="Recipient's username"
              aria-describedby="button-addon2"
            />
            <button class="btn btn-outline-secondary" type="button" id="button-addon2">Button</button>
          </div>
        </div>
        <div class="col-md-6">
          <div class="input-group mb-3">
            <button class="btn btn-outline-secondary" type="button">Button</button>
            <button class="btn btn-outline-secondary" type="button">Button</button>
            <input type="text" class="form-control" placeholder="" aria-label="Example text with two button addons" />
          </div>
          <div class="input-group">
            <input
              type="text"
              class="form-control"
              placeholder="Recipient's username"
              aria-label="Recipient's username with two button addons"
            />
            <button class="btn btn-outline-secondary" type="button">Button</button>
            <button class="btn btn-outline-secondary" type="button">Button</button>
          </div>
        </div>
        <div class="col-md-6">
          <h5 class="mt-5">Buttons With Dropdowns</h5>
          <hr />
          <div class="input-group mb-3">
            <div class="input-group-prepend" ngbDropdown placement="bottom-start">
              <button class="btn btn-outline-secondary one-dropdown" ngbDropdownToggle type="button">Dropdown</button>
              <div ngbDropdownMenu>
                <a class="dropdown-item" href="javascript:">Action</a>
                <a class="dropdown-item" href="javascript:">Another action</a>
                <a class="dropdown-item" href="javascript:">Something else here</a>
                <div role="separator" class="dropdown-divider"></div>
                <a class="dropdown-item" href="javascript:">Separated link</a>
              </div>
            </div>
            <input type="text" class="form-control" aria-label="Text input with dropdown button" />
          </div>
          <div class="input-group mb-3">
            <input type="text" class="form-control" aria-label="Text input with dropdown button" />
            <div class="input-group-append" ngbDropdown [placement]="'bottom-left'">
              <button class="btn btn-outline-secondary one-dropdown" ngbDropdownToggle type="button">Dropdown</button>
              <div ngbDropdownMenu>
                <a class="dropdown-item" href="javascript:">Action</a>
                <a class="dropdown-item" href="javascript:">Another action</a>
                <a class="dropdown-item" href="javascript:">Something else here</a>
                <div role="separator" class="dropdown-divider"></div>
                <a class="dropdown-item" href="javascript:">Separated link</a>
              </div>
            </div>
          </div>
          <div class="input-group" ngbDropdown placement="bottom-start">
            <button class="btn btn-outline-secondary" type="button" aria-expanded="false" ngbDropdownToggle>Dropdown</button>
            <ul ngbDropdownMenu>
              <li>
                <a class="dropdown-item" href="javascript:">Action before</a>
              </li>
              <li>
                <a class="dropdown-item" href="javascript:">Another action before</a>
              </li>
              <li>
                <a class="dropdown-item" href="javascript:">Something else here</a>
              </li>
              <li><hr class="dropdown-divider" /></li>
              <li>
                <a class="dropdown-item" href="javascript:">Separated link</a>
              </li>
            </ul>
            <input type="text" class="form-control" aria-label="Text input with 2 dropdown buttons" />
            <button class="btn btn-outline-secondary dropdown-toggle" type="button" aria-expanded="false" ngbDropdownToggle>
              Dropdown
            </button>
            <ul class="dropdown-menu dropdown-menu-end" ngbDropdownMenu1>
              <li><a class="dropdown-item" href="javascript:">Action</a></li>
              <li>
                <a class="dropdown-item" href="javascript:">Another action</a>
              </li>
              <li>
                <a class="dropdown-item" href="javascript:">Something else here</a>
              </li>
              <li><hr class="dropdown-divider" /></li>
              <li>
                <a class="dropdown-item" href="javascript:">Separated link</a>
              </li>
            </ul>
          </div>
        </div>
        <div class="col-md-6">
          <h5 class="mt-5">Segmented Buttons</h5>
          <hr />
          <div class="input-group mb-3" ngbDropdown [placement]="'bottom-left'">
            <button type="button" class="btn btn-outline-secondary">Action</button>
            <button type="button" class="btn btn-outline-secondary dropdown-toggle-split" ngbDropdownToggle>
              <span class="sr-only">Toggle Dropdown</span>
            </button>
            <div ngbDropdownMenu>
              <a class="dropdown-item" href="javascript:">Action</a>
              <a class="dropdown-item" href="javascript:">Another action</a>
              <a class="dropdown-item" href="javascript:">Something else here</a>
              <div role="separator" class="dropdown-divider"></div>
              <a class="dropdown-item" href="javascript:">Separated link</a>
            </div>
            <input type="text" class="form-control" aria-label="Text input with segmented dropdown button" />
          </div>
          <div class="input-group" ngbDropdown>
            <input type="text" class="form-control" aria-label="Text input with segmented dropdown button" />
            <button type="button" class="btn btn-outline-secondary">Action</button>
            <button type="button" class="btn btn-outline-secondary dropdown-toggle-split" ngbDropdownToggle>
              <span class="sr-only">Toggle Dropdown</span>
            </button>
            <div ngbDropdownMenu>
              <a class="dropdown-item" href="javascript:">Action</a>
              <a class="dropdown-item" href="javascript:">Another action</a>
              <a class="dropdown-item" href="javascript:">Something else here</a>
              <div role="separator" class="dropdown-divider"></div>
              <a class="dropdown-item" href="javascript:">Separated link</a>
            </div>
          </div>
        </div>
      </div>
      <h3 class="mt-5">Custom Forms</h3>
      <div class="row">
        <div class="col-md-6">
          <h5 class="mt-3">Custom Select</h5>
          <hr />
          <div class="input-group mb-3">
            <div class="input-group-prepend">
              <label class="input-group-text" for="inputGroupSelect01">Options</label>
            </div>
            <select class="form-select" id="inputGroupSelect01">
              <option selected>Choose...</option>
              <option value="1">One</option>
              <option value="2">Two</option>
              <option value="3">Three</option>
            </select>
          </div>
          <div class="input-group mb-3">
            <select class="form-select" id="inputGroupSelect02">
              <option selected>Choose...</option>
              <option value="1">One</option>
              <option value="2">Two</option>
              <option value="3">Three</option>
            </select>
            <div class="input-group-append">
              <label class="input-group-text" for="inputGroupSelect02">Options</label>
            </div>
          </div>
          <div class="input-group mb-3">
            <button class="btn btn-outline-secondary" type="button">Button</button>
            <select class="form-select" id="inputGroupSelect03">
              <option selected>Choose...</option>
              <option value="1">One</option>
              <option value="2">Two</option>
              <option value="3">Three</option>
            </select>
          </div>
          <div class="input-group">
            <select class="form-select" id="inputGroupSelect04">
              <option selected>Choose...</option>
              <option value="1">One</option>
              <option value="2">Two</option>
              <option value="3">Three</option>
            </select>
            <button class="btn btn-outline-secondary" type="button">Button</button>
          </div>
        </div>
        <div class="col-md-6">
          <h5 class="mt-3">Custom File Input</h5>
          <hr />
          <div class="input-group mb-3">
            <label for="inputGroupFile01" class="input-group-text">Upload</label>
            <input type="file" class="form-control" id="inputGroupFile01" />
          </div>
          <div class="input-group mb-3">
            <input type="file" class="form-control" id="inputGroupFile01" />
            <label for="inputGroupFile01" class="input-group-text">Upload</label>
          </div>
          <div class="input-group mb-3">
            <button class="btn btn-outline-secondary" id="inputGroupFileAddon03" type="button">Button</button>
            <input type="file" class="form-control" id="inputGroupFile03" aria-describedby="inputGroupFileAddon03" aria-label="Upload" />
          </div>
          <div class="input-group">
            <input type="file" class="form-control" id="inputGroupFile03" aria-describedby="inputGroupFileAddon03" aria-label="Upload" />
            <button class="btn btn-outline-secondary" id="inputGroupFileAddon03" type="button">Button</button>
          </div>
        </div>
      </div>
    </app-card>
  </div>
</div>
