export interface ContactSubmission {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  phone?: string;
  subject: string;
  message: string;
  status: 'new' | 'in_progress' | 'resolved' | 'closed';
  createdAt: string;
  updatedAt: string;
}

export interface ContactSubmissionResponse {
  success: boolean;
  data: {
    submissions: ContactSubmission[];
    total: number;
    page: number;
    totalPages: number;
  };
}

export interface ContactSubmissionFilters {
  page?: number;
  limit?: number;
  status?: string;
  search?: string;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}
