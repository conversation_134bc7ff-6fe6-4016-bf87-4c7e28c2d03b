// Question form component styles
.form-group {
  margin-bottom: 1.5rem;
}

.form-label {
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.btn {
  margin-right: 0.5rem;
}

.spinner-border-sm {
  width: 1rem;
  height: 1rem;
}

.invalid-feedback {
  display: block;
}

.row.mb-2 {
  align-items: end;
}

.alert {
  margin-bottom: 1rem;
}

// Unified select dropdown styling
.form-select,
select.form-control {
  background-color: white;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 0.75rem center;
  background-repeat: no-repeat;
  background-size: 1.5em 1.5em;
  padding-right: 2.5rem;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  font-size: 0.95rem;
  transition: all 0.3s ease;
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;

  &:focus {
    border-color: #3498db;
    box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
    outline: none;
  }

  &.is-invalid {
    border-color: #dc3545;

    &:focus {
      border-color: #dc3545;
      box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
    }
  }

  // Style for the selected option
  option {
    padding: 0.5rem;
    background-color: white;
    color: #495057;

    &:checked {
      background-color: #3498db;
      color: white;
    }

    &:hover {
      background-color: #f8f9fa;
    }
  }
}

// Loading state styling
.text-muted.small {
  font-size: 0.85rem;
  color: #6c757d !important;

  i {
    animation: spin 1s linear infinite;
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

// Multi-language interface styling
.completion-indicator {
  .badge {
    font-size: 0.8rem;
    padding: 0.5rem 0.75rem;
    border-radius: 20px;

    &.badge-info {
      background-color: #17a2b8;
      color: white;
    }
  }
}

.language-tabs {
  .nav-tabs {
    border-bottom: 2px solid #e9ecef;
    margin-bottom: 0;

    .nav-item {
      margin-bottom: -2px;

      .nav-link {
        border: none;
        border-bottom: 3px solid transparent;
        background: none;
        color: #6c757d;
        padding: 0.75rem 1rem;
        font-weight: 500;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        gap: 0.5rem;
        position: relative;

        .flag-icon {
          font-size: 1.2em;
        }

        .required-indicator {
          color: #dc3545;
          font-weight: bold;
        }

        .completion-check {
          color: #28a745;
          font-size: 0.9em;

          i {
            animation: checkPulse 2s infinite;
          }
        }

        &.completed {
          border-left: 3px solid #28a745;

          &:not(.active) {
            background-color: rgba(40, 167, 69, 0.1);
          }
        }

        &:hover {
          color: #3498db;
          border-bottom-color: #3498db;
          background-color: rgba(52, 152, 219, 0.1);
        }

        &.active {
          color: #3498db;
          border-bottom-color: #3498db;
          background-color: rgba(52, 152, 219, 0.1);
          font-weight: 600;
        }
      }
    }
  }
}

@keyframes checkPulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.1);
  }
}

.tab-content {
  .tab-pane {
    padding: 1rem 0;

    &.active {
      display: block;
    }

    &:not(.active) {
      display: none;
    }
  }
}

// Option container styling
.option-container {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 12px;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  transition: all 0.3s ease;

  &:hover {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border-color: #3498db;
  }

  .option-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid #e9ecef;

    .option-title {
      margin: 0;
      color: #495057;
      font-weight: 600;
      font-size: 1rem;
    }

    .btn {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      font-size: 0.875rem;
    }
  }
}

// Small language tabs for options
.language-tabs-small {
  .btn-group {
    width: 100%;

    .btn {
      flex: 1;
      font-size: 0.75rem;
      padding: 0.375rem 0.5rem;
      border-color: #e9ecef;

      &.active {
        background-color: #3498db;
        border-color: #3498db;
        color: white;
        font-weight: 600;
      }

      &:not(.active) {
        background-color: white;
        color: #6c757d;

        &:hover {
          background-color: rgba(52, 152, 219, 0.1);
          color: #3498db;
        }
      }
    }
  }
}

// Form enhancements
.form-label {
  font-weight: 600;
  color: #495057;
  margin-bottom: 0.5rem;
  font-size: 0.9rem;
}

.form-control {
  transition: all 0.3s ease;

  &:focus {
    border-color: #3498db;
    box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
  }
}

// Responsive design
@media (max-width: 768px) {
  .language-tabs {
    .nav-tabs {
      .nav-item {
        .nav-link {
          padding: 0.5rem 0.75rem;
          font-size: 0.875rem;

          .flag-icon {
            font-size: 1em;
          }
        }
      }
    }
  }

  .option-container {
    padding: 1rem;

    .option-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 0.5rem;

      .btn {
        align-self: flex-end;
      }
    }
  }

  .language-tabs-small {
    .btn-group {
      .btn {
        font-size: 0.7rem;
        padding: 0.25rem 0.375rem;
      }
    }
  }
}

// Animation for smooth transitions
.option-container {
  animation: fadeInUp 0.3s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// JSON Preview styling
.json-preview {
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 1rem;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.875rem;
  line-height: 1.5;
  color: #495057;
  max-height: 400px;
  overflow-y: auto;
  white-space: pre-wrap;
  word-wrap: break-word;

  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;

    &:hover {
      background: #a8a8a8;
    }
  }
}
