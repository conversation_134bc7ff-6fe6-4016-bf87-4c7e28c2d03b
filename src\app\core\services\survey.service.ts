import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';
import { Question } from '../models/question.model';

export interface SurveyAnswer {
  questionId: string;
  selectedOptionId: string;
  selectedOptionText: string;
  selectedOptionValue: number;
}

export interface SurveySession {
  employeeName: string;
  startTime: Date;
  endTime?: Date;
  questions: Question[];
  answers: SurveyAnswer[];
  currentQuestionIndex: number;
  isCompleted: boolean;
}

@Injectable({
  providedIn: 'root'
})
export class SurveyService {
  private surveySession: SurveySession | null = null;
  private surveySessionSubject = new BehaviorSubject<SurveySession | null>(null);

  constructor() {}

  // Get current survey session as observable
  getSurveySession(): Observable<SurveySession | null> {
    return this.surveySessionSubject.asObservable();
  }

  // Reset survey session
  resetSurvey(): void {
    this.surveySession = null;
    this.surveySessionSubject.next(null);
  }

  // Set employee name and initialize session
  setEmployeeName(name: string): void {
    this.surveySession = {
      employeeName: name,
      startTime: new Date(),
      questions: [],
      answers: [],
      currentQuestionIndex: 0,
      isCompleted: false
    };
    this.surveySessionSubject.next(this.surveySession);
  }

  // Get random questions for the survey
  getRandomQuestions(): Question[] {
    // This would typically come from an API
    // For now, using the same dummy data as in questions list
    const allQuestions: Question[] = [
      {
        id: "1",
        question_text: "How satisfied are you with your current work-life balance?",
        options: [
          { id: "1", option_text: "Very Satisfied", option_value: 5 },
          { id: "2", option_text: "Satisfied", option_value: 4 },
          { id: "3", option_text: "Neutral", option_value: 3 },
          { id: "4", option_text: "Dissatisfied", option_value: 2 },
          { id: "5", option_text: "Very Dissatisfied", option_value: 1 }
        ],
        isActive: true,
        createdAt: "2024-01-15T10:00:00Z",
        updatedAt: "2024-01-15T10:00:00Z"
      },
      {
        id: "2",
        question_text: "How would you rate the communication within your team?",
        options: [
          { id: "6", option_text: "Excellent", option_value: 5 },
          { id: "7", option_text: "Good", option_value: 4 },
          { id: "8", option_text: "Average", option_value: 3 },
          { id: "9", option_text: "Poor", option_value: 2 },
          { id: "10", option_text: "Very Poor", option_value: 1 }
        ],
        isActive: true,
        createdAt: "2024-01-16T09:30:00Z",
        updatedAt: "2024-01-16T09:30:00Z"
      },
      {
        id: "3",
        question_text: "How likely are you to recommend this company as a great place to work?",
        options: [
          { id: "11", option_text: "Extremely Likely", option_value: 10 },
          { id: "12", option_text: "Very Likely", option_value: 8 },
          { id: "13", option_text: "Somewhat Likely", option_value: 6 },
          { id: "14", option_text: "Not Very Likely", option_value: 4 },
          { id: "15", option_text: "Not At All Likely", option_value: 2 }
        ],
        isActive: true,
        createdAt: "2024-01-17T11:15:00Z",
        updatedAt: "2024-01-17T11:15:00Z"
      },
      {
        id: "4",
        question_text: "How manageable is your current workload?",
        options: [
          { id: "16", option_text: "Very Manageable", option_value: 5 },
          { id: "17", option_text: "Manageable", option_value: 4 },
          { id: "18", option_text: "Somewhat Manageable", option_value: 3 },
          { id: "19", option_text: "Difficult to Manage", option_value: 2 },
          { id: "20", option_text: "Unmanageable", option_value: 1 }
        ],
        isActive: true,
        createdAt: "2024-01-18T14:20:00Z",
        updatedAt: "2024-01-18T14:20:00Z"
      },
      {
        id: "5",
        question_text: "How satisfied are you with the professional development opportunities?",
        options: [
          { id: "21", option_text: "Very Satisfied", option_value: 5 },
          { id: "22", option_text: "Satisfied", option_value: 4 },
          { id: "23", option_text: "Neutral", option_value: 3 },
          { id: "24", option_text: "Dissatisfied", option_value: 2 },
          { id: "25", option_text: "Very Dissatisfied", option_value: 1 }
        ],
        isActive: true,
        createdAt: "2024-01-19T16:45:00Z",
        updatedAt: "2024-01-19T16:45:00Z"
      }
    ];

    // Get only active questions
    const activeQuestions = allQuestions.filter(q => q.isActive);
    
    // Shuffle and take 3 random questions
    const shuffled = activeQuestions.sort(() => 0.5 - Math.random());
    return shuffled.slice(0, 3);
  }

  // Start the quiz with random questions
  startQuiz(): void {
    if (this.surveySession) {
      this.surveySession.questions = this.getRandomQuestions();
      this.surveySession.currentQuestionIndex = 0;
      this.surveySessionSubject.next(this.surveySession);
    }
  }

  // Get current question
  getCurrentQuestion(): Question | null {
    if (this.surveySession && this.surveySession.questions.length > 0) {
      return this.surveySession.questions[this.surveySession.currentQuestionIndex];
    }
    return null;
  }

  // Submit answer and move to next question
  submitAnswer(questionId: string, optionId: string, optionText: string, optionValue: number): boolean {
    if (!this.surveySession) return false;

    // Save the answer
    const answer: SurveyAnswer = {
      questionId,
      selectedOptionId: optionId,
      selectedOptionText: optionText,
      selectedOptionValue: optionValue
    };

    this.surveySession.answers.push(answer);

    // Move to next question or complete survey
    this.surveySession.currentQuestionIndex++;
    
    if (this.surveySession.currentQuestionIndex >= this.surveySession.questions.length) {
      // Survey completed
      this.surveySession.isCompleted = true;
      this.surveySession.endTime = new Date();
    }

    this.surveySessionSubject.next(this.surveySession);
    return this.surveySession.isCompleted;
  }

  // Get survey progress
  getProgress(): { current: number; total: number; percentage: number } {
    if (!this.surveySession) {
      return { current: 0, total: 0, percentage: 0 };
    }

    const current = this.surveySession.currentQuestionIndex;
    const total = this.surveySession.questions.length;
    const percentage = total > 0 ? Math.round((current / total) * 100) : 0;

    return { current, total, percentage };
  }

  // Get current survey session (non-observable)
  getCurrentSession(): SurveySession | null {
    return this.surveySession;
  }
}
