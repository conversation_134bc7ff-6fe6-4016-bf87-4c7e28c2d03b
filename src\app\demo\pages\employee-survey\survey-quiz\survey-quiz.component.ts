import { Component, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';
import { Subscription } from 'rxjs';
import { SurveyService, SurveySession } from '../../../../core/services/survey.service';
import { Question, QuestionOption } from '../../../../core/models/question.model';

@Component({
  selector: 'app-survey-quiz',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './survey-quiz.component.html',
  styleUrls: ['./survey-quiz.component.scss']
})
export class SurveyQuizComponent implements OnInit, OnDestroy {
  surveySession: SurveySession | null = null;
  currentQuestion: Question | null = null;
  selectedOption: QuestionOption | null = null;
  progress = { current: 0, total: 0, percentage: 0 };
  isSubmitting = false;
  showConfirmation = false;

  private subscription: Subscription = new Subscription();

  constructor(
    private surveyService: SurveyService,
    private router: Router
  ) {}

  ngOnInit(): void {
    // Subscribe to survey session changes
    this.subscription.add(
      this.surveyService.getSurveySession().subscribe(session => {
        this.surveySession = session;
        
        if (!session || !session.employeeName) {
          // Redirect to start if no session
          this.router.navigate(['/employee-survey/start']);
          return;
        }

        // Start quiz if questions not loaded
        if (session.questions.length === 0) {
          this.surveyService.startQuiz();
        }

        // Update current question and progress
        this.updateCurrentQuestion();
        this.updateProgress();
      })
    );
  }

  ngOnDestroy(): void {
    this.subscription.unsubscribe();
  }

  updateCurrentQuestion(): void {
    this.currentQuestion = this.surveyService.getCurrentQuestion();
    this.selectedOption = null;
    this.showConfirmation = false;
  }

  updateProgress(): void {
    this.progress = this.surveyService.getProgress();
  }

  onOptionSelect(option: QuestionOption): void {
    this.selectedOption = option;
    this.showConfirmation = true;
  }

  onConfirmAnswer(): void {
    if (!this.currentQuestion || !this.selectedOption || this.isSubmitting) {
      return;
    }

    this.isSubmitting = true;

    // Simulate a brief delay for better UX
    setTimeout(() => {
      const isCompleted = this.surveyService.submitAnswer(
        this.currentQuestion!.id!,
        this.selectedOption!.id!,
        this.selectedOption!.option_text,
        this.selectedOption!.option_value
      );

      this.isSubmitting = false;

      if (isCompleted) {
        // Survey completed, navigate to completion page
        this.router.navigate(['/employee-survey/complete']);
      } else {
        // Move to next question
        this.updateCurrentQuestion();
        this.updateProgress();
      }
    }, 800);
  }

  onChangeAnswer(): void {
    this.selectedOption = null;
    this.showConfirmation = false;
  }

  getQuestionNumber(): number {
    return this.progress.current + 1;
  }

  getEmployeeName(): string {
    return this.surveySession?.employeeName || 'Employee';
  }

  // Get option letter (A, B, C, etc.)
  getOptionLetter(index: number): string {
    return String.fromCharCode(65 + index); // A=65, B=66, etc.
  }
}
