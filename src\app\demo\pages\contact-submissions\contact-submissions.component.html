<div class="contact-submissions-container">
  <!-- Header Section -->
  <div class="row mb-4">
    <div class="col-12">
      <div class="page-header">
        <div class="d-flex justify-content-between align-items-center">
          <div>
            <h2 class="page-title">
              <i class="feather icon-mail me-2"></i>
              Contact Submissions
            </h2>
            <p class="page-subtitle">Manage and respond to customer inquiries</p>
          </div>
          <!-- <div class="header-actions">
            <button class="btn btn-outline-secondary" (click)="refreshList()" [disabled]="loading">
              <i class="feather icon-refresh-cw me-2"></i>
              Refresh
            </button>
          </div> -->
        </div>
      </div>
    </div>
  </div>

  <!-- Search and Filter Section -->
  <div *ngIf="!loading && !error" class="row mb-4">
    <div class="col-12">
      <div class="search-filter-card">
        <div class="row align-items-center">
          <div class="col-md-4">
            <div class="search-container">
              <div class="search-input-wrapper">
                <i class="feather icon-search search-icon"></i>
                <input
                  type="text"
                  class="form-control search-input"
                  placeholder="Search by name, email, or subject..."
                  [(ngModel)]="searchTerm"
                  (input)="onSearchChange()"
                />
                <button
                  *ngIf="searchTerm"
                  class="btn btn-clear"
                  (click)="clearSearch()"
                  title="Clear search">
                  <i class="feather icon-x"></i>
                </button>
              </div>
            </div>
          </div>
          <div class="col-md-3">
            <!-- <div class="filter-container">
              <label class="filter-label">Status Filter</label>
              <select
                class="form-select filter-select"
                [(ngModel)]="statusFilter"
                (change)="onStatusFilterChange()">
                <option *ngFor="let option of statusOptions" [value]="option.value">
                  {{ option.label }}
                </option>
              </select>
            </div> -->
          </div>
          <div class="col-md-2">
            <!-- <div class="filter-container">
              <label class="filter-label">Per Page</label>
              <select
                class="form-select filter-select"
                [(ngModel)]="pageSize"
                (change)="onSearchChange()">
                <option value="10">10</option>
                <option value="25">25</option>
                <option value="50">50</option>
              </select>
            </div> -->
          </div>
          <div class="col-md-3">
            <div class="search-results">
              <span class="results-text">
                <i class="feather icon-list me-1"></i>
                {{ totalSubmissions }} total submissions
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Loading State -->
  <div *ngIf="loading" class="loading-container">
    <div class="row">
      <div class="col-12">
        <div class="card">
          <div class="card-body text-center py-5">
            <div class="spinner-border text-primary" role="status">
              <span class="visually-hidden">Loading...</span>
            </div>
            <p class="mt-3 mb-0">Loading contact submissions...</p>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Error State -->
  <div *ngIf="error && !loading" class="error-container">
    <div class="row">
      <div class="col-12">
        <div class="alert alert-danger" role="alert">
          <i class="feather icon-alert-circle me-2"></i>
          {{ error }}
          <button class="btn btn-sm btn-outline-danger ms-3" (click)="refreshList()">
            <i class="feather icon-refresh-cw me-1"></i>
            Try Again
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- Submissions Table -->
  <div *ngIf="!loading && !error" class="row">
    <div class="col-12">
      <div class="card">
        <div class="card-body">
          <div class="table-responsive">
            <table class="table table-hover">
              <thead>
                <tr>
                  <th class="sortable" (click)="onSortChange('firstName')">
                    Contact Person
                    <!-- <i [class]="getSortIcon('firstName')"></i> -->
                  </th>
                  <th class="sortable" (click)="onSortChange('email')">
                    Email
                    <!-- <i [class]="getSortIcon('email')"></i> -->
                  </th>
                  <th class="sortable" (click)="onSortChange('subject')">
                    Subject
                    <!-- <i [class]="getSortIcon('subject')"></i> -->
                  </th>
                  <th>Message</th>
                  <!-- <th class="sortable" (click)="onSortChange('status')">
                    Status
                    <i [class]="getSortIcon('status')"></i>
                  </th> -->
                  <th class="sortable" (click)="onSortChange('createdAt')">
                    Date
                    <i [class]="getSortIcon('createdAt')"></i>
                  </th>
                  <!-- <th>Actions</th> -->
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let submission of submissions; trackBy: trackBySubmissionId">
                  <td>
                    <div class="contact-info">
                      <h6 class="contact-name mb-1">{{ getFullName(submission) }}</h6>
                      <small class="text-muted" *ngIf="submission.phone">
                        <i class="feather icon-phone me-1"></i>
                        {{ submission.phone }}
                      </small>
                    </div>
                  </td>
                  <td>
                    <a href="mailto:{{ submission.email }}" class="email-link">
                      {{ submission.email }}
                    </a>
                  </td>
                  <td>
                    <span class="subject-text">{{ submission.subject }}</span>
                  </td>
                  <td>
                    <div class="message-preview" [title]="submission.message">
                      {{ truncateMessage(submission.message) }}
                    </div>
                  </td>
                  <!-- <td>
                    <div class="dropdown">
                      <span 
                        class="badge dropdown-toggle" 
                        [ngClass]="getStatusBadgeClass(submission.status)"
                        data-bs-toggle="dropdown"
                        style="cursor: pointer;">
                        {{ getStatusText(submission.status) }}
                      </span>
                      <ul class="dropdown-menu">
                        <li><a class="dropdown-item" (click)="updateStatus(submission.id, 'new')">New</a></li>
                        <li><a class="dropdown-item" (click)="updateStatus(submission.id, 'in_progress')">In Progress</a></li>
                        <li><a class="dropdown-item" (click)="updateStatus(submission.id, 'resolved')">Resolved</a></li>
                        <li><a class="dropdown-item" (click)="updateStatus(submission.id, 'closed')">Closed</a></li>
                      </ul>
                    </div>
                  </td> -->
                  <td>
                    <span class="created-date">{{ formatDate(submission.createdAt) }}</span>
                  </td>
                  <!-- <td>
                    <div class="action-buttons">
                      <button
                        class="btn btn-sm btn-outline-primary me-1"
                        (click)="viewSubmission(submission.id)"
                        title="View Details">
                        <i class="feather icon-eye"></i>
                      </button>
                      <button
                        class="btn btn-sm btn-outline-danger"
                        (click)="deleteSubmission(submission.id)"
                        title="Delete Submission">
                        <i class="feather icon-trash-2"></i>
                      </button>
                    </div>
                  </td> -->
                </tr>
                <tr *ngIf="submissions.length === 0">
                  <td colspan="7" class="text-center py-4">
                    <div class="no-data">
                      <i class="feather icon-inbox text-muted mb-2" style="font-size: 2rem;"></i>
                      <p class="text-muted mb-0">
                        {{ searchTerm || statusFilter !== 'all' ? 'No submissions found matching your criteria.' : 'No contact submissions found.' }}
                      </p>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>

          <!-- Pagination -->
          <div *ngIf="totalPages > 1" class="pagination-container">
            <nav aria-label="Submissions pagination">
              <ul class="pagination justify-content-center">
                <li class="page-item" [class.disabled]="currentPage === 1">
                  <a class="page-link" (click)="onPageChange(currentPage - 1)" [attr.aria-disabled]="currentPage === 1">
                    <i class="feather icon-chevron-left"></i>
                  </a>
                </li>
                <li *ngFor="let page of getPaginationArray()" 
                    class="page-item" 
                    [class.active]="page === currentPage">
                  <a class="page-link" (click)="onPageChange(page)">{{ page }}</a>
                </li>
                <li class="page-item" [class.disabled]="currentPage === totalPages">
                  <a class="page-link" (click)="onPageChange(currentPage + 1)" [attr.aria-disabled]="currentPage === totalPages">
                    <i class="feather icon-chevron-right"></i>
                  </a>
                </li>
              </ul>
            </nav>
            <div class="pagination-info text-center mt-2">
              <small class="text-muted">
                Showing {{ (currentPage - 1) * pageSize + 1 }} to {{ getEndRange() }} of {{ totalSubmissions }} submissions
              </small>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
