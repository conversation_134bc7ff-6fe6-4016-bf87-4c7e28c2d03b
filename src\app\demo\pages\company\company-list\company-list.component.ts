import { Component, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Router } from '@angular/router';
import { CompanyService, CompanyFilters } from '../../../../core/services/company.service';
import { Subject } from 'rxjs';
import { debounceTime, distinctUntilChanged } from 'rxjs/operators';

export interface Company {
  id: string;
  name: string;
  product_owner_id: string;
  contact_person_firstName: string;
  contact_person_lastName: string;
  contact_person_email: string;
  contact_person_phone?: string;
  address_line: string;
  zipcode: string;
  country: string;
  state: string;
  city: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  createdBy: string;
  updatedBy?: string;
  isDeleted: boolean;
  deletedBy?: string;
}

@Component({
  selector: 'app-company-list',
  standalone: true,
  imports: [CommonModule, FormsModule],
  templateUrl: './company-list.component.html',
  styleUrls: ['./company-list.component.scss']
})
export class CompanyListComponent implements OnInit, OnDestroy {
  companies: Company[] = [];
  filteredCompanies: Company[] = [];
  loading = false;
  error = '';
  searchTerm = '';
  statusFilter = 'all';

  // Pagination properties
  currentPage = 1;
  pageSize = 5;
  totalCompanies = 0;
  totalPages = 0;

  // Filters for API
  filters: CompanyFilters = {
    page: 1,
    limit: 5,
    search: '',
    status: 'all'
  };

  // Debounce search
  private searchSubject = new Subject<string>();

  constructor(
    private companyService: CompanyService,
    private router: Router
  ) {
    // Setup search debouncing
    this.searchSubject.pipe(
      debounceTime(500), // Wait 500ms after user stops typing
      distinctUntilChanged() // Only emit if search term is different
    ).subscribe(searchTerm => {
      this.searchTerm = searchTerm;
      this.currentPage = 1; // Reset to first page when searching
      this.loadCompanies();
    });
  }

  ngOnInit(): void {
    this.loadCompanies();
  }

  ngOnDestroy(): void {
    this.searchSubject.complete();
  }

  loadCompanies(): void {
    this.loading = true;
    this.error = '';

    // Update filters with current search and status
    this.filters = {
      page: this.currentPage,
      limit: this.pageSize,
      search: this.searchTerm.trim(),
      status: this.statusFilter as 'all' | 'active' | 'inactive'
    };

    this.companyService.getCompanies(this.filters).subscribe({
      next: (response) => {
        console.log('Companies from API:', response);
        this.companies = response.companies;
        this.totalCompanies = response.pagination.total;
        this.currentPage = response.pagination.page;
        this.totalPages = response.pagination.totalPages;
        this.pageSize = response.pagination.limit;

        // For paginated API, we don't need client-side filtering
        this.filteredCompanies = [...this.companies];
        this.loading = false;
      },
      error: (error) => {
        console.warn('API failed, using fallback:', error);
        // Fallback to old API method
        this.loadCompaniesWithFallback();
      }
    });
  }

  private loadCompaniesWithFallback(): void {
    this.companyService.getAllCompanies().subscribe({
      next: (data) => {
        this.companies = data;
        this.simulatePaginationWithData();
        this.loading = false;
      },
      error: (error) => {
        this.error = 'Failed to load companies. Please try again.';
        this.loading = false;
        console.error('Error loading companies:', error);
      }
    });
  }

  private simulatePaginationWithData(): void {
    // Apply client-side filtering for fallback data
    let filtered = [...this.companies];

    // Apply search filter
    if (this.searchTerm.trim()) {
      const term = this.searchTerm.toLowerCase().trim();
      filtered = filtered.filter(company =>
        company.name.toLowerCase().includes(term) ||
        company.contact_person_firstName.toLowerCase().includes(term) ||
        company.contact_person_lastName.toLowerCase().includes(term) ||
        company.contact_person_email.toLowerCase().includes(term) ||
        company.city.toLowerCase().includes(term) ||
        company.state.toLowerCase().includes(term) ||
        company.country.toLowerCase().includes(term)
      );
    }

    // Apply status filter
    if (this.statusFilter !== 'all') {
      if (this.statusFilter === 'active') {
        filtered = filtered.filter(company => company.isActive && !company.isDeleted);
      } else if (this.statusFilter === 'inactive') {
        filtered = filtered.filter(company => !company.isActive || company.isDeleted);
      }
    }

    // Calculate pagination
    this.totalCompanies = filtered.length;
    this.totalPages = Math.ceil(this.totalCompanies / this.pageSize);

    // Get current page data
    const startIndex = (this.currentPage - 1) * this.pageSize;
    const endIndex = startIndex + this.pageSize;

    this.companies = filtered.slice(startIndex, endIndex);
    this.filteredCompanies = [...this.companies];
  }

  onSearchChange(searchTerm: string): void {
    // Emit search term to the debounced subject
    this.searchSubject.next(searchTerm);
  }

  onStatusFilterChange(status: string): void {
    this.statusFilter = status;
    this.currentPage = 1; // Reset to first page when filtering
    this.loadCompanies();
  }

  clearSearch(): void {
    this.searchTerm = '';
    this.statusFilter = 'all';
    this.currentPage = 1;
    this.loadCompanies();
  }

  // Pagination Methods
  onPageChange(page: number): void {
    if (page >= 1 && page <= this.totalPages && page !== this.currentPage) {
      this.currentPage = page;
      this.loadCompanies();
    }
  }

  onPageSizeChange(newPageSize: number): void {
    this.pageSize = newPageSize;
    this.currentPage = 1; // Reset to first page when changing page size
    this.loadCompanies();
  }

  getPaginationArray(): number[] {
    const pages: number[] = [];
    const maxVisiblePages = 5;

    if (this.totalPages <= maxVisiblePages) {
      // Show all pages if total pages is less than or equal to max visible
      for (let i = 1; i <= this.totalPages; i++) {
        pages.push(i);
      }
    } else {
      // Show pages around current page
      const startPage = Math.max(1, this.currentPage - Math.floor(maxVisiblePages / 2));
      const endPage = Math.min(this.totalPages, startPage + maxVisiblePages - 1);

      for (let i = startPage; i <= endPage; i++) {
        pages.push(i);
      }
    }

    return pages;
  }

  getStartRange(): number {
    return (this.currentPage - 1) * this.pageSize + 1;
  }

  getEndRange(): number {
    return Math.min(this.currentPage * this.pageSize, this.totalCompanies);
  }

  getTotalCompaniesCount(): number {
    return this.totalCompanies;
  }

  createCompany(): void {
    this.router.navigate(['/company/create']);
  }

  editCompany(companyId: string): void {
    // TODO: Implement edit functionality
    console.log('Edit company:', companyId);
  }

  viewCompany(companyId: string): void {
    // TODO: Implement view functionality
    console.log('View company:', companyId);
  }

  deleteCompany(companyId: string): void {
    if (confirm('Are you sure you want to delete this company?')) {
      // TODO: Implement delete functionality
      console.log('Delete company:', companyId);
    }
  }

  getStatusBadgeClass(company: Company): string {
    if (company.isDeleted) {
      return 'badge-danger';
    }
    return company.isActive ? 'badge-success' : 'badge-warning';
  }

  getStatusText(company: Company): string {
    if (company.isDeleted) {
      return 'Deleted';
    }
    return company.isActive ? 'Active' : 'Inactive';
  }

  formatDate(dateString: string): string {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  }

  getFullContactName(company: Company): string {
    return `${company.contact_person_firstName} ${company.contact_person_lastName}`;
  }

  getFullAddress(company: Company): string {
    return `${company.address_line}, ${company.city}, ${company.state}, ${company.country} ${company.zipcode}`;
  }

  refreshList(): void {
    this.loadCompanies();
  }

  trackByCompanyId(_index: number, company: Company): string {
    return company.id;
  }
}
