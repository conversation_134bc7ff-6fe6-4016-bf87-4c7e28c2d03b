import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Router } from '@angular/router';
import { CompanyService } from '../../../../core/services/company.service';

export interface Company {
  id: string;
  name: string;
  product_owner_id: string;
  contact_person_firstName: string;
  contact_person_lastName: string;
  contact_person_email: string;
  contact_person_phone?: string;
  address_line: string;
  zipcode: string;
  country: string;
  state: string;
  city: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  createdBy: string;
  updatedBy?: string;
  isDeleted: boolean;
  deletedBy?: string;
}

@Component({
  selector: 'app-company-list',
  standalone: true,
  imports: [CommonModule, FormsModule],
  templateUrl: './company-list.component.html',
  styleUrls: ['./company-list.component.scss']
})
export class CompanyListComponent implements OnInit {
  companies: Company[] = [];
  filteredCompanies: Company[] = [];
  loading = false;
  error = '';
  searchTerm = '';
  statusFilter = 'all';

  constructor(
    private companyService: CompanyService,
    private router: Router
  ) {}

  ngOnInit(): void {
    this.loadCompanies();
  }

  loadCompanies(): void {
    this.loading = true;
    this.error = '';
    
    this.companyService.getCompanies().subscribe({
      next: (data) => {
        this.companies = data;
        this.filteredCompanies = [...this.companies];
        this.loading = false;
        this.applyFilters();
      },
      error: (error) => {
        this.error = 'Failed to load companies. Please try again.';
        this.loading = false;
        console.error('Error loading companies:', error);
      }
    });
  }

  onSearchChange(searchTerm: string): void {
    this.searchTerm = searchTerm;
    this.applyFilters();
  }

  onStatusFilterChange(status: string): void {
    this.statusFilter = status;
    this.applyFilters();
  }

  applyFilters(): void {
    let filtered = [...this.companies];

    // Apply search filter
    if (this.searchTerm.trim()) {
      const term = this.searchTerm.toLowerCase().trim();
      filtered = filtered.filter(company =>
        company.name.toLowerCase().includes(term) ||
        company.contact_person_firstName.toLowerCase().includes(term) ||
        company.contact_person_lastName.toLowerCase().includes(term) ||
        company.contact_person_email.toLowerCase().includes(term) ||
        company.city.toLowerCase().includes(term) ||
        company.state.toLowerCase().includes(term) ||
        company.country.toLowerCase().includes(term)
      );
    }

    // Apply status filter
    if (this.statusFilter !== 'all') {
      if (this.statusFilter === 'active') {
        filtered = filtered.filter(company => company.isActive && !company.isDeleted);
      } else if (this.statusFilter === 'inactive') {
        filtered = filtered.filter(company => !company.isActive || company.isDeleted);
      }
    }

    this.filteredCompanies = filtered;
  }

  clearSearch(): void {
    this.searchTerm = '';
    this.applyFilters();
  }

  createCompany(): void {
    this.router.navigate(['/company/create']);
  }

  editCompany(companyId: string): void {
    // TODO: Implement edit functionality
    console.log('Edit company:', companyId);
  }

  viewCompany(companyId: string): void {
    // TODO: Implement view functionality
    console.log('View company:', companyId);
  }

  deleteCompany(companyId: string): void {
    if (confirm('Are you sure you want to delete this company?')) {
      // TODO: Implement delete functionality
      console.log('Delete company:', companyId);
    }
  }

  getStatusBadgeClass(company: Company): string {
    if (company.isDeleted) {
      return 'badge-danger';
    }
    return company.isActive ? 'badge-success' : 'badge-warning';
  }

  getStatusText(company: Company): string {
    if (company.isDeleted) {
      return 'Deleted';
    }
    return company.isActive ? 'Active' : 'Inactive';
  }

  formatDate(dateString: string): string {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  }

  getFullContactName(company: Company): string {
    return `${company.contact_person_firstName} ${company.contact_person_lastName}`;
  }

  getFullAddress(company: Company): string {
    return `${company.address_line}, ${company.city}, ${company.state}, ${company.country} ${company.zipcode}`;
  }

  refreshList(): void {
    this.loadCompanies();
  }

  trackByCompanyId(index: number, company: Company): string {
    return company.id;
  }
}
