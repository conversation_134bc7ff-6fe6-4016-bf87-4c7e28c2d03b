<!doctype html>
<html lang="en">
  <head>
    <title>Employee Survey App | Professional Workplace Feedback</title>

    <!-- HTML5 Shim and Respond.js IE11 support of HTML5 elements and media queries -->
    <!-- WARNING: Respond.js doesn't work if you view the page via file:// -->
    <!--[if lt IE 11]>
      <script src="https://oss.maxcdn.com/libs/html5shiv/3.7.0/html5shiv.js"></script>
      <script src="https://oss.maxcdn.com/libs/respond.js/1.4.2/respond.min.js"></script>
    <![endif]-->

    <base href="/" />

    <!-- Meta -->
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=0, minimal-ui" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta
      name="description"
      content="Professional employee survey application for collecting workplace feedback. Mobile-first design with offline capabilities."
    />
    <meta
      name="keywords"
      content="employee survey, workplace feedback, PWA, mobile survey, offline survey, employee engagement"
    />
    <meta name="author" content="Survey App Team" />

    <!-- PWA Meta Tags -->
    <meta name="theme-color" content="#667eea" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="default" />
    <meta name="apple-mobile-web-app-title" content="Survey App" />
    <meta name="msapplication-TileColor" content="#667eea" />
    <meta name="msapplication-config" content="browserconfig.xml" />

    <link rel="icon" type="image/x-icon" href="favicon.ico" />

    <!-- PWA Manifest -->
    <link rel="manifest" href="manifest.json" />

    <!-- Apple Touch Icons -->
    <link rel="apple-touch-icon" href="favicon.ico" />
    <link rel="apple-touch-icon" sizes="152x152" href="favicon.ico" />
    <link rel="apple-touch-icon" sizes="180x180" href="favicon.ico" />

    <!-- font style -->
    <link href="https://fonts.googleapis.com/css?family=Open+Sans:300,400,600" rel="stylesheet" />
  </head>
  <body>
    <app-root></app-root>

    <!-- Service Worker Registration -->
    <script>
      if ('serviceWorker' in navigator) {
        window.addEventListener('load', () => {
          navigator.serviceWorker.register('/sw.js')
            .then((registration) => {
              console.log('SW registered: ', registration);
            })
            .catch((registrationError) => {
              console.log('SW registration failed: ', registrationError);
            });
        });
      }
    </script>
  </body>
</html>
