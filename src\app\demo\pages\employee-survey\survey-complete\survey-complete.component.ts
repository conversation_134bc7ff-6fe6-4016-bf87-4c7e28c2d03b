import { Component, OnInit, OnD<PERSON>roy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';
import { SurveyService, SurveySession } from '../../../../core/services/survey.service';

@Component({
  selector: 'app-survey-complete',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './survey-complete.component.html',
  styleUrls: ['./survey-complete.component.scss']
})
export class SurveyCompleteComponent implements OnInit, OnDestroy {
  surveySession: SurveySession | null = null;
  showDetails = false;
  countdown: number = 5;
  private countdownInterval: any;

  constructor(
    private surveyService: SurveyService,
    private router: Router
  ) {}

  ngOnInit(): void {
    this.surveySession = this.surveyService.getCurrentSession();

    if (!this.surveySession || !this.surveySession.isCompleted) {
      // Redirect to start if no completed session
      this.router.navigate(['/employee-survey/start']);
      return;
    }

    // Start countdown for auto-redirect
    this.startCountdown();
  }

  ngOnDestroy(): void {
    if (this.countdownInterval) {
      clearInterval(this.countdownInterval);
    }
  }

  private startCountdown(): void {
    this.countdownInterval = setInterval(() => {
      this.countdown--;
      if (this.countdown <= 0) {
        this.startNewSurvey();
      }
    }, 1000);
  }

  getEmployeeName(): string {
    return this.surveySession?.employeeName || 'Employee';
  }

  getDuration(): string {
    if (!this.surveySession?.startTime || !this.surveySession?.endTime) {
      return 'N/A';
    }

    const start = new Date(this.surveySession.startTime);
    const end = new Date(this.surveySession.endTime);
    const durationMs = end.getTime() - start.getTime();
    const minutes = Math.floor(durationMs / 60000);
    const seconds = Math.floor((durationMs % 60000) / 1000);

    if (minutes > 0) {
      return `${minutes}m ${seconds}s`;
    } else {
      return `${seconds}s`;
    }
  }

  getCompletionTime(): string {
    if (!this.surveySession?.endTime) {
      return 'N/A';
    }

    return new Date(this.surveySession.endTime).toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: true
    });
  }

  toggleDetails(): void {
    this.showDetails = !this.showDetails;
  }

  startNewSurvey(): void {
    // Clear countdown when manually starting new survey
    if (this.countdownInterval) {
      clearInterval(this.countdownInterval);
    }
    this.surveyService.resetSurvey();
    this.router.navigate(['/employee-survey/start']);
  }

  goToQuestions(): void {
    this.router.navigate(['/questions']);
  }

  getAnswerSummary(): { question: string; answer: string; value: number }[] {
    if (!this.surveySession) return [];

    return this.surveySession.answers.map(answer => {
      const question = this.surveySession!.questions.find(q => q.id === answer.questionId);
      return {
        question: question?.question_text || 'Unknown Question',
        answer: answer.selectedOptionText,
        value: answer.selectedOptionValue
      };
    });
  }

  getAverageScore(): number {
    if (!this.surveySession || this.surveySession.answers.length === 0) {
      return 0;
    }

    const totalScore = this.surveySession.answers.reduce((sum, answer) => sum + answer.selectedOptionValue, 0);
    return Math.round((totalScore / this.surveySession.answers.length) * 10) / 10;
  }

  getScoreColor(): string {
    const score = this.getAverageScore();
    if (score >= 4) return '#00b894'; // Green
    if (score >= 3) return '#fdcb6e'; // Yellow
    return '#e17055'; // Red
  }

  getScoreLabel(): string {
    const score = this.getAverageScore();
    if (score >= 4) return 'Excellent';
    if (score >= 3) return 'Good';
    return 'Needs Attention';
  }
}
