// Mobile-First Survey Container
.survey-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

// Header Styling
.survey-header {
  background: white;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  padding: 1rem;
  position: sticky;
  top: 0;
  z-index: 100;

  .header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 400px;
    margin: 0 auto;
  }

  .company-info {
    .company-name {
      font-size: 1.5rem;
      font-weight: 700;
      color: #2c3e50;
      margin: 0;
      letter-spacing: 1px;
    }
  }

  .time-info {
    .current-time {
      font-size: 1.2rem;
      font-weight: 600;
      color: #34495e;
    }
  }

  .menu-icon {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: #3498db;
    font-weight: 600;

    i {
      font-size: 1.2rem;
    }

    .menu-text {
      font-size: 1rem;
      font-weight: 700;
    }
  }
}

// Content Area
.survey-content {
  padding: 2rem 1rem;
  max-width: 400px;
  margin: 0 auto;
  min-height: calc(100vh - 80px);
}

// Responsive Design
@media (max-width: 480px) {
  .survey-header {
    padding: 0.75rem;

    .company-info .company-name {
      font-size: 1.3rem;
    }

    .time-info .current-time {
      font-size: 1.1rem;
    }
  }

  .survey-content {
    padding: 1.5rem 0.75rem;
  }
}

@media (min-width: 768px) {
  .survey-content {
    max-width: 500px;
    padding: 3rem 2rem;
  }
}
