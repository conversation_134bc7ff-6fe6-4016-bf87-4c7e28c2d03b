import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from '../../../environments/environment';
import { ContactSubmission, ContactSubmissionResponse, ContactSubmissionFilters } from '../models/contact-submission.model';

@Injectable({
  providedIn: 'root'
})
export class ContactSubmissionService {
  private apiUrl = environment.apiUrl;

  constructor(private http: HttpClient) {}

  /**
   * Get contact submissions with pagination and filters
   */
  getSubmissions(filters: ContactSubmissionFilters = {}): Observable<ContactSubmissionResponse> {
    let params = new HttpParams();
    
    if (filters.page) {
      params = params.set('page', filters.page.toString());
    }
    if (filters.limit) {
      params = params.set('limit', filters.limit.toString());
    }
    if (filters.status) {
      params = params.set('status', filters.status);
    }
    if (filters.search) {
      params = params.set('search', filters.search);
    }
    if (filters.sortBy) {
      params = params.set('sortBy', filters.sortBy);
    }
    if (filters.sortOrder) {
      params = params.set('sortOrder', filters.sortOrder);
    }

    return this.http.get<ContactSubmissionResponse>(`${this.apiUrl}/contact/submissions`, { params });
  }

  /**
   * Get a specific contact submission by ID
   */
  getSubmission(id: string): Observable<ContactSubmission> {
    return this.http.get<ContactSubmission>(`${this.apiUrl}/contact/submissions/${id}`);
  }

  /**
   * Update submission status
   */
  updateSubmissionStatus(id: string, status: string): Observable<ContactSubmission> {
    return this.http.patch<ContactSubmission>(`${this.apiUrl}/contact/submissions/${id}/status`, { status });
  }

  /**
   * Delete a contact submission
   */
  deleteSubmission(id: string): Observable<any> {
    return this.http.delete(`${this.apiUrl}/contact/submissions/${id}`);
  }

  /**
   * Mark submission as read
   */
  markAsRead(id: string): Observable<ContactSubmission> {
    return this.http.patch<ContactSubmission>(`${this.apiUrl}/contact/submissions/${id}/read`, {});
  }

  /**
   * Get submission statistics
   */
  getSubmissionStats(): Observable<any> {
    return this.http.get(`${this.apiUrl}/contact/submissions/stats`);
  }
}
