<div class="container-fluid">
  <div class="row">
    <div class="col-12">
      <div class="card">
        <div class="card-header">
          <div class="d-flex justify-content-between align-items-center">
            <h5 class="mb-0">{{ isEditMode ? 'Edit Question' : 'Create New Question' }}</h5>
            <div class="completion-indicator">
              <span class="badge badge-info">
                <i class="feather icon-globe me-1"></i>
                {{ getOverallCompletionPercentage() }}% Complete
              </span>
            </div>
          </div>
        </div>
        <div class="card-body">
          <div *ngIf="loading" class="text-center">
            <div class="spinner-border" role="status">
              <span class="sr-only">Loading...</span>
            </div>
          </div>

          <div *ngIf="error" class="alert alert-danger">{{ error }}</div>

          <form  [formGroup]="questionForm" (ngSubmit)="onSubmit()" *ngIf="!loading">
            <div class="form-group mb-3">
              <label class="form-label">Question Text</label>

              <!-- Language Tabs -->
              <div class="language-tabs mb-3">
                <ul class="nav nav-tabs" role="tablist">
                  <li class="nav-item" *ngFor="let lang of supportedLanguages" role="presentation">
                    <button
                      class="nav-link"
                      [class.active]="activeLanguageTab === lang.code"
                      [class.completed]="getLanguageCompletionStatus(lang.code).question && getLanguageCompletionStatus(lang.code).options"
                      type="button"
                      (click)="switchLanguageTab(lang.code)">
                      <span class="flag-icon">{{ lang.flag }}</span>
                      {{ lang.name }}
                      <span class="required-indicator" *ngIf="lang.code === 'en'">*</span>
                      <span class="completion-check" *ngIf="getLanguageCompletionStatus(lang.code).question && getLanguageCompletionStatus(lang.code).options">
                        <i class="feather icon-check-circle"></i>
                      </span>
                    </button>
                  </li>
                </ul>
              </div>

              <!-- Question Text Inputs -->
              <div class="tab-content" formGroupName="question_text">
                <div
                  class="tab-pane"
                  [class.active]="activeLanguageTab === lang.code"
                  *ngFor="let lang of supportedLanguages">
                  <div *ngIf="activeLanguageTab === lang.code">
                    <textarea
                      [id]="'question_text_' + lang.code"
                      [formControlName]="lang.code"
                      class="form-control"
                      [class.is-invalid]="getQuestionTextControl(lang.code)?.touched && getQuestionTextControl(lang.code)?.errors"
                      rows="3"
                      [placeholder]="'Enter your question in ' + lang.name + '...'">
                    </textarea>
                    <div *ngIf="getQuestionTextControl(lang.code)?.touched && getQuestionTextControl(lang.code)?.errors" class="invalid-feedback">
                      <div *ngIf="getQuestionTextControl(lang.code)?.errors?.['required']">Question text in {{ lang.name }} is required</div>
                      <div *ngIf="getQuestionTextControl(lang.code)?.errors?.['minlength']">Question text must be at least 5 characters long</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div class="form-group mb-3">
              <label class="form-label">Answer Options</label>
              <div formArrayName="options">
                <div *ngFor="let option of options.controls; let i = index" [formGroupName]="i" class="mb-4 option-container">
                  <div class="option-header">
                    <h6 class="option-title">Option {{ i + 1 }}</h6>
                    <button
                      type="button"
                      class="btn btn-outline-danger btn-sm"
                      (click)="removeOption(i)"
                      [disabled]="options.length <= 2">
                      <i class="feather icon-trash-2"></i>
                      Remove
                    </button>
                  </div>

                  <!-- Option Text Multi-language -->
                  <div class="row mb-3">
                    <div class="col-md-6">
                      <label class="form-label">Option Text</label>

                      <!-- Language Tabs for Options -->
                      <div class="language-tabs-small mb-2">
                        <div class="btn-group btn-group-sm" role="group">
                          <button
                            *ngFor="let lang of supportedLanguages"
                            type="button"
                            class="btn btn-outline-secondary"
                            [class.active]="activeLanguageTab === lang.code"
                            (click)="switchLanguageTab(lang.code)">
                            {{ lang.flag }} {{ lang.code.toUpperCase() }}
                          </button>
                        </div>
                      </div>

                      <!-- Option Text Inputs -->
                      <div formGroupName="option_text">
                        <div *ngFor="let lang of supportedLanguages">
                          <div *ngIf="activeLanguageTab === lang.code">
                            <input
                              type="text"
                              [formControlName]="lang.code"
                              class="form-control"
                              [class.is-invalid]="getOptionTextControl(i, lang.code)?.touched && getOptionTextControl(i, lang.code)?.errors"
                              [placeholder]="'Option text in ' + lang.name">
                            <div *ngIf="getOptionTextControl(i, lang.code)?.touched && getOptionTextControl(i, lang.code)?.errors" class="invalid-feedback">
                              Option text in {{ lang.name }} is required
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                    <!-- <div class="col-md-3">
                      <label class="form-label">Score Value</label>
                      <input
                        type="number"
                        formControlName="option_value"
                        class="form-control"
                        [class.is-invalid]="option.get('option_value')?.touched && option.get('option_value')?.errors"
                        placeholder="Score value"
                        min="0">
                      <div *ngIf="option.get('option_value')?.touched && option.get('option_value')?.errors" class="invalid-feedback">
                        <div *ngIf="option.get('option_value')?.errors?.['required']">Score is required</div>
                        <div *ngIf="option.get('option_value')?.errors?.['min']">Score must be 0 or greater</div>
                      </div>
                    </div> -->
                    

                    <div class="col-md-6">
                      <label class="form-label">Category</label>
                      <select
                        formControlName="categoryId"
                        class="form-control form-select"
                        [class.is-invalid]="option.get('categoryId')?.touched && option.get('categoryId')?.errors"
                        (change)="onCategoryChange(i, $any($event.target).value)">
                        <option value="">Select Category</option>
                        <option *ngFor="let category of categories" [value]="category.id">
                          {{category.name}}
                        </option>
                      </select>
                      <div *ngIf="option.get('categoryId')?.touched && option.get('categoryId')?.errors" class="invalid-feedback">
                        Category selection is required
                      </div>
                      <div *ngIf="loadingCategories" class="text-muted small mt-1">
                        <i class="feather icon-loader me-1"></i>
                        Loading categories...
                      </div>
                      <small class="text-muted mt-1">
                        <i class="feather icon-info me-1"></i>
                        Score value will be automatically set based on category
                      </small>
                    </div>
                  </div>
                </div>
              </div>
              <button type="button" class="btn btn-outline-primary mt-2" (click)="addOption()">
                Add Option
              </button>
            </div>

            <div class="form-group">
              <button
                type="submit"
                class="btn btn-primary me-2"
                [disabled]="questionForm.invalid || submitting">
                <span *ngIf="submitting" class="spinner-border spinner-border-sm me-1"></span>
                {{ isEditMode ? 'Update Question' : 'Create Question' }}
              </button>
              <button
                type="button"
                class="btn btn-secondary me-2"
                (click)="router.navigate(['/questions'])">
                Cancel
              </button>
              <button
                type="button"
                class="btn btn-outline-info"
                (click)="showPreview = !showPreview">
                <i class="feather icon-eye me-1"></i>
                {{ showPreview ? 'Hide' : 'Show' }} JSON Preview
              </button>
            </div>

            <!-- JSON Preview -->
            <div *ngIf="showPreview" class="mt-4">
              <div class="card">
                <div class="card-header">
                  <h6 class="mb-0">
                    <i class="feather icon-code me-2"></i>
                    Multi-language JSON Structure
                  </h6>
                </div>
                <div class="card-body">
                  <pre class="json-preview">{{ getFormDataAsJson() }}</pre>
                </div>
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</div>
