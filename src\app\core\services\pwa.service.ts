import { Injectable } from '@angular/core';
import { BehaviorSubject } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class PwaService {
  private promptEvent: any;
  private isInstallable = new BehaviorSubject<boolean>(false);
  public isInstallable$ = this.isInstallable.asObservable();

  constructor() {
    this.initPwaPrompt();
  }

  private initPwaPrompt(): void {
    console.log('Initializing PWA prompt listeners...');

    window.addEventListener('beforeinstallprompt', (event: any) => {
      console.log('beforeinstallprompt event fired', event);
      event.preventDefault();
      this.promptEvent = event;
      this.isInstallable.next(true);
    });

    window.addEventListener('appinstalled', () => {
      console.log('PWA was installed');
      this.isInstallable.next(false);
      this.promptEvent = null;
    });

    // Check if already installed
    if (window.matchMedia && window.matchMedia('(display-mode: standalone)').matches) {
      console.log('PWA is already installed');
      this.isInstallable.next(false);
    }

    // Debug: Check if service worker is registered
    if ('serviceWorker' in navigator) {
      navigator.serviceWorker.ready.then(() => {
        console.log('Service Worker is ready');
      });
    }
  }

  public async installPwa(): Promise<boolean> {
    if (!this.promptEvent) {
      return false;
    }

    try {
      this.promptEvent.prompt();
      const result = await this.promptEvent.userChoice;
      
      if (result.outcome === 'accepted') {
        this.isInstallable.next(false);
        this.promptEvent = null;
        return true;
      }
      return false;
    } catch (error) {
      console.error('Error installing PWA:', error);
      return false;
    }
  }

  public checkForUpdate(): void {
    // Basic update check - in a full implementation, this would check for new versions
    console.log('Checked for updates');
  }

  public isOnline(): boolean {
    return navigator.onLine;
  }

  public getInstallPromptEvent(): any {
    return this.promptEvent;
  }
}
