// Create Employee Component Styles

.card {
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  border: none;

  .card-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 8px 8px 0 0;
    
    h5 {
      margin: 0;
      font-weight: 600;
    }
  }

  .card-body {
    padding: 2rem;
  }
}

.form-group {
  margin-bottom: 1.5rem;

  .form-label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
  }

  .form-control {
    border: 1px solid #ced4da;
    border-radius: 4px;
    padding: 0.75rem;
    font-size: 0.9rem;
    transition: all 0.2s ease;

    &:focus {
      border-color: #667eea;
      box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
      outline: none;
    }

    &.is-invalid {
      border-color: #dc3545;
      box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
    }
  }

  select.form-control {
    background-color: white;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 0.75rem center;
    background-repeat: no-repeat;
    background-size: 1.5em 1.5em;
    padding-right: 2.5rem;
  }

  .form-check {
    padding-left: 1.5rem;

    .form-check-input {
      margin-left: -1.5rem;
      margin-top: 0.25rem;
    }

    .form-check-label {
      font-weight: 500;
      color: #495057;
      cursor: pointer;
    }
  }

  .form-text {
    font-size: 0.8rem;
    color: #6c757d;
    margin-top: 0.25rem;
  }

  .invalid-feedback {
    color: #dc3545;
    font-size: 0.8rem;
    margin-top: 0.25rem;
    font-weight: 500;
    display: block;
  }
}

// Section headers
h6.text-muted {
  font-weight: 600;
  color: #6c757d !important;
  border-bottom: 1px solid #e9ecef;
  padding-bottom: 0.5rem;
  margin-bottom: 1rem !important;
}

// Form actions
.btn {
  padding: 0.75rem 1.5rem;
  font-weight: 600;
  border-radius: 6px;
  transition: all 0.2s ease;

  &.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;

    &:hover:not(:disabled) {
      background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
      transform: translateY(-1px);
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    }
  }

  &.btn-secondary {
    background: #6c757d;
    border: none;

    &:hover:not(:disabled) {
      background: #5a6268;
      transform: translateY(-1px);
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    }
  }

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
  }

  .spinner-border-sm {
    width: 1rem;
    height: 1rem;
  }
}

// Responsive design
@media (max-width: 768px) {
  .card-body {
    padding: 1rem;
  }

  .btn {
    width: 100%;
    margin-bottom: 0.5rem;
  }
}

// Animation for supervisor section
.row {
  transition: all 0.3s ease;
}

// Hover effects for form elements
.form-control:hover:not(:focus):not(.is-invalid) {
  border-color: #adb5bd;
}

.form-check:hover .form-check-input:not(:checked) {
  border-color: #667eea;
}
