{"$schema": "./node_modules/@angular/service-worker/config/schema.json", "index": "/index.html", "assetGroups": [{"name": "app", "installMode": "prefetch", "resources": {"files": ["/favicon.ico", "/index.html", "/manifest.json", "/*.css", "/*.js"]}}, {"name": "assets", "installMode": "lazy", "updateMode": "prefetch", "resources": {"files": ["/assets/**", "/*.(eot|svg|cur|jpg|png|webp|gif|otf|ttf|woff|woff2|ani)"]}}], "dataGroups": [{"name": "api-performance", "urls": ["/api/**"], "cacheConfig": {"strategy": "performance", "maxSize": 100, "maxAge": "3d"}}, {"name": "api-freshness", "urls": ["/api/survey/**", "/api/questions/**"], "cacheConfig": {"strategy": "freshness", "maxSize": 100, "maxAge": "1h"}}], "navigationUrls": ["/**", "!/**/*.*", "!/**/*__*", "!/**/*__*/**"]}