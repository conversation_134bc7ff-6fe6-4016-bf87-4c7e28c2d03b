(function () {
  var a;
  window.AmCharts
    ? (a = window.AmCharts)
    : ((a = {}),
      (window.AmCharts = a),
      (a.themes = {}),
      (a.maps = {}),
      (a.inheriting = {}),
      (a.charts = []),
      (a.onReadyArray = []),
      (a.useUTC = !1),
      (a.updateRate = 60),
      (a.uid = 0),
      (a.lang = {}),
      (a.translations = {}),
      (a.mapTranslations = {}),
      (a.windows = {}),
      (a.initHandlers = []),
      (a.amString = 'am'),
      (a.pmString = 'pm'));
  a.Class = function (e) {
    var d = function () {
      arguments[0] !== a.inheriting && ((this.events = {}), this.construct.apply(this, arguments));
    };
    e.inherits
      ? ((d.prototype = new e.inherits(a.inheriting)), (d.base = e.inherits.prototype), delete e.inherits)
      : ((d.prototype.createEvents = function () {
          for (var b = 0; b < arguments.length; b++) {
            this.events[arguments[b]] = [];
          }
        }),
        (d.prototype.listenTo = function (h, g, i) {
          this.removeListener(h, g, i);
          h.events[g].push({ handler: i, scope: this });
        }),
        (d.prototype.addListener = function (h, g, i) {
          this.removeListener(this, h, g);
          h && this.events[h] && this.events[h].push({ handler: g, scope: i });
        }),
        (d.prototype.removeListener = function (h, g, i) {
          if (h && h.events && (h = h.events[g])) {
            for (g = h.length - 1; 0 <= g; g--) {
              h[g].handler === i && h.splice(g, 1);
            }
          }
        }),
        (d.prototype.fire = function (h) {
          for (var g = this.events[h.type], j = 0; j < g.length; j++) {
            var i = g[j];
            i.handler.call(i.scope, h);
          }
        }));
    for (var f in e) {
      d.prototype[f] = e[f];
    }
    return d;
  };
  a.addChart = function (b) {
    window.requestAnimationFrame
      ? a.animationRequested || ((a.animationRequested = !0), window.requestAnimationFrame(a.update))
      : a.updateInt ||
        (a.updateInt = setInterval(
          function () {
            a.update();
          },
          Math.round(1000 / a.updateRate)
        ));
    a.charts.push(b);
  };
  a.removeChart = function (e) {
    for (var d = a.charts, f = d.length - 1; 0 <= f; f--) {
      d[f] == e && d.splice(f, 1);
    }
    0 === d.length &&
      (a.requestAnimation && (window.cancelAnimationFrame(a.requestAnimation), (a.animationRequested = !1)),
      a.updateInt && (clearInterval(a.updateInt), (a.updateInt = NaN)));
  };
  a.isModern = !0;
  a.getIEVersion = function () {
    var e = 0,
      d,
      f;
    'Microsoft Internet Explorer' == navigator.appName &&
      ((d = navigator.userAgent), (f = /MSIE ([0-9]{1,}[.0-9]{0,})/), null !== f.exec(d) && (e = parseFloat(RegExp.$1)));
    return e;
  };
  a.applyLang = function (e, d) {
    var f = a.translations;
    d.dayNames = a.extend({}, a.dayNames);
    d.shortDayNames = a.extend({}, a.shortDayNames);
    d.monthNames = a.extend({}, a.monthNames);
    d.shortMonthNames = a.extend({}, a.shortMonthNames);
    d.amString = 'am';
    d.pmString = 'pm';
    f &&
      (f = f[e]) &&
      ((a.lang = f),
      (d.langObj = f),
      f.monthNames &&
        ((d.dayNames = a.extend({}, f.dayNames)),
        (d.shortDayNames = a.extend({}, f.shortDayNames)),
        (d.monthNames = a.extend({}, f.monthNames)),
        (d.shortMonthNames = a.extend({}, f.shortMonthNames))),
      f.am && (d.amString = f.am),
      f.pm && (d.pmString = f.pm));
    a.amString = d.amString;
    a.pmString = d.pmString;
  };
  a.IEversion = a.getIEVersion();
  9 > a.IEversion && 0 < a.IEversion && ((a.isModern = !1), (a.isIE = !0));
  a.dx = 0;
  a.dy = 0;
  if (document.addEventListener || window.opera) {
    (a.isNN = !0), (a.isIE = !1), (a.dx = 0.5), (a.dy = 0.5);
  }
  document.attachEvent && ((a.isNN = !1), (a.isIE = !0), a.isModern || ((a.dx = 0), (a.dy = 0)));
  window.chrome && (a.chrome = !0);
  a.handleMouseUp = function (f) {
    for (var d = a.charts, h = 0; h < d.length; h++) {
      var g = d[h];
      g && g.handleReleaseOutside && g.handleReleaseOutside(f);
    }
  };
  a.handleMouseMove = function (f) {
    for (var d = a.charts, h = 0; h < d.length; h++) {
      var g = d[h];
      g && g.handleMouseMove && g.handleMouseMove(f);
    }
  };
  a.handleWheel = function (f) {
    for (var d = a.charts, h = 0; h < d.length; h++) {
      var g = d[h];
      if (g && g.mouseIsOver) {
        (g.mouseWheelScrollEnabled || g.mouseWheelZoomEnabled) && g.handleWheel && g.handleWheel(f);
        break;
      }
    }
  };
  a.resetMouseOver = function () {
    for (var e = a.charts, d = 0; d < e.length; d++) {
      var f = e[d];
      f && (f.mouseIsOver = !1);
    }
  };
  a.ready = function (b) {
    a.onReadyArray.push(b);
  };
  a.handleLoad = function () {
    a.isReady = !0;
    for (var e = a.onReadyArray, d = 0; d < e.length; d++) {
      var f = e[d];
      isNaN(a.processDelay) ? f() : setTimeout(f, a.processDelay * d);
    }
    a.onReadyArray = [];
  };
  a.addInitHandler = function (d, c) {
    a.initHandlers.push({ method: d, types: c });
  };
  a.callInitHandler = function (f) {
    var d = a.initHandlers;
    if (a.initHandlers) {
      for (var h = 0; h < d.length; h++) {
        var g = d[h];
        g.types ? a.isInArray(g.types, f.type) && g.method(f) : g.method(f);
      }
    }
  };
  a.getUniqueId = function () {
    a.uid++;
    return 'AmChartsEl-' + a.uid;
  };
  a.isNN &&
    (document.addEventListener('mousemove', a.handleMouseMove),
    document.addEventListener('mouseup', a.handleMouseUp, !0),
    window.addEventListener('load', a.handleLoad, !0));
  a.isIE &&
    (document.attachEvent('onmousemove', a.handleMouseMove),
    document.attachEvent('onmouseup', a.handleMouseUp),
    window.attachEvent('onload', a.handleLoad));
  a.addWheelListeners = function () {
    a.wheelIsListened ||
      (a.isNN && (window.addEventListener('DOMMouseScroll', a.handleWheel, !0), document.addEventListener('mousewheel', a.handleWheel, !0)),
      a.isIE && document.attachEvent('onmousewheel', a.handleWheel));
    a.wheelIsListened = !0;
  };
  a.clear = function () {
    var d = a.charts;
    if (d) {
      for (var c = d.length - 1; 0 <= c; c--) {
        d[c].clear();
      }
    }
    a.updateInt && clearInterval(a.updateInt);
    a.requestAnimation && window.cancelAnimationFrame(a.requestAnimation);
    a.charts = [];
    a.isNN &&
      (document.removeEventListener('mousemove', a.handleMouseMove, !0),
      document.removeEventListener('mouseup', a.handleMouseUp, !0),
      window.removeEventListener('load', a.handleLoad, !0),
      window.removeEventListener('DOMMouseScroll', a.handleWheel, !0),
      document.removeEventListener('mousewheel', a.handleWheel, !0));
    a.isIE &&
      (document.detachEvent('onmousemove', a.handleMouseMove),
      document.detachEvent('onmouseup', a.handleMouseUp),
      window.detachEvent('onload', a.handleLoad));
  };
  a.makeChart = function (h, d, l) {
    var k = d.type,
      j = d.theme;
    a.isString(j) && ((j = a.themes[j]), (d.theme = j));
    var i;
    switch (k) {
      case 'serial':
        i = new a.AmSerialChart(j);
        break;
      case 'xy':
        i = new a.AmXYChart(j);
        break;
      case 'pie':
        i = new a.AmPieChart(j);
        break;
      case 'radar':
        i = new a.AmRadarChart(j);
        break;
      case 'gauge':
        i = new a.AmAngularGauge(j);
        break;
      case 'funnel':
        i = new a.AmFunnelChart(j);
        break;
      case 'map':
        i = new a.AmMap(j);
        break;
      case 'stock':
        i = new a.AmStockChart(j);
        break;
      case 'gantt':
        i = new a.AmGanttChart(j);
    }
    a.extend(i, d);
    a.isReady
      ? isNaN(l)
        ? i.write(h)
        : setTimeout(function () {
            a.realWrite(i, h);
          }, l)
      : a.ready(function () {
          isNaN(l)
            ? i.write(h)
            : setTimeout(function () {
                a.realWrite(i, h);
              }, l);
        });
    return i;
  };
  a.realWrite = function (d, c) {
    d.write(c);
  };
  a.updateCount = 0;
  a.validateAt = Math.round(a.updateRate / 10);
  a.update = function () {
    var e = a.charts;
    a.updateCount++;
    var d = !1;
    a.updateCount == a.validateAt && ((d = !0), (a.updateCount = 0));
    if (e) {
      for (var f = e.length - 1; 0 <= f; f--) {
        e[f].update && e[f].update(),
          d && (e[f].autoResize ? e[f].validateSize && e[f].validateSize() : e[f].premeasure && e[f].premeasure());
      }
    }
    window.requestAnimationFrame && (a.requestAnimation = window.requestAnimationFrame(a.update));
  };
  'complete' == document.readyState && a.handleLoad();
})();
(function () {
  var a = window.AmCharts;
  a.toBoolean = function (d, c) {
    if (void 0 === d) {
      return c;
    }
    switch (String(d).toLowerCase()) {
      case 'true':
      case 'yes':
      case '1':
        return !0;
      case 'false':
      case 'no':
      case '0':
      case null:
        return !1;
      default:
        return !!d;
    }
  };
  a.removeFromArray = function (e, d) {
    var f;
    if (void 0 !== d && void 0 !== e) {
      for (f = e.length - 1; 0 <= f; f--) {
        e[f] == d && e.splice(f, 1);
      }
    }
  };
  a.getPath = function () {
    var e = document.getElementsByTagName('script');
    if (e) {
      for (var d = 0; d < e.length; d++) {
        var f = e[d].src;
        if (-1 !== f.search(/\/(amcharts|ammap)\.js/)) {
          return f.replace(/\/(amcharts|ammap)\.js.*/, '/');
        }
      }
    }
  };
  a.normalizeUrl = function (b) {
    return '' !== b && -1 === b.search(/\/$/) ? b + '/' : b;
  };
  a.isAbsolute = function (b) {
    return 0 === b.search(/^http[s]?:|^\//);
  };
  a.isInArray = function (e, d) {
    for (var f = 0; f < e.length; f++) {
      if (e[f] == d) {
        return !0;
      }
    }
    return !1;
  };
  a.getDecimals = function (d) {
    var c = 0;
    isNaN(d) ||
      ((d = String(d)), -1 != d.indexOf('e-') ? (c = Number(d.split('-')[1])) : -1 != d.indexOf('.') && (c = d.split('.')[1].length));
    return c;
  };
  a.wordwrap = function (i, d, p, o) {
    var n, m, l, j;
    i += '';
    if (1 > d) {
      return i;
    }
    n = -1;
    for (i = (j = i.split(/\r\n|\n|\r/)).length; ++n < i; j[n] += l) {
      l = j[n];
      for (j[n] = ''; l.length > d; j[n] += a.trim(l.slice(0, m)) + ((l = l.slice(m)).length ? p : '')) {
        m =
          2 == o || (m = l.slice(0, d + 1).match(/\S*(\s)?$/))[1]
            ? d
            : m.input.length - m[0].length || (1 == o && d) || m.input.length + (m = l.slice(d).match(/^\S*/))[0].length;
      }
      l = a.trim(l);
    }
    return j.join(p);
  };
  a.trim = function (b) {
    return b.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g, '');
  };
  a.wrappedText = function (v, u, t, s, r, q, p, o) {
    var j = a.text(v, u, t, s, r, q, p);
    if (j) {
      var i = j.getBBox();
      if (i.width > o) {
        var d = '\n';
        a.isModern || (d = '<br>');
        o = Math.floor(o / (i.width / u.length));
        2 < o && (o -= 2);
        u = a.wordwrap(u, o, d, !0);
        j.remove();
        j = a.text(v, u, t, s, r, q, p);
      }
    }
    return j;
  };
  a.getStyle = function (f, d) {
    var h = '';
    if (document.defaultView && document.defaultView.getComputedStyle) {
      try {
        h = document.defaultView.getComputedStyle(f, '').getPropertyValue(d);
      } catch (g) {}
    } else {
      f.currentStyle &&
        ((d = d.replace(/\-(\w)/g, function (e, c) {
          return c.toUpperCase();
        })),
        (h = f.currentStyle[d]));
    }
    return h;
  };
  a.removePx = function (b) {
    if (void 0 !== b) {
      return Number(b.substring(0, b.length - 2));
    }
  };
  a.getURL = function (e, d) {
    if (e) {
      if ('_self' != d && d) {
        if ('_top' == d && window.top) {
          window.top.location.href = e;
        } else {
          if ('_parent' == d && window.parent) {
            window.parent.location.href = e;
          } else {
            if ('_blank' == d) {
              window.open(e);
            } else {
              var f = document.getElementsByName(d)[0];
              f
                ? (f.src = e)
                : (f = a.windows[d])
                  ? f.opener && !f.opener.closed
                    ? (f.location.href = e)
                    : (a.windows[d] = window.open(e))
                  : (a.windows[d] = window.open(e));
            }
          }
        }
      } else {
        window.location.href = e;
      }
    }
  };
  a.ifArray = function (b) {
    return b && 'object' == typeof b && 0 < b.length ? !0 : !1;
  };
  a.callMethod = function (i, f) {
    var n;
    for (n = 0; n < f.length; n++) {
      var l = f[n];
      if (l) {
        if (l[i]) {
          l[i]();
        }
        var m = l.length;
        if (0 < m) {
          var k;
          for (k = 0; k < m; k++) {
            var j = l[k];
            if (j && j[i]) {
              j[i]();
            }
          }
        }
      }
    }
  };
  a.toNumber = function (b) {
    return 'number' == typeof b ? b : Number(String(b).replace(/[^0-9\-.]+/g, ''));
  };
  a.toColor = function (e) {
    if ('' !== e && void 0 !== e) {
      if (-1 != e.indexOf(',')) {
        e = e.split(',');
        var d;
        for (d = 0; d < e.length; d++) {
          var f = e[d].substring(e[d].length - 6, e[d].length);
          e[d] = '#' + f;
        }
      } else {
        (e = e.substring(e.length - 6, e.length)), (e = '#' + e);
      }
    }
    return e;
  };
  a.toCoordinate = function (f, d, h) {
    var g;
    void 0 !== f &&
      ((f = String(f)),
      h && h < d && (d = h),
      (g = Number(f)),
      -1 != f.indexOf('!') && (g = d - Number(f.substr(1))),
      -1 != f.indexOf('%') && (g = (d * Number(f.substr(0, f.length - 1))) / 100));
    return g;
  };
  a.fitToBounds = function (e, d, f) {
    e < d && (e = d);
    e > f && (e = f);
    return e;
  };
  a.isDefined = function (b) {
    return void 0 === b ? !1 : !0;
  };
  a.stripNumbers = function (b) {
    return b.replace(/[0-9]+/g, '');
  };
  a.roundTo = function (e, d) {
    if (0 > d) {
      return e;
    }
    var f = Math.pow(10, d);
    return Math.round(e * f) / f;
  };
  a.toFixed = function (h, f) {
    var l = !1;
    0 > h && ((l = !0), (h = Math.abs(h)));
    var j = String(Math.round(h * Math.pow(10, f)));
    if (0 < f) {
      var k = j.length;
      if (k < f) {
        var i;
        for (i = 0; i < f - k; i++) {
          j = '0' + j;
        }
      }
      k = j.substring(0, j.length - f);
      '' === k && (k = 0);
      j = k + '.' + j.substring(j.length - f, j.length);
      return l ? '-' + j : j;
    }
    return String(j);
  };
  a.formatDuration = function (r, q, p, o, n, m) {
    var j = a.intervals,
      i = m.decimalSeparator;
    if (r >= j[q].contains) {
      var d = r - Math.floor(r / j[q].contains) * j[q].contains;
      'ss' == q ? ((d = a.formatNumber(d, m)), 1 == d.split(i)[0].length && (d = '0' + d)) : (d = a.roundTo(d, m.precision));
      ('mm' == q || 'hh' == q) && 10 > d && (d = '0' + d);
      p = d + '' + o[q] + '' + p;
      r = Math.floor(r / j[q].contains);
      q = j[q].nextInterval;
      return a.formatDuration(r, q, p, o, n, m);
    }
    'ss' == q && ((r = a.formatNumber(r, m)), 1 == r.split(i)[0].length && (r = '0' + r));
    ('mm' == q || 'hh' == q) && 10 > r && (r = '0' + r);
    p = r + '' + o[q] + '' + p;
    if (j[n].count > j[q].count) {
      for (r = j[q].count; r < j[n].count; r++) {
        (q = j[q].nextInterval), 'ss' == q || 'mm' == q || 'hh' == q ? (p = '00' + o[q] + '' + p) : 'DD' == q && (p = '0' + o[q] + '' + p);
      }
    }
    ':' == p.charAt(p.length - 1) && (p = p.substring(0, p.length - 1));
    return p;
  };
  a.formatNumber = function (v, u, t, s, r) {
    v = a.roundTo(v, u.precision);
    isNaN(t) && (t = u.precision);
    var q = u.decimalSeparator;
    u = u.thousandsSeparator;
    var p;
    p = 0 > v ? '-' : '';
    v = Math.abs(v);
    var o = String(v),
      j = !1;
    -1 != o.indexOf('e') && (j = !0);
    0 <= t && !j && (o = a.toFixed(v, t));
    var i = '';
    if (j) {
      i = o;
    } else {
      var o = o.split('.'),
        j = String(o[0]),
        d;
      for (d = j.length; 0 <= d; d -= 3) {
        i = d != j.length ? (0 !== d ? j.substring(d - 3, d) + u + i : j.substring(d - 3, d) + i) : j.substring(d - 3, d);
      }
      void 0 !== o[1] && (i = i + q + o[1]);
      void 0 !== t && 0 < t && '0' != i && (i = a.addZeroes(i, q, t));
    }
    i = p + i;
    '' === p && !0 === s && 0 !== v && (i = '+' + i);
    !0 === r && (i += '%');
    return i;
  };
  a.addZeroes = function (e, d, f) {
    e = e.split(d);
    void 0 === e[1] && 0 < f && (e[1] = '0');
    return e[1].length < f ? ((e[1] += '0'), a.addZeroes(e[0] + d + e[1], d, f)) : void 0 !== e[1] ? e[0] + d + e[1] : e[0];
  };
  a.scientificToNormal = function (f) {
    var d;
    f = String(f).split('e');
    var h;
    if ('-' == f[1].substr(0, 1)) {
      d = '0.';
      for (h = 0; h < Math.abs(Number(f[1])) - 1; h++) {
        d += '0';
      }
      d += f[0].split('.').join('');
    } else {
      var g = 0;
      d = f[0].split('.');
      d[1] && (g = d[1].length);
      d = f[0].split('.').join('');
      for (h = 0; h < Math.abs(Number(f[1])) - g; h++) {
        d += '0';
      }
    }
    return d;
  };
  a.toScientific = function (f, d) {
    if (0 === f) {
      return '0';
    }
    var h = Math.floor(Math.log(Math.abs(f)) * Math.LOG10E),
      g = String(g).split('.').join(d);
    return String(g) + 'e' + h;
  };
  a.randomColor = function () {
    return '#' + ('00000' + ((16777216 * Math.random()) << 0).toString(16)).substr(-6);
  };
  a.hitTest = function (r, q, p) {
    var o = !1,
      n = r.x,
      m = r.x + r.width,
      j = r.y,
      i = r.y + r.height,
      d = a.isInRectangle;
    o || (o = d(n, j, q));
    o || (o = d(n, i, q));
    o || (o = d(m, j, q));
    o || (o = d(m, i, q));
    o || !0 === p || (o = a.hitTest(q, r, !0));
    return o;
  };
  a.isInRectangle = function (e, d, f) {
    return e >= f.x - 5 && e <= f.x + f.width + 5 && d >= f.y - 5 && d <= f.y + f.height + 5 ? !0 : !1;
  };
  a.isPercents = function (b) {
    if (-1 != String(b).indexOf('%')) {
      return !0;
    }
  };
  a.formatValue = function (v, u, t, s, r, q, p, o) {
    if (u) {
      void 0 === r && (r = '');
      var j;
      for (j = 0; j < t.length; j++) {
        var i = t[j],
          d = u[i];
        void 0 !== d &&
          ((d = q ? a.addPrefix(d, o, p, s) : a.formatNumber(d, s)), (v = v.replace(new RegExp('\\[\\[' + r + '' + i + '\\]\\]', 'g'), d)));
      }
    }
    return v;
  };
  a.formatDataContextValue = function (g, f) {
    if (g) {
      var j = g.match(/\[\[.*?\]\]/g),
        h;
      for (h = 0; h < j.length; h++) {
        var i = j[h],
          i = i.substr(2, i.length - 4);
        void 0 !== f[i] && (g = g.replace(new RegExp('\\[\\[' + i + '\\]\\]', 'g'), f[i]));
      }
    }
    return g;
  };
  a.massReplace = function (f, d) {
    for (var h in d) {
      if (d.hasOwnProperty(h)) {
        var g = d[h];
        void 0 === g && (g = '');
        f = f.replace(h, g);
      }
    }
    return f;
  };
  a.cleanFromEmpty = function (b) {
    return b.replace(/\[\[[^\]]*\]\]/g, '');
  };
  a.addPrefix = function (t, s, r, q, p) {
    var o = a.formatNumber(t, q),
      n = '',
      j,
      i,
      d;
    if (0 === t) {
      return '0';
    }
    0 > t && (n = '-');
    t = Math.abs(t);
    if (1 < t) {
      for (j = s.length - 1; -1 < j; j--) {
        if (
          t >= s[j].number &&
          ((i = t / s[j].number),
          (d = Number(q.precision)),
          1 > d && (d = 1),
          (r = a.roundTo(i, d)),
          (d = a.formatNumber(r, {
            precision: -1,
            decimalSeparator: q.decimalSeparator,
            thousandsSeparator: q.thousandsSeparator
          })),
          !p || i == r)
        ) {
          o = n + '' + d + '' + s[j].prefix;
          break;
        }
      }
    } else {
      for (j = 0; j < r.length; j++) {
        if (t <= r[j].number) {
          i = t / r[j].number;
          d = Math.abs(Math.floor(Math.log(i) * Math.LOG10E));
          i = a.roundTo(i, d);
          o = n + '' + i + '' + r[j].prefix;
          break;
        }
      }
    }
    return o;
  };
  a.remove = function (b) {
    b && b.remove();
  };
  a.getEffect = function (b) {
    '>' == b && (b = 'easeOutSine');
    '<' == b && (b = 'easeInSine');
    'elastic' == b && (b = 'easeOutElastic');
    return b;
  };
  a.getObjById = function (g, f) {
    var j, h;
    for (h = 0; h < g.length; h++) {
      var i = g[h];
      if (i.id == f) {
        j = i;
        break;
      }
    }
    return j;
  };
  a.applyTheme = function (f, d, h) {
    d || (d = a.theme);
    try {
      d = JSON.parse(JSON.stringify(d));
    } catch (g) {}
    d && d[h] && a.extend(f, d[h]);
  };
  a.isString = function (b) {
    return 'string' == typeof b ? !0 : !1;
  };
  a.extend = function (f, d, h) {
    var g;
    f || (f = {});
    for (g in d) {
      h ? f.hasOwnProperty(g) || (f[g] = d[g]) : (f[g] = d[g]);
    }
    return f;
  };
  a.copyProperties = function (e, d) {
    for (var f in e) {
      e.hasOwnProperty(f) && 'events' != f && void 0 !== e[f] && 'function' != typeof e[f] && 'cname' != f && (d[f] = e[f]);
    }
  };
  a.processObject = function (g, d, j, i) {
    if (!1 === g instanceof d && ((g = i ? a.extend(new d(j), g) : a.extend(g, new d(j), !0)), g.listeners)) {
      for (var h in g.listeners) {
        (d = g.listeners[h]), g.addListener(d.event, d.method);
      }
    }
    return g;
  };
  a.fixNewLines = function (d) {
    var c = RegExp('\\n', 'g');
    d && (d = d.replace(c, '<br />'));
    return d;
  };
  a.fixBrakes = function (d) {
    if (a.isModern) {
      var c = RegExp('<br>', 'g');
      d && (d = d.replace(c, '\n'));
    } else {
      d = a.fixNewLines(d);
    }
    return d;
  };
  a.deleteObject = function (f, d) {
    if (f) {
      if (void 0 === d || null === d) {
        d = 20;
      }
      if (0 !== d) {
        if ('[object Array]' === Object.prototype.toString.call(f)) {
          for (var h = 0; h < f.length; h++) {
            a.deleteObject(f[h], d - 1), (f[h] = null);
          }
        } else {
          if (f && !f.tagName) {
            try {
              for (h in ((f.theme = null), f)) {
                f[h] && ('object' == typeof f[h] && a.deleteObject(f[h], d - 1), 'function' != typeof f[h] && (f[h] = null));
              }
            } catch (g) {}
          }
        }
      }
    }
  };
  a.bounce = function (g, f, j, h, i) {
    return (f /= i) < 1 / 2.75
      ? 7.5625 * h * f * f + j
      : f < 2 / 2.75
        ? h * (7.5625 * (f -= 1.5 / 2.75) * f + 0.75) + j
        : f < 2.5 / 2.75
          ? h * (7.5625 * (f -= 2.25 / 2.75) * f + 0.9375) + j
          : h * (7.5625 * (f -= 2.625 / 2.75) * f + 0.984375) + j;
  };
  a.easeInOutQuad = function (g, f, j, h, i) {
    f /= i / 2;
    if (1 > f) {
      return (h / 2) * f * f + j;
    }
    f--;
    return (-h / 2) * (f * (f - 2) - 1) + j;
  };
  a.easeInSine = function (g, f, j, h, i) {
    return -h * Math.cos((f / i) * (Math.PI / 2)) + h + j;
  };
  a.easeOutSine = function (g, f, j, h, i) {
    return h * Math.sin((f / i) * (Math.PI / 2)) + j;
  };
  a.easeOutElastic = function (i, f, n, l, m) {
    i = 1.70158;
    var k = 0,
      j = l;
    if (0 === f) {
      return n;
    }
    if (1 == (f /= m)) {
      return n + l;
    }
    k || (k = 0.3 * m);
    j < Math.abs(l) ? ((j = l), (i = k / 4)) : (i = (k / (2 * Math.PI)) * Math.asin(l / j));
    return j * Math.pow(2, -10 * f) * Math.sin((2 * (f * m - i) * Math.PI) / k) + l + n;
  };
  a.fixStepE = function (d) {
    d = d.toExponential(0).split('e');
    var c = Number(d[1]);
    9 == Number(d[0]) && c++;
    return a.generateNumber(1, c);
  };
  a.generateNumber = function (g, f) {
    var j = '',
      h;
    h = 0 > f ? Math.abs(f) - 1 : Math.abs(f);
    var i;
    for (i = 0; i < h; i++) {
      j += '0';
    }
    return 0 > f ? Number('0.' + j + String(g)) : Number(String(g) + j);
  };
  a.setCN = function (g, f, j, h) {
    if (g.addClassNames && f && (f = f.node) && j) {
      var i = f.getAttribute('class');
      g = g.classNamePrefix + '-';
      h && (g = '');
      i ? f.setAttribute('class', i + ' ' + g + j) : f.setAttribute('class', g + j);
    }
  };
  a.removeCN = function (e, d, f) {
    d && (d = d.node) && f && (d = d.classList) && d.remove(e.classNamePrefix + '-' + f);
  };
  a.parseDefs = function (g, d) {
    for (var j in g) {
      var i = typeof g[j];
      if (0 < g[j].length && 'object' == i) {
        for (var h = 0; h < g[j].length; h++) {
          (i = document.createElementNS(a.SVG_NS, j)), d.appendChild(i), a.parseDefs(g[j][h], i);
        }
      } else {
        'object' == i ? ((i = document.createElementNS(a.SVG_NS, j)), d.appendChild(i), a.parseDefs(g[j], i)) : d.setAttribute(j, g[j]);
      }
    }
  };
})();
(function () {
  var a = window.AmCharts;
  a.AmDraw = a.Class({
    construct: function (f, d, h, g) {
      a.SVG_NS = 'http://www.w3.org/2000/svg';
      a.SVG_XLINK = 'http://www.w3.org/1999/xlink';
      a.hasSVG = !!document.createElementNS && !!document.createElementNS(a.SVG_NS, 'svg').createSVGRect;
      1 > d && (d = 10);
      1 > h && (h = 10);
      this.div = f;
      this.width = d;
      this.height = h;
      this.rBin = document.createElement('div');
      a.hasSVG
        ? ((a.SVG = !0),
          (d = this.createSvgElement('svg')),
          f.appendChild(d),
          (this.container = d),
          this.addDefs(g),
          (this.R = new a.SVGRenderer(this)))
        : a.isIE &&
          a.VMLRenderer &&
          ((a.VML = !0),
          a.vmlStyleSheet ||
            (document.namespaces.add('amvml', 'urn:schemas-microsoft-com:vml'),
            31 > document.styleSheets.length
              ? ((d = document.createStyleSheet()),
                d.addRule('.amvml', 'behavior:url(#default#VML); display:inline-block; antialias:true'),
                (a.vmlStyleSheet = d))
              : document.styleSheets[0].addRule('.amvml', 'behavior:url(#default#VML); display:inline-block; antialias:true')),
          (this.container = f),
          (this.R = new a.VMLRenderer(this, g)),
          this.R.disableSelection(f));
    },
    createSvgElement: function (b) {
      return document.createElementNS(a.SVG_NS, b);
    },
    circle: function (g, d, j, i) {
      var h = new a.AmDObject('circle', this);
      h.attr({ r: j, cx: g, cy: d });
      this.addToContainer(h.node, i);
      return h;
    },
    ellipse: function (h, d, l, k, j) {
      var i = new a.AmDObject('ellipse', this);
      i.attr({ rx: l, ry: k, cx: h, cy: d });
      this.addToContainer(i.node, j);
      return i;
    },
    setSize: function (d, c) {
      0 < d && 0 < c && ((this.container.style.width = d + 'px'), (this.container.style.height = c + 'px'));
    },
    rect: function (i, d, p, o, n, m, l) {
      var j = new a.AmDObject('rect', this);
      a.VML &&
        ((n = Math.round((100 * n) / Math.min(p, o))),
        (p += 2 * m),
        (o += 2 * m),
        (j.bw = m),
        (j.node.style.marginLeft = -m),
        (j.node.style.marginTop = -m));
      1 > p && (p = 1);
      1 > o && (o = 1);
      j.attr({
        x: i,
        y: d,
        width: p,
        height: o,
        rx: n,
        ry: n,
        'stroke-width': m
      });
      this.addToContainer(j.node, l);
      return j;
    },
    image: function (i, d, n, m, l, k) {
      var j = new a.AmDObject('image', this);
      j.attr({ x: d, y: n, width: m, height: l });
      this.R.path(j, i);
      this.addToContainer(j.node, k);
      return j;
    },
    addToContainer: function (d, c) {
      c || (c = this.container);
      c.appendChild(d);
    },
    text: function (e, d, f) {
      return this.R.text(e, d, f);
    },
    path: function (g, d, j, i) {
      var h = new a.AmDObject('path', this);
      i || (i = '100,100');
      h.attr({ cs: i });
      j ? h.attr({ dd: g }) : h.attr({ d: g });
      this.addToContainer(h.node, d);
      return h;
    },
    set: function (b) {
      return this.R.set(b);
    },
    remove: function (d) {
      if (d) {
        var c = this.rBin;
        c.appendChild(d);
        c.innerHTML = '';
      }
    },
    renderFix: function () {
      var h = this.container,
        f = h.style;
      f.top = '0px';
      f.left = '0px';
      try {
        var l = h.getBoundingClientRect(),
          j = l.left - Math.round(l.left),
          k = l.top - Math.round(l.top);
        j && (f.left = j + 'px');
        k && (f.top = k + 'px');
      } catch (i) {}
    },
    update: function () {
      this.R.update();
    },
    addDefs: function (f) {
      if (a.hasSVG) {
        var d = this.createSvgElement('desc'),
          h = this.container;
        h.setAttribute('version', '1.1');
        h.style.position = 'absolute';
        this.setSize(this.width, this.height);
        if (f.accessibleTitle) {
          var g = this.createSvgElement('text');
          h.appendChild(g);
          g.innerHTML = f.accessibleTitle;
          g.style.opacity = 0;
        }
        a.rtl && (h.setAttribute('direction', 'rtl'), (h.style.left = 'auto'), (h.style.right = '0px'));
        f &&
          (f.addCodeCredits && d.appendChild(document.createTextNode('JavaScript chart by amCharts ' + f.version)),
          h.appendChild(d),
          f.defs && ((d = this.createSvgElement('defs')), h.appendChild(d), a.parseDefs(f.defs, d), (this.defs = d)));
      }
    }
  });
})();
(function () {
  var a = window.AmCharts;
  a.AmDObject = a.Class({
    construct: function (d, c) {
      this.D = c;
      this.R = c.R;
      this.node = this.R.create(this, d);
      this.y = this.x = 0;
      this.scale = 1;
    },
    attr: function (b) {
      this.R.attr(this, b);
      return this;
    },
    getAttr: function (b) {
      return this.node.getAttribute(b);
    },
    setAttr: function (d, c) {
      this.R.setAttr(this, d, c);
      return this;
    },
    clipRect: function (f, d, h, g) {
      this.R.clipRect(this, f, d, h, g);
    },
    translate: function (f, d, h, g) {
      g || ((f = Math.round(f)), (d = Math.round(d)));
      this.R.move(this, f, d, h);
      this.x = f;
      this.y = d;
      this.scale = h;
      this.angle && this.rotate(this.angle);
    },
    rotate: function (d, c) {
      this.R.rotate(this, d, c);
      this.angle = d;
    },
    animate: function (h, d, l) {
      for (var k in h) {
        if (h.hasOwnProperty(k)) {
          var j = k,
            i = h[k];
          l = a.getEffect(l);
          this.R.animate(this, j, i, d, l);
        }
      }
    },
    push: function (e) {
      if (e) {
        var d = this.node;
        d.appendChild(e.node);
        var f = e.clipPath;
        f && d.appendChild(f);
        (e = e.grad) && d.appendChild(e);
      }
    },
    text: function (b) {
      this.R.setText(this, b);
    },
    remove: function () {
      this.stop();
      this.R.remove(this);
    },
    clear: function () {
      var b = this.node;
      if (b.hasChildNodes()) {
        for (; 1 <= b.childNodes.length; ) {
          b.removeChild(b.firstChild);
        }
      }
    },
    hide: function () {
      this.setAttr('visibility', 'hidden');
    },
    show: function () {
      this.setAttr('visibility', 'visible');
    },
    getBBox: function () {
      return this.R.getBBox(this);
    },
    toFront: function () {
      var d = this.node;
      if (d) {
        this.prevNextNode = d.nextSibling;
        var c = d.parentNode;
        c && c.appendChild(d);
      }
    },
    toPrevious: function () {
      var b = this.node;
      b && this.prevNextNode && (b = b.parentNode) && b.insertBefore(this.prevNextNode, null);
    },
    toBack: function () {
      var e = this.node;
      if (e) {
        this.prevNextNode = e.nextSibling;
        var d = e.parentNode;
        if (d) {
          var f = d.firstChild;
          f && d.insertBefore(e, f);
        }
      }
    },
    mouseover: function (b) {
      this.R.addListener(this, 'mouseover', b);
      return this;
    },
    mouseout: function (b) {
      this.R.addListener(this, 'mouseout', b);
      return this;
    },
    click: function (b) {
      this.R.addListener(this, 'click', b);
      return this;
    },
    dblclick: function (b) {
      this.R.addListener(this, 'dblclick', b);
      return this;
    },
    mousedown: function (b) {
      this.R.addListener(this, 'mousedown', b);
      return this;
    },
    mouseup: function (b) {
      this.R.addListener(this, 'mouseup', b);
      return this;
    },
    touchmove: function (b) {
      this.R.addListener(this, 'touchmove', b);
      return this;
    },
    touchstart: function (b) {
      this.R.addListener(this, 'touchstart', b);
      return this;
    },
    touchend: function (b) {
      this.R.addListener(this, 'touchend', b);
      return this;
    },
    keyup: function (b) {
      this.R.addListener(this, 'keyup', b);
      return this;
    },
    focus: function (b) {
      this.R.addListener(this, 'focus', b);
      return this;
    },
    blur: function (b) {
      this.R.addListener(this, 'blur', b);
      return this;
    },
    contextmenu: function (b) {
      this.node.addEventListener ? this.node.addEventListener('contextmenu', b, !0) : this.R.addListener(this, 'contextmenu', b);
      return this;
    },
    stop: function () {
      a.removeFromArray(this.R.animations, this.an_translate);
      a.removeFromArray(this.R.animations, this.an_y);
      a.removeFromArray(this.R.animations, this.an_x);
    },
    length: function () {
      return this.node.childNodes.length;
    },
    gradient: function (e, d, f) {
      this.R.gradient(this, e, d, f);
    },
    pattern: function (e, d, f) {
      e && this.R.pattern(this, e, d, f);
    }
  });
})();
(function () {
  var a = window.AmCharts;
  a.SVGRenderer = a.Class({
    construct: function (b) {
      this.D = b;
      this.animations = [];
    },
    create: function (d, c) {
      return document.createElementNS(a.SVG_NS, c);
    },
    attr: function (e, d) {
      for (var f in d) {
        d.hasOwnProperty(f) && this.setAttr(e, f, d[f]);
      }
    },
    setAttr: function (e, d, f) {
      void 0 !== f && e.node.setAttribute(d, f);
    },
    animate: function (h, d, l, k, j) {
      h.animationFinished = !1;
      var i = h.node;
      h['an_' + d] && a.removeFromArray(this.animations, h['an_' + d]);
      'translate' == d
        ? ((i = (i = i.getAttribute('transform')) ? String(i).substring(10, i.length - 1) : '0,0'),
          (i = i.split(', ').join(' ')),
          (i = i.split(' ').join(',')),
          0 === i && (i = '0,0'))
        : (i = Number(i.getAttribute(d)));
      l = {
        obj: h,
        frame: 0,
        attribute: d,
        from: i,
        to: l,
        time: k,
        effect: j
      };
      this.animations.push(l);
      h['an_' + d] = l;
    },
    update: function () {
      var t,
        s = this.animations;
      for (t = s.length - 1; 0 <= t; t--) {
        var r = s[t],
          q = r.time * a.updateRate,
          p = r.frame + 1,
          o = r.obj,
          n = r.attribute,
          j,
          i,
          d;
        if (p <= q) {
          r.frame++;
          if ('translate' == n) {
            j = r.from.split(',');
            n = Number(j[0]);
            j = Number(j[1]);
            isNaN(j) && (j = 0);
            i = r.to.split(',');
            d = Number(i[0]);
            i = Number(i[1]);
            d = 0 === d - n ? d : Math.round(a[r.effect](0, p, n, d - n, q));
            r = 0 === i - j ? i : Math.round(a[r.effect](0, p, j, i - j, q));
            n = 'transform';
            if (isNaN(d) || isNaN(r)) {
              continue;
            }
            r = 'translate(' + d + ',' + r + ')';
          } else {
            (i = Number(r.from)),
              (j = Number(r.to)),
              (d = j - i),
              (r = a[r.effect](0, p, i, d, q)),
              isNaN(r) && (r = j),
              0 === d && this.animations.splice(t, 1);
          }
          this.setAttr(o, n, r);
        } else {
          'translate' == n
            ? ((i = r.to.split(',')), (d = Number(i[0])), (i = Number(i[1])), o.translate(d, i))
            : ((j = Number(r.to)), this.setAttr(o, n, j)),
            (o.animationFinished = !0),
            this.animations.splice(t, 1);
        }
      }
    },
    getBBox: function (d) {
      if ((d = d.node)) {
        try {
          return d.getBBox();
        } catch (c) {}
      }
      return { width: 0, height: 0, x: 0, y: 0 };
    },
    path: function (d, c) {
      d.node.setAttributeNS(a.SVG_XLINK, 'xlink:href', c);
    },
    clipRect: function (i, d, p, o, n) {
      var m = i.node,
        l = i.clipPath;
      l && this.D.remove(l);
      var j = m.parentNode;
      j &&
        ((m = document.createElementNS(a.SVG_NS, 'clipPath')),
        (l = a.getUniqueId()),
        m.setAttribute('id', l),
        this.D.rect(d, p, o, n, 0, 0, m),
        j.appendChild(m),
        (d = '#'),
        a.baseHref && !a.isIE && (d = this.removeTarget(window.location.href) + d),
        this.setAttr(i, 'clip-path', 'url(' + d + l + ')'),
        this.clipPathC++,
        (i.clipPath = m));
    },
    text: function (i, d, n) {
      var m = new a.AmDObject('text', this.D);
      i = String(i).split('\n');
      var l = a.removePx(d['font-size']),
        k;
      for (k = 0; k < i.length; k++) {
        var j = this.create(null, 'tspan');
        j.appendChild(document.createTextNode(i[k]));
        j.setAttribute('y', (l + 2) * k + Math.round(l / 2));
        j.setAttribute('x', 0);
        m.node.appendChild(j);
      }
      m.node.setAttribute('y', Math.round(l / 2));
      this.attr(m, d);
      this.D.addToContainer(m.node, n);
      return m;
    },
    setText: function (e, d) {
      var f = e.node;
      f && (f.removeChild(f.firstChild), f.appendChild(document.createTextNode(d)));
    },
    move: function (f, d, h, g) {
      isNaN(d) && (d = 0);
      isNaN(h) && (h = 0);
      d = 'translate(' + d + ',' + h + ')';
      g && (d = d + ' scale(' + g + ')');
      this.setAttr(f, 'transform', d);
    },
    rotate: function (f, d) {
      var h = f.node.getAttribute('transform'),
        g = 'rotate(' + d + ')';
      h && (g = h + ' ' + g);
      this.setAttr(f, 'transform', g);
    },
    set: function (e) {
      var d = new a.AmDObject('g', this.D);
      this.D.container.appendChild(d.node);
      if (e) {
        var f;
        for (f = 0; f < e.length; f++) {
          d.push(e[f]);
        }
      }
      return d;
    },
    addListener: function (e, d, f) {
      e.node['on' + d] = f;
    },
    gradient: function (t, s, r, q) {
      var p = t.node,
        o = t.grad;
      o && this.D.remove(o);
      s = document.createElementNS(a.SVG_NS, s);
      o = a.getUniqueId();
      s.setAttribute('id', o);
      if (!isNaN(q)) {
        var n = 0,
          j = 0,
          i = 0,
          d = 0;
        90 == q ? (i = 100) : 270 == q ? (d = 100) : 180 == q ? (n = 100) : 0 === q && (j = 100);
        s.setAttribute('x1', n + '%');
        s.setAttribute('x2', j + '%');
        s.setAttribute('y1', i + '%');
        s.setAttribute('y2', d + '%');
      }
      for (q = 0; q < r.length; q++) {
        (n = document.createElementNS(a.SVG_NS, 'stop')),
          (j = (100 * q) / (r.length - 1)),
          0 === q && (j = 0),
          n.setAttribute('offset', j + '%'),
          n.setAttribute('stop-color', r[q]),
          s.appendChild(n);
      }
      p.parentNode.appendChild(s);
      r = '#';
      a.baseHref && !a.isIE && (r = this.removeTarget(window.location.href) + r);
      p.setAttribute('fill', 'url(' + r + o + ')');
      t.grad = s;
    },
    removeTarget: function (b) {
      return b.split('#')[0];
    },
    pattern: function (v, u, t, s) {
      var r = v.node;
      isNaN(t) && (t = 1);
      var q = v.patternNode;
      q && this.D.remove(q);
      var q = document.createElementNS(a.SVG_NS, 'pattern'),
        p = a.getUniqueId(),
        o = u;
      u.url && (o = u.url);
      a.isAbsolute(o) || -1 != o.indexOf('data:image') || (o = s + o);
      s = Number(u.width);
      isNaN(s) && (s = 4);
      var j = Number(u.height);
      isNaN(j) && (j = 4);
      s /= t;
      j /= t;
      t = u.x;
      isNaN(t) && (t = 0);
      var i = -Math.random() * Number(u.randomX);
      isNaN(i) || (t = i);
      i = u.y;
      isNaN(i) && (i = 0);
      var d = -Math.random() * Number(u.randomY);
      isNaN(d) || (i = d);
      q.setAttribute('id', p);
      q.setAttribute('width', s);
      q.setAttribute('height', j);
      q.setAttribute('patternUnits', 'userSpaceOnUse');
      q.setAttribute('xlink:href', o);
      u.color &&
        ((d = document.createElementNS(a.SVG_NS, 'rect')),
        d.setAttributeNS(null, 'height', s),
        d.setAttributeNS(null, 'width', j),
        d.setAttributeNS(null, 'fill', u.color),
        q.appendChild(d));
      this.D.image(o, 0, 0, s, j, q).translate(t, i);
      o = '#';
      a.baseHref && !a.isIE && (o = this.removeTarget(window.location.href) + o);
      r.setAttribute('fill', 'url(' + o + p + ')');
      v.patternNode = q;
      r.parentNode.appendChild(q);
    },
    remove: function (b) {
      b.clipPath && this.D.remove(b.clipPath);
      b.grad && this.D.remove(b.grad);
      b.patternNode && this.D.remove(b.patternNode);
      this.D.remove(b.node);
    }
  });
})();
(function () {
  var a = window.AmCharts;
  a.AmChart = a.Class({
    construct: function (e) {
      this.svgIcons = this.tapToActivate = !0;
      this.theme = e;
      this.classNamePrefix = 'amcharts';
      this.addClassNames = !1;
      this.version = '3.21.5';
      a.addChart(this);
      this.createEvents('buildStarted', 'dataUpdated', 'init', 'rendered', 'drawn', 'failed', 'resized', 'animationFinished');
      this.height = this.width = '100%';
      this.dataChanged = !0;
      this.chartCreated = !1;
      this.previousWidth = this.previousHeight = 0;
      this.backgroundColor = '#FFFFFF';
      this.borderAlpha = this.backgroundAlpha = 0;
      this.color = this.borderColor = '#000000';
      this.fontFamily = 'Verdana';
      this.fontSize = 11;
      this.usePrefixes = !1;
      this.autoResize = !0;
      this.autoDisplay = !1;
      this.addCodeCredits = this.accessible = !0;
      this.touchStartTime = this.touchClickDuration = 0;
      this.precision = -1;
      this.percentPrecision = 2;
      this.decimalSeparator = '.';
      this.thousandsSeparator = ',';
      this.labels = [];
      this.allLabels = [];
      this.titles = [];
      this.marginRight = this.marginLeft = this.autoMarginOffset = 0;
      this.timeOuts = [];
      this.creditsPosition = 'top-left';
      var d = document.createElement('div'),
        f = d.style;
      f.overflow = 'hidden';
      f.position = 'relative';
      f.textAlign = 'left';
      this.chartDiv = d;
      d = document.createElement('div');
      f = d.style;
      f.overflow = 'hidden';
      f.position = 'relative';
      f.textAlign = 'left';
      this.legendDiv = d;
      this.titleHeight = 0;
      this.hideBalloonTime = 150;
      this.handDrawScatter = 2;
      this.cssScale = this.handDrawThickness = 1;
      this.cssAngle = 0;
      this.prefixesOfBigNumbers = [
        { number: 1000, prefix: 'k' },
        { number: 1000000, prefix: 'M' },
        { number: 1000000000, prefix: 'G' },
        { number: 1000000000000, prefix: 'T' },
        { number: 1000000000000000, prefix: 'P' },
        { number: 1000000000000000000, prefix: 'E' },
        { number: 1e21, prefix: 'Z' },
        { number: 1e24, prefix: 'Y' }
      ];
      this.prefixesOfSmallNumbers = [
        { number: 1e-24, prefix: 'y' },
        { number: 1e-21, prefix: 'z' },
        { number: 1e-18, prefix: 'a' },
        { number: 1e-15, prefix: 'f' },
        { number: 1e-12, prefix: 'p' },
        { number: 1e-9, prefix: 'n' },
        { number: 0.000001, prefix: '\u03bc' },
        { number: 0.001, prefix: 'm' }
      ];
      this.panEventsEnabled = !0;
      this.product = 'amcharts';
      this.animations = [];
      this.balloon = new a.AmBalloon(this.theme);
      this.balloon.chart = this;
      this.processTimeout = 0;
      this.processCount = 1000;
      this.animatable = [];
      this.langObj = {};
      a.applyTheme(this, e, 'AmChart');
    },
    drawChart: function () {
      0 < this.realWidth &&
        0 < this.realHeight &&
        (this.drawBackground(),
        this.redrawLabels(),
        this.drawTitles(),
        this.brr(),
        this.renderFix(),
        this.chartDiv && (this.boundingRect = this.chartDiv.getBoundingClientRect()));
    },
    makeAccessible: function (e, d, f) {
      this.accessible && e && (f && e.setAttr('role', f), e.setAttr('aria-label', d));
    },
    drawBackground: function () {
      a.remove(this.background);
      var h = this.container,
        d = this.backgroundColor,
        l = this.backgroundAlpha,
        k = this.set;
      a.isModern || 0 !== l || (l = 0.001);
      var j = this.updateWidth();
      this.realWidth = j;
      var i = this.updateHeight();
      this.realHeight = i;
      d = a.polygon(h, [0, j - 1, j - 1, 0], [0, 0, i - 1, i - 1], d, l, 1, this.borderColor, this.borderAlpha);
      a.setCN(this, d, 'bg');
      this.background = d;
      k.push(d);
      if ((d = this.backgroundImage)) {
        (h = h.image(d, 0, 0, j, i)), a.setCN(this, d, 'bg-image'), (this.bgImg = h), k.push(h);
      }
    },
    drawTitles: function (r) {
      var q = this.titles;
      this.titleHeight = 0;
      if (a.ifArray(q)) {
        var p = 20,
          o;
        for (o = 0; o < q.length; o++) {
          var n = q[o],
            n = a.processObject(n, a.Title, this.theme);
          if (!1 !== n.enabled) {
            var m = n.color;
            void 0 === m && (m = this.color);
            var j = n.size;
            isNaN(j) && (j = this.fontSize + 2);
            isNaN(n.alpha);
            var i = this.marginLeft,
              d = !0;
            void 0 !== n.bold && (d = n.bold);
            m = a.wrappedText(this.container, n.text, m, this.fontFamily, j, 'middle', d, this.realWidth - 35 - this.marginRight - i);
            m.translate(i + (this.realWidth - this.marginRight - i) / 2, p);
            m.node.style.pointerEvents = 'none';
            n.sprite = m;
            void 0 !== n.tabIndex && m.setAttr('tabindex', n.tabIndex);
            a.setCN(this, m, 'title');
            n.id && a.setCN(this, m, 'title-' + n.id);
            m.attr({ opacity: n.alpha });
            p += m.getBBox().height + 5;
            r ? m.remove() : this.freeLabelsSet.push(m);
          }
        }
        this.titleHeight = p - 10;
      }
    },
    write: function (f) {
      var d = this;
      if (d.listeners) {
        for (var h = 0; h < d.listeners.length; h++) {
          var g = d.listeners[h];
          d.addListener(g.event, g.method);
        }
      }
      d.fire({ type: 'buildStarted', chart: d });
      d.afterWriteTO && clearTimeout(d.afterWriteTO);
      0 < d.processTimeout
        ? (d.afterWriteTO = setTimeout(function () {
            d.afterWrite.call(d, f);
          }, d.processTimeout))
        : d.afterWrite(f);
    },
    afterWrite: function (i) {
      var d;
      if ((d = 'object' != typeof i ? document.getElementById(i) : i)) {
        for (; d.firstChild; ) {
          d.removeChild(d.firstChild);
        }
        this.div = d;
        d.style.overflow = 'hidden';
        d.style.textAlign = 'left';
        i = this.chartDiv;
        var p = this.legendDiv,
          o = this.legend,
          n = p.style,
          m = i.style;
        this.measure();
        this.previousHeight = this.divRealHeight;
        this.previousWidth = this.divRealWidth;
        var l,
          j = document.createElement('div');
        l = j.style;
        l.position = 'relative';
        this.containerDiv = j;
        j.className = this.classNamePrefix + '-main-div';
        i.className = this.classNamePrefix + '-chart-div';
        d.appendChild(j);
        (d = this.exportConfig) && a.AmExport && !this.AmExport && (this.AmExport = new a.AmExport(this, d));
        this.amExport && a.AmExport && (this.AmExport = a.extend(this.amExport, new a.AmExport(this), !0));
        this.AmExport && this.AmExport.init && this.AmExport.init();
        if (o) {
          o = this.addLegend(o, o.divId);
          if (o.enabled) {
            switch (
              ((n.left = null),
              (n.top = null),
              (n.right = null),
              (m.left = null),
              (m.right = null),
              (m.top = null),
              (n.position = 'relative'),
              (m.position = 'relative'),
              (l.width = '100%'),
              (l.height = '100%'),
              o.position)
            ) {
              case 'bottom':
                j.appendChild(i);
                j.appendChild(p);
                break;
              case 'top':
                j.appendChild(p);
                j.appendChild(i);
                break;
              case 'absolute':
                n.position = 'absolute';
                m.position = 'absolute';
                void 0 !== o.left && (n.left = o.left + 'px');
                void 0 !== o.right && (n.right = o.right + 'px');
                void 0 !== o.top && (n.top = o.top + 'px');
                void 0 !== o.bottom && (n.bottom = o.bottom + 'px');
                o.marginLeft = 0;
                o.marginRight = 0;
                j.appendChild(i);
                j.appendChild(p);
                break;
              case 'right':
                n.position = 'relative';
                m.position = 'absolute';
                j.appendChild(i);
                j.appendChild(p);
                break;
              case 'left':
                n.position = 'absolute';
                m.position = 'relative';
                j.appendChild(i);
                j.appendChild(p);
                break;
              case 'outside':
                j.appendChild(i);
            }
          } else {
            j.appendChild(i);
          }
          this.prevLegendPosition = o.position;
        } else {
          j.appendChild(i);
        }
        this.listenersAdded || (this.addListeners(), (this.listenersAdded = !0));
        (this.mouseWheelScrollEnabled || this.mouseWheelZoomEnabled) && a.addWheelListeners();
        this.initChart();
      }
    },
    createLabelsSet: function () {
      a.remove(this.labelsSet);
      this.labelsSet = this.container.set();
      this.freeLabelsSet.push(this.labelsSet);
    },
    initChart: function () {
      this.balloon = a.processObject(this.balloon, a.AmBalloon, this.theme);
      window.AmCharts_path && (this.path = window.AmCharts_path);
      void 0 === this.path && (this.path = a.getPath());
      void 0 === this.path && (this.path = 'amcharts/');
      this.path = a.normalizeUrl(this.path);
      void 0 === this.pathToImages && (this.pathToImages = this.path + 'images/');
      this.initHC || (a.callInitHandler(this), (this.initHC = !0));
      a.applyLang(this.language, this);
      var b = this.numberFormatter;
      b &&
        (isNaN(b.precision) || (this.precision = b.precision),
        void 0 !== b.thousandsSeparator && (this.thousandsSeparator = b.thousandsSeparator),
        void 0 !== b.decimalSeparator && (this.decimalSeparator = b.decimalSeparator));
      (b = this.percentFormatter) && !isNaN(b.precision) && (this.percentPrecision = b.precision);
      this.nf = {
        precision: this.precision,
        thousandsSeparator: this.thousandsSeparator,
        decimalSeparator: this.decimalSeparator
      };
      this.pf = {
        precision: this.percentPrecision,
        thousandsSeparator: this.thousandsSeparator,
        decimalSeparator: this.decimalSeparator
      };
      this.destroy();
      (b = this.container)
        ? ((b.container.innerHTML = ''),
          (b.width = this.realWidth),
          (b.height = this.realHeight),
          b.addDefs(this),
          this.chartDiv.appendChild(b.container))
        : (b = new a.AmDraw(this.chartDiv, this.realWidth, this.realHeight, this));
      this.container = b;
      this.extension = '.png';
      this.svgIcons && a.SVG && (this.extension = '.svg');
      this.checkDisplay();
      this.checkTransform(this.div);
      b.chart = this;
      a.VML || a.SVG
        ? ((b.handDrawn = this.handDrawn),
          (b.handDrawScatter = this.handDrawScatter),
          (b.handDrawThickness = this.handDrawThickness),
          a.remove(this.set),
          (this.set = b.set()),
          a.remove(this.gridSet),
          (this.gridSet = b.set()),
          a.remove(this.cursorLineSet),
          (this.cursorLineSet = b.set()),
          a.remove(this.graphsBehindSet),
          (this.graphsBehindSet = b.set()),
          a.remove(this.bulletBehindSet),
          (this.bulletBehindSet = b.set()),
          a.remove(this.columnSet),
          (this.columnSet = b.set()),
          a.remove(this.graphsSet),
          (this.graphsSet = b.set()),
          a.remove(this.trendLinesSet),
          (this.trendLinesSet = b.set()),
          a.remove(this.axesSet),
          (this.axesSet = b.set()),
          a.remove(this.cursorSet),
          (this.cursorSet = b.set()),
          a.remove(this.scrollbarsSet),
          (this.scrollbarsSet = b.set()),
          a.remove(this.bulletSet),
          (this.bulletSet = b.set()),
          a.remove(this.freeLabelsSet),
          (this.freeLabelsSet = b.set()),
          a.remove(this.axesLabelsSet),
          (this.axesLabelsSet = b.set()),
          a.remove(this.balloonsSet),
          (this.balloonsSet = b.set()),
          a.remove(this.plotBalloonsSet),
          (this.plotBalloonsSet = b.set()),
          a.remove(this.zoomButtonSet),
          (this.zoomButtonSet = b.set()),
          a.remove(this.zbSet),
          (this.zbSet = null),
          a.remove(this.linkSet),
          (this.linkSet = b.set()))
        : this.fire({ type: 'failed', chart: this });
    },
    premeasure: function () {
      var f = this.div;
      if (f) {
        try {
          this.boundingRect = this.chartDiv.getBoundingClientRect();
        } catch (g) {}
        var d = f.offsetWidth,
          h = f.offsetHeight;
        f.clientHeight && ((d = f.clientWidth), (h = f.clientHeight));
        if (d != this.mw || h != this.mh) {
          (this.mw = d), (this.mh = h), this.measure();
        }
      }
    },
    measure: function () {
      var h = this.div;
      if (h) {
        var d = this.chartDiv,
          l = h.offsetWidth,
          k = h.offsetHeight,
          j = this.container;
        h.clientHeight && ((l = h.clientWidth), (k = h.clientHeight));
        var k = Math.round(k),
          l = Math.round(l),
          h = Math.round(a.toCoordinate(this.width, l)),
          i = Math.round(a.toCoordinate(this.height, k));
        (l != this.previousWidth || k != this.previousHeight) &&
          0 < h &&
          0 < i &&
          ((d.style.width = h + 'px'),
          (d.style.height = i + 'px'),
          (d.style.padding = 0),
          j && j.setSize(h, i),
          (this.balloon = a.processObject(this.balloon, a.AmBalloon, this.theme)));
        this.balloon && this.balloon.setBounds && this.balloon.setBounds(2, 2, h - 2, i);
        this.updateWidth();
        this.balloon.chart = this;
        this.realWidth = h;
        this.realHeight = i;
        this.divRealWidth = l;
        this.divRealHeight = k;
      }
    },
    checkDisplay: function () {
      if (this.autoDisplay && this.container) {
        var d = a.rect(this.container, 10, 10),
          c = d.getBBox();
        0 === c.width &&
          0 === c.height &&
          ((this.divRealHeight = this.divRealWidth = this.realHeight = this.realWidth = 0),
          (this.previousWidth = this.previousHeight = NaN));
        d.remove();
      }
    },
    checkTransform: function (e) {
      if (this.autoTransform && window.getComputedStyle && e) {
        if (e.style) {
          var d = window.getComputedStyle(e, null);
          if (
            d &&
            (d =
              d.getPropertyValue('-webkit-transform') ||
              d.getPropertyValue('-moz-transform') ||
              d.getPropertyValue('-ms-transform') ||
              d.getPropertyValue('-o-transform') ||
              d.getPropertyValue('transform')) &&
            'none' !== d
          ) {
            var f = d.split('(')[1].split(')')[0].split(','),
              d = f[0],
              f = f[1],
              d = Math.sqrt(d * d + f * f);
            isNaN(d) || (this.cssScale *= d);
          }
        }
        e.parentNode && this.checkTransform(e.parentNode);
      }
    },
    destroy: function () {
      this.chartDiv.innerHTML = '';
      this.clearTimeOuts();
      this.legend && this.legend.destroy();
    },
    clearTimeOuts: function () {
      var d = this.timeOuts;
      if (d) {
        var c;
        for (c = 0; c < d.length; c++) {
          clearTimeout(d[c]);
        }
      }
      this.timeOuts = [];
    },
    clear: function (d) {
      try {
        document.removeEventListener('touchstart', this.docfn1, !0), document.removeEventListener('touchend', this.docfn2, !0);
      } catch (c) {}
      a.callMethod('clear', [this.chartScrollbar, this.scrollbarV, this.scrollbarH, this.chartCursor]);
      this.chartCursor = this.scrollbarH = this.scrollbarV = this.chartScrollbar = null;
      this.clearTimeOuts();
      this.container && (this.container.remove(this.chartDiv), this.container.remove(this.legendDiv));
      d || a.removeChart(this);
      if ((d = this.div)) {
        for (; d.firstChild; ) {
          d.removeChild(d.firstChild);
        }
      }
      this.legend && this.legend.destroy();
      this.AmExport && this.AmExport.clear && this.AmExport.clear();
    },
    setMouseCursor: function (b) {
      'auto' == b && a.isNN && (b = 'default');
      this.chartDiv.style.cursor = b;
      this.legendDiv.style.cursor = b;
    },
    redrawLabels: function () {
      this.labels = [];
      var d = this.allLabels;
      this.createLabelsSet();
      var c;
      for (c = 0; c < d.length; c++) {
        this.drawLabel(d[c]);
      }
    },
    drawLabel: function (v) {
      var u = this;
      if (u.container && !1 !== v.enabled) {
        v = a.processObject(v, a.Label, u.theme);
        var t = v.y,
          s = v.text,
          r = v.align,
          q = v.size,
          p = v.color,
          o = v.rotation,
          j = v.alpha,
          i = v.bold,
          d = a.toCoordinate(v.x, u.realWidth),
          t = a.toCoordinate(t, u.realHeight);
        d || (d = 0);
        t || (t = 0);
        void 0 === p && (p = u.color);
        isNaN(q) && (q = u.fontSize);
        r || (r = 'start');
        'left' == r && (r = 'start');
        'right' == r && (r = 'end');
        'center' == r && ((r = 'middle'), o ? (t = u.realHeight - t + t / 2) : (d = u.realWidth / 2 - d));
        void 0 === j && (j = 1);
        void 0 === o && (o = 0);
        t += q / 2;
        s = a.text(u.container, s, p, u.fontFamily, q, r, i, j);
        s.translate(d, t);
        void 0 !== v.tabIndex && s.setAttr('tabindex', v.tabIndex);
        a.setCN(u, s, 'label');
        v.id && a.setCN(u, s, 'label-' + v.id);
        0 !== o && s.rotate(o);
        v.url
          ? (s.setAttr('cursor', 'pointer'),
            s.click(function () {
              a.getURL(v.url, u.urlTarget);
            }))
          : (s.node.style.pointerEvents = 'none');
        u.labelsSet.push(s);
        u.labels.push(s);
      }
    },
    addLabel: function (t, s, r, p, q, o, n, j, i, f) {
      t = {
        x: t,
        y: s,
        text: r,
        align: p,
        size: q,
        color: o,
        alpha: j,
        rotation: n,
        bold: i,
        url: f,
        enabled: !0
      };
      this.container && this.drawLabel(t);
      this.allLabels.push(t);
    },
    clearLabels: function () {
      var d = this.labels,
        c;
      for (c = d.length - 1; 0 <= c; c--) {
        d[c].remove();
      }
      this.labels = [];
      this.allLabels = [];
    },
    updateHeight: function () {
      var e = this.divRealHeight,
        d = this.legend;
      if (d) {
        var f = this.legendDiv.offsetHeight,
          d = d.position;
        if ('top' == d || 'bottom' == d) {
          e -= f;
          if (0 > e || isNaN(e)) {
            e = 0;
          }
          this.chartDiv.style.height = e + 'px';
        }
      }
      return e;
    },
    updateWidth: function () {
      var i = this.divRealWidth,
        f = this.divRealHeight,
        p = this.legend;
      if (p) {
        var n = this.legendDiv,
          o = n.offsetWidth;
        isNaN(p.width) || (o = p.width);
        p.ieW && (o = p.ieW);
        var m = n.offsetHeight,
          n = n.style,
          l = this.chartDiv.style,
          j = p.position;
        if (('right' == j || 'left' == j) && void 0 === p.divId) {
          i -= o;
          if (0 > i || isNaN(i)) {
            i = 0;
          }
          l.width = i + 'px';
          this.balloon && this.balloon.setBounds && this.balloon.setBounds(2, 2, i - 2, this.realHeight);
          'left' == j ? ((l.left = o + 'px'), (n.left = '0px')) : ((l.left = '0px'), (n.left = i + 'px'));
          f > m && (n.top = (f - m) / 2 + 'px');
        }
      }
      return i;
    },
    getTitleHeight: function () {
      this.drawTitles(!0);
      return this.titleHeight;
    },
    addTitle: function (g, f, j, h, i) {
      isNaN(f) && (f = this.fontSize + 2);
      g = { text: g, size: f, color: j, alpha: h, bold: i, enabled: !0 };
      this.titles.push(g);
      return g;
    },
    handleWheel: function (d) {
      var c = 0;
      d || (d = window.event);
      d.wheelDelta ? (c = d.wheelDelta / 120) : d.detail && (c = -d.detail / 3);
      c && this.handleWheelReal(c, d.shiftKey);
      d.preventDefault && d.preventDefault();
    },
    handleWheelReal: function () {},
    handleDocTouchStart: function () {
      this.handleMouseMove();
      this.tmx = this.mouseX;
      this.tmy = this.mouseY;
      this.touchStartTime = new Date().getTime();
    },
    handleDocTouchEnd: function () {
      -0.5 < this.tmx && this.tmx < this.divRealWidth + 1 && 0 < this.tmy && this.tmy < this.divRealHeight
        ? (this.handleMouseMove(),
          4 > Math.abs(this.mouseX - this.tmx) && 4 > Math.abs(this.mouseY - this.tmy)
            ? ((this.tapped = !0),
              this.panRequired &&
                this.panEventsEnabled &&
                this.chartDiv &&
                ((this.chartDiv.style.msTouchAction = 'none'), (this.chartDiv.style.touchAction = 'none')))
            : this.mouseIsOver || this.resetTouchStyle())
        : ((this.tapped = !1), this.resetTouchStyle());
    },
    resetTouchStyle: function () {
      this.panEventsEnabled && this.chartDiv && ((this.chartDiv.style.msTouchAction = 'auto'), (this.chartDiv.style.touchAction = 'auto'));
    },
    checkTouchDuration: function (e) {
      var d = this,
        f = new Date().getTime();
      if (e) {
        if (e.touches) {
          d.isTouchEvent = !0;
        } else {
          if (!d.isTouchEvent) {
            return !0;
          }
        }
      }
      if (f - d.touchStartTime > d.touchClickDuration) {
        return !0;
      }
      setTimeout(function () {
        d.resetTouchDuration();
      }, 300);
    },
    resetTouchDuration: function () {
      this.isTouchEvent = !1;
    },
    checkTouchMoved: function () {
      if (4 < Math.abs(this.mouseX - this.tmx) || 4 < Math.abs(this.mouseY - this.tmy)) {
        return !0;
      }
    },
    addListeners: function () {
      var d = this,
        c = d.chartDiv;
      document.addEventListener
        ? ('ontouchstart' in document.documentElement &&
            (c.addEventListener(
              'touchstart',
              function (e) {
                d.handleTouchStart.call(d, e);
              },
              !0
            ),
            c.addEventListener(
              'touchmove',
              function (e) {
                d.handleMouseMove.call(d, e);
              },
              !0
            ),
            c.addEventListener(
              'touchend',
              function (e) {
                d.handleTouchEnd.call(d, e);
              },
              !0
            ),
            (d.docfn1 = function (e) {
              d.handleDocTouchStart.call(d, e);
            }),
            (d.docfn2 = function (e) {
              d.handleDocTouchEnd.call(d, e);
            }),
            document.addEventListener('touchstart', d.docfn1, !0),
            document.addEventListener('touchend', d.docfn2, !0)),
          c.addEventListener(
            'mousedown',
            function (e) {
              d.mouseIsOver = !0;
              d.handleMouseMove.call(d, e);
              d.handleMouseDown.call(d, e);
              d.handleDocTouchStart.call(d, e);
            },
            !0
          ),
          c.addEventListener(
            'mouseover',
            function (e) {
              d.handleMouseOver.call(d, e);
            },
            !0
          ),
          c.addEventListener(
            'mouseout',
            function (e) {
              d.handleMouseOut.call(d, e);
            },
            !0
          ),
          c.addEventListener(
            'mouseup',
            function (e) {
              d.handleDocTouchEnd.call(d, e);
            },
            !0
          ))
        : (c.attachEvent('onmousedown', function (e) {
            d.handleMouseDown.call(d, e);
          }),
          c.attachEvent('onmouseover', function (e) {
            d.handleMouseOver.call(d, e);
          }),
          c.attachEvent('onmouseout', function (e) {
            d.handleMouseOut.call(d, e);
          }));
    },
    dispDUpd: function () {
      this.skipEvents ||
        (this.dispatchDataUpdated && ((this.dispatchDataUpdated = !1), this.fire({ type: 'dataUpdated', chart: this })),
        this.chartCreated || ((this.chartCreated = !0), this.fire({ type: 'init', chart: this })),
        this.chartRendered || (this.fire({ type: 'rendered', chart: this }), (this.chartRendered = !0)),
        this.fire({ type: 'drawn', chart: this }));
      this.skipEvents = !1;
    },
    validateSize: function () {
      var e = this;
      e.premeasure();
      e.checkDisplay();
      e.cssScale = 1;
      e.cssAngle = 0;
      e.checkTransform(e.div);
      if (e.divRealWidth != e.previousWidth || e.divRealHeight != e.previousHeight) {
        var d = e.legend;
        if (0 < e.realWidth && 0 < e.realHeight) {
          e.sizeChanged = !0;
          if (d) {
            e.legendInitTO && clearTimeout(e.legendInitTO);
            var f = setTimeout(function () {
              d.invalidateSize();
            }, 10);
            e.timeOuts.push(f);
            e.legendInitTO = f;
          }
          e.marginsUpdated = !1;
          clearTimeout(e.initTO);
          f = setTimeout(function () {
            e.initChart();
          }, 10);
          e.timeOuts.push(f);
          e.initTO = f;
        }
        e.renderFix();
        d && d.renderFix && d.renderFix();
        clearTimeout(e.resizedTO);
        e.resizedTO = setTimeout(function () {
          e.fire({ type: 'resized', chart: e });
        }, 10);
        e.previousHeight = e.divRealHeight;
        e.previousWidth = e.divRealWidth;
      }
    },
    invalidateSize: function () {
      this.previousHeight = this.previousWidth = NaN;
      this.invalidateSizeReal();
    },
    invalidateSizeReal: function () {
      var d = this;
      d.marginsUpdated = !1;
      clearTimeout(d.validateTO);
      var c = setTimeout(function () {
        d.validateSize();
      }, 5);
      d.timeOuts.push(c);
      d.validateTO = c;
    },
    validateData: function (b) {
      this.chartCreated && ((this.dataChanged = !0), (this.marginsUpdated = !1), this.initChart(b));
    },
    validateNow: function (e, d) {
      this.initTO && clearTimeout(this.initTO);
      e && ((this.dataChanged = !0), (this.marginsUpdated = !1));
      this.skipEvents = d;
      this.chartRendered = !1;
      var f = this.legend;
      f &&
        f.position != this.prevLegendPosition &&
        ((this.previousWidth = this.mw = 0), f.invalidateSize && (f.invalidateSize(), this.validateSize()));
      this.write(this.div);
    },
    showItem: function (b) {
      b.hidden = !1;
      this.initChart();
    },
    hideItem: function (b) {
      b.hidden = !0;
      this.initChart();
    },
    hideBalloon: function () {
      var b = this;
      clearTimeout(b.hoverInt);
      clearTimeout(b.balloonTO);
      b.hoverInt = setTimeout(function () {
        b.hideBalloonReal.call(b);
      }, b.hideBalloonTime);
    },
    cleanChart: function () {},
    hideBalloonReal: function () {
      var b = this.balloon;
      b && b.hide && b.hide();
    },
    showBalloon: function (h, f, l, j, k) {
      var i = this;
      clearTimeout(i.balloonTO);
      clearTimeout(i.hoverInt);
      i.balloonTO = setTimeout(function () {
        i.showBalloonReal.call(i, h, f, l, j, k);
      }, 1);
    },
    showBalloonReal: function (h, f, l, j, k) {
      this.handleMouseMove();
      var i = this.balloon;
      i.enabled &&
        (i.followCursor(!1),
        i.changeColor(f),
        !l || i.fixedPosition ? (i.setPosition(j, k), isNaN(j) || isNaN(k) ? i.followCursor(!0) : i.followCursor(!1)) : i.followCursor(!0),
        h && i.showBalloon(h));
    },
    handleMouseOver: function () {
      this.outTO && clearTimeout(this.outTO);
      a.resetMouseOver();
      this.mouseIsOver = !0;
    },
    handleMouseOut: function () {
      var b = this;
      a.resetMouseOver();
      b.outTO && clearTimeout(b.outTO);
      b.outTO = setTimeout(function () {
        b.handleMouseOutReal();
      }, 10);
    },
    handleMouseOutReal: function () {
      this.mouseIsOver = !1;
    },
    handleMouseMove: function (h) {
      h || (h = window.event);
      this.mouse2Y = this.mouse2X = NaN;
      var f, l, j, k;
      if (h) {
        if (h.touches) {
          var i = h.touches.item(1);
          i &&
            this.panEventsEnabled &&
            this.boundingRect &&
            ((j = i.clientX - this.boundingRect.left), (k = i.clientY - this.boundingRect.top));
          h = h.touches.item(0);
          if (!h) {
            return;
          }
        } else {
          this.wasTouched = !1;
        }
        this.boundingRect && h.clientX && ((f = h.clientX - this.boundingRect.left), (l = h.clientY - this.boundingRect.top));
        isNaN(j) ? (this.mouseX = f) : ((this.mouseX = Math.min(f, j)), (this.mouse2X = Math.max(f, j)));
        isNaN(k) ? (this.mouseY = l) : ((this.mouseY = Math.min(l, k)), (this.mouse2Y = Math.max(l, k)));
        this.autoTransform && ((this.mouseX /= this.cssScale), (this.mouseY /= this.cssScale));
      }
    },
    handleTouchStart: function (b) {
      this.hideBalloonReal();
      (b && ((b.touches && this.tapToActivate && !this.tapped) || !this.panRequired)) || (this.handleMouseMove(b), this.handleMouseDown(b));
    },
    handleTouchEnd: function (b) {
      this.wasTouched = !0;
      this.handleMouseMove(b);
      a.resetMouseOver();
      this.handleReleaseOutside(b);
    },
    handleReleaseOutside: function () {
      this.handleDocTouchEnd.call(this);
    },
    handleMouseDown: function (b) {
      a.resetMouseOver();
      this.mouseIsOver = !0;
      b && b.preventDefault && (this.panEventsEnabled ? b.preventDefault() : b.touches || b.preventDefault());
    },
    addLegend: function (e, d) {
      e = a.processObject(e, a.AmLegend, this.theme);
      e.divId = d;
      e.ieW = 0;
      var f;
      f = 'object' != typeof d && d ? document.getElementById(d) : d;
      this.legend = e;
      e.chart = this;
      f ? ((e.div = f), (e.position = 'outside'), (e.autoMargins = !1)) : (e.div = this.legendDiv);
      return e;
    },
    removeLegend: function () {
      this.legend = void 0;
      this.previousWidth = 0;
      this.legendDiv.innerHTML = '';
    },
    handleResize: function () {
      (a.isPercents(this.width) || a.isPercents(this.height)) && this.invalidateSizeReal();
      this.renderFix();
    },
    renderFix: function () {
      if (!a.VML) {
        var b = this.container;
        b && b.renderFix();
      }
    },
    getSVG: function () {
      if (a.hasSVG) {
        return this.container;
      }
    },
    animate: function (i, d, n, m, l, k, j) {
      i['an_' + d] && a.removeFromArray(this.animations, i['an_' + d]);
      n = {
        obj: i,
        frame: 0,
        attribute: d,
        from: n,
        to: m,
        time: l,
        effect: k,
        suffix: j
      };
      i['an_' + d] = n;
      this.animations.push(n);
      return n;
    },
    setLegendData: function (d) {
      var c = this.legend;
      c && c.setData(d);
    },
    stopAnim: function (b) {
      a.removeFromArray(this.animations, b);
    },
    updateAnimations: function () {
      var i;
      this.container && this.container.update();
      if (this.animations) {
        for (i = this.animations.length - 1; 0 <= i; i--) {
          var d = this.animations[i],
            p = a.updateRate * d.time,
            o = d.frame + 1,
            n = d.obj,
            m = d.attribute;
          if (o <= p) {
            d.frame++;
            var l = Number(d.from),
              j = Number(d.to) - l,
              p = a[d.effect](0, o, l, j, p);
            0 === j ? (this.animations.splice(i, 1), (n.node.style[m] = Number(d.to) + d.suffix)) : (n.node.style[m] = p + d.suffix);
          } else {
            (n.node.style[m] = Number(d.to) + d.suffix), (n.animationFinished = !0), this.animations.splice(i, 1);
          }
        }
      }
    },
    update: function () {
      this.updateAnimations();
      var f = this.animatable;
      if (0 < f.length) {
        for (var d = !0, h = f.length - 1; 0 <= h; h--) {
          var g = f[h];
          g && (g.animationFinished ? f.splice(h, 1) : (d = !1));
        }
        d && (this.fire({ type: 'animationFinished', chart: this }), (this.animatable = []));
      }
    },
    inIframe: function () {
      try {
        return window.self !== window.top;
      } catch (b) {
        return !0;
      }
    },
    brr: function () {
      if (!this.hideCredits) {
        var t = 'amcharts.com',
          s = window.location.hostname.split('.'),
          r;
        2 <= s.length && (r = s[s.length - 2] + '.' + s[s.length - 1]);
        this.amLink && (s = this.amLink.parentNode) && s.removeChild(this.amLink);
        s = this.creditsPosition;
        if (r != t || !0 === this.inIframe()) {
          var t = 'http://www.' + t,
            p = (r = 0),
            q = this.realWidth,
            o = this.realHeight,
            n = this.type;
          if ('serial' == n || 'xy' == n || 'gantt' == n) {
            (r = this.marginLeftReal), (p = this.marginTopReal), (q = r + this.plotAreaWidth), (o = p + this.plotAreaHeight);
          }
          var n = t + '/javascript-charts/',
            j = 'JavaScript charts',
            i = 'JS chart by amCharts';
          'ammap' == this.product && ((n = t + '/javascript-maps/'), (j = 'Interactive JavaScript maps'), (i = 'JS map by amCharts'));
          t = document.createElement('a');
          i = document.createTextNode(i);
          t.setAttribute('href', n);
          t.setAttribute('title', j);
          this.urlTarget && t.setAttribute('target', this.urlTarget);
          t.appendChild(i);
          this.chartDiv.appendChild(t);
          this.amLink = t;
          n = t.style;
          n.position = 'absolute';
          n.textDecoration = 'none';
          n.color = this.color;
          n.fontFamily = this.fontFamily;
          n.fontSize = '11px';
          n.opacity = 0.7;
          n.display = 'block';
          var j = t.offsetWidth,
            t = t.offsetHeight,
            i = 5 + r,
            f = p + 5;
          'bottom-left' == s && ((i = 5 + r), (f = o - t - 3));
          'bottom-right' == s && ((i = q - j - 5), (f = o - t - 3));
          'top-right' == s && ((i = q - j - 5), (f = p + 5));
          n.left = i + 'px';
          n.top = f + 'px';
        }
      }
    }
  });
  a.Slice = a.Class({ construct: function () {} });
  a.SerialDataItem = a.Class({ construct: function () {} });
  a.GraphDataItem = a.Class({ construct: function () {} });
  a.Guide = a.Class({
    construct: function (b) {
      this.cname = 'Guide';
      a.applyTheme(this, b, this.cname);
    }
  });
  a.Title = a.Class({
    construct: function (b) {
      this.cname = 'Title';
      a.applyTheme(this, b, this.cname);
    }
  });
  a.Label = a.Class({
    construct: function (b) {
      this.cname = 'Label';
      a.applyTheme(this, b, this.cname);
    }
  });
})();
(function () {
  var a = window.AmCharts;
  a.AmBalloon = a.Class({
    construct: function (b) {
      this.cname = 'AmBalloon';
      this.enabled = !0;
      this.fillColor = '#FFFFFF';
      this.fillAlpha = 0.8;
      this.borderThickness = 2;
      this.borderColor = '#FFFFFF';
      this.borderAlpha = 1;
      this.cornerRadius = 0;
      this.maxWidth = 220;
      this.horizontalPadding = 8;
      this.verticalPadding = 4;
      this.pointerWidth = 6;
      this.pointerOrientation = 'V';
      this.color = '#000000';
      this.adjustBorderColor = !0;
      this.show = this.follow = this.showBullet = !1;
      this.bulletSize = 3;
      this.shadowAlpha = 0.4;
      this.shadowColor = '#000000';
      this.fadeOutDuration = this.animationDuration = 0.3;
      this.fixedPosition = !0;
      this.offsetY = 6;
      this.offsetX = 1;
      this.textAlign = 'center';
      this.disableMouseEvents = !0;
      this.deltaSignX = this.deltaSignY = 1;
      a.isModern || (this.offsetY *= 1.5);
      this.sdy = this.sdx = 0;
      a.applyTheme(this, b, this.cname);
    },
    draw: function () {
      var al = this.pointToX,
        ak = this.pointToY;
      a.isModern || (this.drop = !1);
      var aj = this.chart;
      a.VML && (this.fadeOutDuration = 0);
      this.xAnim && aj.stopAnim(this.xAnim);
      this.yAnim && aj.stopAnim(this.yAnim);
      this.sdy = this.sdx = 0;
      if (!isNaN(al)) {
        var ai = this.follow,
          ah = aj.container,
          ag = this.set;
        a.remove(ag);
        this.removeDiv();
        ag = ah.set();
        ag.node.style.pointerEvents = 'none';
        this.set = ag;
        this.mainSet ? (this.mainSet.push(this.set), (this.sdx = this.mainSet.x), (this.sdy = this.mainSet.y)) : aj.balloonsSet.push(ag);
        if (this.show) {
          var af = this.l,
            ae = this.t,
            ad = this.r,
            ac = this.b,
            ab = this.balloonColor,
            aa = this.fillColor,
            T = this.borderColor,
            W = aa;
          void 0 != ab && (this.adjustBorderColor ? (W = T = ab) : (aa = ab));
          var Y = this.horizontalPadding,
            s = this.verticalPadding,
            X = this.pointerWidth,
            R = this.pointerOrientation,
            N = this.cornerRadius,
            P = aj.fontFamily,
            Z = this.fontSize;
          void 0 == Z && (Z = aj.fontSize);
          var ab = document.createElement('div'),
            V = aj.classNamePrefix;
          ab.className = V + '-balloon-div';
          this.className && (ab.className = ab.className + ' ' + V + '-balloon-div-' + this.className);
          V = ab.style;
          this.disableMouseEvents && (V.pointerEvents = 'none');
          V.position = 'absolute';
          var I = this.minWidth,
            j = document.createElement('div');
          ab.appendChild(j);
          var Q = j.style;
          isNaN(I) || (Q.minWidth = I - 2 * Y + 'px');
          Q.textAlign = this.textAlign;
          Q.maxWidth = this.maxWidth + 'px';
          Q.fontSize = Z + 'px';
          Q.color = this.color;
          Q.fontFamily = P;
          j.innerHTML = this.text;
          aj.chartDiv.appendChild(ab);
          this.textDiv = ab;
          var Q = ab.offsetWidth,
            S = ab.offsetHeight;
          ab.clientHeight && ((Q = ab.clientWidth), (S = ab.clientHeight));
          P = S + 2 * s;
          j = Q + 2 * Y;
          !isNaN(I) && j < I && (j = I);
          window.opera && (P += 2);
          var M = !1,
            Z = this.offsetY;
          aj.handDrawn && (Z += aj.handDrawScatter + 2);
          'H' != R
            ? ((I = al - j / 2),
              ak < ae + P + 10 && 'down' != R
                ? ((M = !0), ai && (ak += Z), (Z = ak + X), (this.deltaSignY = -1))
                : (ai && (ak -= Z), (Z = ak - P - X), (this.deltaSignY = 1)))
            : (2 * X > P && (X = P / 2),
              (Z = ak - P / 2),
              al < af + (ad - af) / 2 ? ((I = al + X), (this.deltaSignX = -1)) : ((I = al - j - X), (this.deltaSignX = 1)));
          Z + P >= ac && (Z = ac - P);
          Z < ae && (Z = ae);
          I < af && (I = af);
          I + j > ad && (I = ad - j);
          var ae = Z + s,
            ac = I + Y,
            O = this.shadowAlpha,
            U = this.shadowColor,
            Y = this.borderThickness,
            i = this.bulletSize,
            o,
            s = this.fillAlpha,
            d = this.borderAlpha;
          this.showBullet && ((o = a.circle(ah, i, W, s)), ag.push(o));
          this.drop
            ? ((af = j / 1.6),
              (ad = 0),
              'V' == R && (R = 'down'),
              'H' == R && (R = 'left'),
              'down' == R && ((I = al + 1), (Z = ak - af - af / 3)),
              'up' == R && ((ad = 180), (I = al + 1), (Z = ak + af + af / 3)),
              'left' == R && ((ad = 270), (I = al + af + af / 3 + 2), (Z = ak)),
              'right' == R && ((ad = 90), (I = al - af - af / 3 + 2), (Z = ak)),
              (ae = Z - S / 2 + 1),
              (ac = I - Q / 2 - 1),
              (aa = a.drop(ah, af, ad, aa, s, Y, T, d)))
            : 0 < N || 0 === X
              ? (0 < O &&
                  ((al = a.rect(ah, j, P, aa, 0, Y + 1, U, O, N)), a.isModern ? al.translate(1, 1) : al.translate(4, 4), ag.push(al)),
                (aa = a.rect(ah, j, P, aa, s, Y, T, d, N)))
              : ((W = []),
                (N = []),
                'H' != R
                  ? ((af = al - I),
                    af > j - X && (af = j - X),
                    af < X && (af = X),
                    (W = [0, af - X, al - I, af + X, j, j, 0, 0]),
                    (N = M ? [0, 0, ak - Z, 0, 0, P, P, 0] : [P, P, ak - Z, P, P, 0, 0, P]))
                  : ((R = ak - Z),
                    R > P - X && (R = P - X),
                    R < X && (R = X),
                    (N = [0, R - X, ak - Z, R + X, P, P, 0, 0]),
                    (W =
                      al < af + (ad - af) / 2
                        ? [0, 0, I < al ? 0 : al - I, 0, 0, j, j, 0]
                        : [j, j, I + j > al ? j : al - I, j, j, 0, 0, j])),
                0 < O && ((al = a.polygon(ah, W, N, aa, 0, Y, U, O)), al.translate(1, 1), ag.push(al)),
                (aa = a.polygon(ah, W, N, aa, s, Y, T, d)));
          this.bg = aa;
          ag.push(aa);
          aa.toFront();
          a.setCN(aj, aa, 'balloon-bg');
          this.className && a.setCN(aj, aa, 'balloon-bg-' + this.className);
          ah = 1 * this.deltaSignX;
          ac += this.sdx;
          ae += this.sdy;
          V.left = ac + 'px';
          V.top = ae + 'px';
          ag.translate(I - ah, Z, 1, !0);
          aa = aa.getBBox();
          this.bottom = Z + P + 1;
          this.yPos = aa.y + Z;
          o && o.translate(this.pointToX - I + ah, ak - Z);
          ak = this.animationDuration;
          0 < this.animationDuration &&
            !ai &&
            !isNaN(this.prevX) &&
            (ag.translate(this.prevX, this.prevY, NaN, !0),
            ag.animate({ translate: I - ah + ',' + Z }, ak, 'easeOutSine'),
            ab &&
              ((V.left = this.prevTX + 'px'),
              (V.top = this.prevTY + 'px'),
              (this.xAnim = aj.animate({ node: ab }, 'left', this.prevTX, ac, ak, 'easeOutSine', 'px')),
              (this.yAnim = aj.animate({ node: ab }, 'top', this.prevTY, ae, ak, 'easeOutSine', 'px'))));
          this.prevX = I - ah;
          this.prevY = Z;
          this.prevTX = ac;
          this.prevTY = ae;
        }
      }
    },
    fixPrevious: function () {
      this.rPrevX = this.prevX;
      this.rPrevY = this.prevY;
      this.rPrevTX = this.prevTX;
      this.rPrevTY = this.prevTY;
    },
    restorePrevious: function () {
      this.prevX = this.rPrevX;
      this.prevY = this.rPrevY;
      this.prevTX = this.rPrevTX;
      this.prevTY = this.rPrevTY;
    },
    followMouse: function () {
      if (this.follow && this.show) {
        var g = this.chart.mouseX - this.offsetX * this.deltaSignX - this.sdx,
          f = this.chart.mouseY - this.sdy;
        this.pointToX = g;
        this.pointToY = f;
        if (g != this.previousX || f != this.previousY) {
          if (((this.previousX = g), (this.previousY = f), 0 === this.cornerRadius)) {
            this.draw();
          } else {
            var j = this.set;
            if (j) {
              var h = j.getBBox(),
                g = g - h.width / 2,
                i = f - h.height - 10;
              g < this.l && (g = this.l);
              g > this.r - h.width && (g = this.r - h.width);
              i < this.t && (i = f + 10);
              j.translate(g, i);
              f = this.textDiv.style;
              f.left = g + this.horizontalPadding + 'px';
              f.top = i + this.verticalPadding + 'px';
            }
          }
        }
      }
    },
    changeColor: function (b) {
      this.balloonColor = b;
    },
    setBounds: function (f, d, h, g) {
      this.l = f;
      this.t = d;
      this.r = h;
      this.b = g;
      this.destroyTO && clearTimeout(this.destroyTO);
    },
    showBalloon: function (b) {
      if (this.text != b || this.positionChanged) {
        (this.text = b),
          (this.isHiding = !1),
          (this.show = !0),
          this.destroyTO && clearTimeout(this.destroyTO),
          (b = this.chart),
          this.fadeAnim1 && b.stopAnim(this.fadeAnim1),
          this.fadeAnim2 && b.stopAnim(this.fadeAnim2),
          this.draw(),
          (this.positionChanged = !1);
      }
    },
    hide: function (f) {
      var d = this;
      d.text = void 0;
      isNaN(f) && (f = d.fadeOutDuration);
      var h = d.chart;
      if (0 < f && !d.isHiding) {
        d.isHiding = !0;
        d.destroyTO && clearTimeout(d.destroyTO);
        d.destroyTO = setTimeout(function () {
          d.destroy.call(d);
        }, 1000 * f);
        d.follow = !1;
        d.show = !1;
        var g = d.set;
        g && (g.setAttr('opacity', d.fillAlpha), (d.fadeAnim1 = g.animate({ opacity: 0 }, f, 'easeInSine')));
        d.textDiv && (d.fadeAnim2 = h.animate({ node: d.textDiv }, 'opacity', 1, 0, f, 'easeInSine', ''));
      } else {
        (d.show = !1), (d.follow = !1), d.destroy();
      }
    },
    setPosition: function (d, c) {
      if (d != this.pointToX || c != this.pointToY) {
        (this.previousX = this.pointToX),
          (this.previousY = this.pointToY),
          (this.pointToX = d),
          (this.pointToY = c),
          (this.positionChanged = !0);
      }
    },
    followCursor: function (f) {
      var d = this;
      d.follow = f;
      clearInterval(d.interval);
      var h = d.chart.mouseX - d.sdx,
        g = d.chart.mouseY - d.sdy;
      !isNaN(h) &&
        f &&
        ((d.pointToX = h - d.offsetX * d.deltaSignX),
        (d.pointToY = g),
        d.followMouse(),
        (d.interval = setInterval(function () {
          d.followMouse.call(d);
        }, 40)));
    },
    removeDiv: function () {
      if (this.textDiv) {
        var b = this.textDiv.parentNode;
        b && b.removeChild(this.textDiv);
      }
    },
    destroy: function () {
      clearInterval(this.interval);
      a.remove(this.set);
      this.removeDiv();
      this.set = null;
    }
  });
})();
(function () {
  var a = window.AmCharts;
  a.circle = function (r, q, p, o, n, m, j, i, d) {
    0 >= q && (q = 0.001);
    if (void 0 == n || 0 === n) {
      n = 0.01;
    }
    void 0 === m && (m = '#000000');
    void 0 === j && (j = 0);
    o = {
      fill: p,
      stroke: m,
      'fill-opacity': o,
      'stroke-width': n,
      'stroke-opacity': j
    };
    r = isNaN(d) ? r.circle(0, 0, q).attr(o) : r.ellipse(0, 0, q, d).attr(o);
    i && r.gradient('radialGradient', [p, a.adjustLuminosity(p, -0.6)]);
    return r;
  };
  a.text = function (i, d, p, o, n, m, l, j) {
    m || (m = 'middle');
    'right' == m && (m = 'end');
    'left' == m && (m = 'start');
    isNaN(j) && (j = 1);
    void 0 !== d && ((d = String(d)), a.isIE && !a.isModern && ((d = d.replace('&amp;', '&')), (d = d.replace('&', '&amp;'))));
    p = { fill: p, 'font-family': o, 'font-size': n + 'px', opacity: j };
    !0 === l && (p['font-weight'] = 'bold');
    p['text-anchor'] = m;
    return i.text(d, p);
  };
  a.polygon = function (A, z, y, x, w, v, u, s, q, o, j) {
    isNaN(v) && (v = 0.01);
    isNaN(s) && (s = w);
    var i = x,
      B = !1;
    'object' == typeof i && 1 < i.length && ((B = !0), (i = i[0]));
    void 0 === u && (u = i);
    w = {
      fill: i,
      stroke: u,
      'fill-opacity': w,
      'stroke-width': v,
      'stroke-opacity': s
    };
    void 0 !== j && 0 < j && (w['stroke-dasharray'] = j);
    j = a.dx;
    v = a.dy;
    A.handDrawn && ((y = a.makeHD(z, y, A.handDrawScatter)), (z = y[0]), (y = y[1]));
    u = Math.round;
    o && ((z[d] = a.roundTo(z[d], 5)), (y[d] = a.roundTo(y[d], 5)), (u = Number));
    s = 'M' + (u(z[0]) + j) + ',' + (u(y[0]) + v);
    for (var d = 1; d < z.length; d++) {
      o && ((z[d] = a.roundTo(z[d], 5)), (y[d] = a.roundTo(y[d], 5))), (s += ' L' + (u(z[d]) + j) + ',' + (u(y[d]) + v));
    }
    A = A.path(s + ' Z').attr(w);
    B && A.gradient('linearGradient', x, q);
    return A;
  };
  a.rect = function (C, B, A, z, y, x, w, v, u, s, o) {
    if (isNaN(B) || isNaN(A)) {
      return C.set();
    }
    isNaN(x) && (x = 0);
    void 0 === u && (u = 0);
    void 0 === s && (s = 270);
    isNaN(y) && (y = 0);
    var j = z,
      D = !1;
    'object' == typeof j && ((j = j[0]), (D = !0));
    void 0 === w && (w = j);
    void 0 === v && (v = y);
    B = Math.round(B);
    A = Math.round(A);
    var d = 0,
      i = 0;
    0 > B && ((B = Math.abs(B)), (d = -B));
    0 > A && ((A = Math.abs(A)), (i = -A));
    d += a.dx;
    i += a.dy;
    y = { fill: j, stroke: w, 'fill-opacity': y, 'stroke-opacity': v };
    void 0 !== o && 0 < o && (y['stroke-dasharray'] = o);
    C = C.rect(d, i, B, A, u, x).attr(y);
    D && C.gradient('linearGradient', z, s);
    return C;
  };
  a.bullet = function (A, z, y, x, w, v, u, s, q, o, j, i, B) {
    var d;
    'circle' == z && (z = 'round');
    switch (z) {
      case 'round':
        d = a.circle(A, y / 2, x, w, v, u, s);
        break;
      case 'square':
        d = a.polygon(A, [-y / 2, y / 2, y / 2, -y / 2], [y / 2, y / 2, -y / 2, -y / 2], x, w, v, u, s, o - 180, void 0, B);
        break;
      case 'rectangle':
        d = a.polygon(A, [-y, y, y, -y], [y / 2, y / 2, -y / 2, -y / 2], x, w, v, u, s, o - 180, void 0, B);
        break;
      case 'diamond':
        d = a.polygon(A, [-y / 2, 0, y / 2, 0], [0, -y / 2, 0, y / 2], x, w, v, u, s);
        break;
      case 'triangleUp':
        d = a.triangle(A, y, 0, x, w, v, u, s);
        break;
      case 'triangleDown':
        d = a.triangle(A, y, 180, x, w, v, u, s);
        break;
      case 'triangleLeft':
        d = a.triangle(A, y, 270, x, w, v, u, s);
        break;
      case 'triangleRight':
        d = a.triangle(A, y, 90, x, w, v, u, s);
        break;
      case 'bubble':
        d = a.circle(A, y / 2, x, w, v, u, s, !0);
        break;
      case 'line':
        d = a.line(A, [-y / 2, y / 2], [0, 0], x, w, v, u, s);
        break;
      case 'yError':
        d = A.set();
        d.push(a.line(A, [0, 0], [-y / 2, y / 2], x, w, v));
        d.push(a.line(A, [-q, q], [-y / 2, -y / 2], x, w, v));
        d.push(a.line(A, [-q, q], [y / 2, y / 2], x, w, v));
        break;
      case 'xError':
        (d = A.set()),
          d.push(a.line(A, [-y / 2, y / 2], [0, 0], x, w, v)),
          d.push(a.line(A, [-y / 2, -y / 2], [-q, q], x, w, v)),
          d.push(a.line(A, [y / 2, y / 2], [-q, q], x, w, v));
    }
    d && d.pattern(j, NaN, i);
    return d;
  };
  a.triangle = function (r, q, p, n, o, m, j, i) {
    if (void 0 === m || 0 === m) {
      m = 1;
    }
    void 0 === j && (j = '#000');
    void 0 === i && (i = 0);
    n = {
      fill: n,
      stroke: j,
      'fill-opacity': o,
      'stroke-width': m,
      'stroke-opacity': i
    };
    q /= 2;
    var f;
    0 === p && (f = ' M' + -q + ',' + q + ' L0,' + -q + ' L' + q + ',' + q + ' Z');
    180 == p && (f = ' M' + -q + ',' + -q + ' L0,' + q + ' L' + q + ',' + -q + ' Z');
    90 == p && (f = ' M' + -q + ',' + -q + ' L' + q + ',0 L' + -q + ',' + q + ' Z');
    270 == p && (f = ' M' + -q + ',0 L' + q + ',' + q + ' L' + q + ',' + -q + ' Z');
    return r.path(f).attr(n);
  };
  a.line = function (v, u, t, s, r, q, p, o, j, i, d) {
    if (v.handDrawn && !d) {
      return a.handDrawnLine(v, u, t, s, r, q, p, o, j, i, d);
    }
    q = { fill: 'none', 'stroke-width': q };
    void 0 !== p && 0 < p && (q['stroke-dasharray'] = p);
    isNaN(r) || (q['stroke-opacity'] = r);
    s && (q.stroke = s);
    s = Math.round;
    i && ((s = Number), (u[0] = a.roundTo(u[0], 5)), (t[0] = a.roundTo(t[0], 5)));
    i = a.dx;
    r = a.dy;
    p = 'M' + (s(u[0]) + i) + ',' + (s(t[0]) + r);
    for (o = 1; o < u.length; o++) {
      (u[o] = a.roundTo(u[o], 5)), (t[o] = a.roundTo(t[o], 5)), (p += ' L' + (s(u[o]) + i) + ',' + (s(t[o]) + r));
    }
    if (a.VML) {
      return v.path(p, void 0, !0).attr(q);
    }
    j && (p += ' M0,0 L0,0');
    return v.path(p).attr(q);
  };
  a.makeHD = function (y, x, w) {
    for (var u = [], v = [], s = 1; s < y.length; s++) {
      for (
        var r = Number(y[s - 1]),
          q = Number(x[s - 1]),
          o = Number(y[s]),
          j = Number(x[s]),
          i = Math.round(Math.sqrt(Math.pow(o - r, 2) + Math.pow(j - q, 2)) / 50) + 1,
          o = (o - r) / i,
          j = (j - q) / i,
          f = 0;
        f <= i;
        f++
      ) {
        var z = q + f * j + Math.random() * w;
        u.push(r + f * o + Math.random() * w);
        v.push(z);
      }
    }
    return [u, v];
  };
  a.handDrawnLine = function (C, B, A, z, y, x, w, v, u, s) {
    var o,
      j = C.set();
    for (o = 1; o < B.length; o++) {
      for (
        var D = [B[o - 1], B[o]], d = [A[o - 1], A[o]], d = a.makeHD(D, d, C.handDrawScatter), D = d[0], d = d[1], i = 1;
        i < D.length;
        i++
      ) {
        j.push(
          a.line(
            C,
            [D[i - 1], D[i]],
            [d[i - 1], d[i]],
            z,
            y,
            x + Math.random() * C.handDrawThickness - C.handDrawThickness / 2,
            w,
            v,
            u,
            s,
            !0
          )
        );
      }
    }
    return j;
  };
  a.doNothing = function (b) {
    return b;
  };
  a.drop = function (G, F, D, A, C, z, x, w) {
    var v = (1 / 180) * Math.PI,
      u = D - 20,
      s = Math.sin(u * v) * F,
      o = Math.cos(u * v) * F,
      H = Math.sin((u + 40) * v) * F,
      f = Math.cos((u + 40) * v) * F,
      i = 0.8 * F,
      E = -F / 3,
      j = F / 3;
    0 === D && ((E = -E), (j = 0));
    180 == D && (j = 0);
    90 == D && (E = 0);
    270 == D && ((E = 0), (j = -j));
    D = {
      fill: A,
      stroke: x,
      'stroke-width': z,
      'stroke-opacity': w,
      'fill-opacity': C
    };
    F =
      'M' +
      s +
      ',' +
      o +
      ' A' +
      F +
      ',' +
      F +
      ',0,1,1,' +
      H +
      ',' +
      f +
      (' A' + i + ',' + i + ',0,0,0,' + (Math.sin((u + 20) * v) * F + j) + ',' + (Math.cos((u + 20) * v) * F + E));
    F += ' A' + i + ',' + i + ',0,0,0,' + s + ',' + o;
    return G.path(F, void 0, void 0, '1000,1000').attr(D);
  };
  a.wedge = function (al, ak, aj, ai, ah, ag, af, ae, ad, ac, ab, aa, T, W) {
    var Y = Math.round;
    ag = Y(ag);
    af = Y(af);
    ae = Y(ae);
    var s = Y((af / ag) * ae),
      X = a.VML,
      R = 359.5 + ag / 100;
    359.94 < R && (R = 359.94);
    ah >= R && (ah = R);
    var N = (1 / 180) * Math.PI,
      R = ak + Math.sin(ai * N) * ae,
      P = aj - Math.cos(ai * N) * s,
      Z = ak + Math.sin(ai * N) * ag,
      V = aj - Math.cos(ai * N) * af,
      I = ak + Math.sin((ai + ah) * N) * ag,
      j = aj - Math.cos((ai + ah) * N) * af,
      Q = ak + Math.sin((ai + ah) * N) * ae,
      N = aj - Math.cos((ai + ah) * N) * s,
      S = {
        fill: a.adjustLuminosity(ac.fill, -0.2),
        'stroke-opacity': 0,
        'fill-opacity': ac['fill-opacity']
      },
      M = 0;
    180 < Math.abs(ah) && (M = 1);
    ai = al.set();
    var O;
    X &&
      ((R = Y(10 * R)),
      (Z = Y(10 * Z)),
      (I = Y(10 * I)),
      (Q = Y(10 * Q)),
      (P = Y(10 * P)),
      (V = Y(10 * V)),
      (j = Y(10 * j)),
      (N = Y(10 * N)),
      (ak = Y(10 * ak)),
      (ad = Y(10 * ad)),
      (aj = Y(10 * aj)),
      (ag *= 10),
      (af *= 10),
      (ae *= 10),
      (s *= 10),
      1 > Math.abs(ah) && 1 >= Math.abs(I - Z) && 1 >= Math.abs(j - V) && (O = !0));
    ah = '';
    var U;
    aa && ((S['fill-opacity'] = 0), (S['stroke-opacity'] = ac['stroke-opacity'] / 2), (S.stroke = ac.stroke));
    if (0 < ad) {
      U = ' M' + R + ',' + (P + ad) + ' L' + Z + ',' + (V + ad);
      X
        ? (O ||
            (U +=
              ' A' +
              (ak - ag) +
              ',' +
              (ad + aj - af) +
              ',' +
              (ak + ag) +
              ',' +
              (ad + aj + af) +
              ',' +
              Z +
              ',' +
              (V + ad) +
              ',' +
              I +
              ',' +
              (j + ad)),
          (U += ' L' + Q + ',' + (N + ad)),
          0 < ae &&
            (O ||
              (U +=
                ' B' +
                (ak - ae) +
                ',' +
                (ad + aj - s) +
                ',' +
                (ak + ae) +
                ',' +
                (ad + aj + s) +
                ',' +
                Q +
                ',' +
                (ad + N) +
                ',' +
                R +
                ',' +
                (ad + P))))
        : ((U += ' A' + ag + ',' + af + ',0,' + M + ',1,' + I + ',' + (j + ad) + ' L' + Q + ',' + (N + ad)),
          0 < ae && (U += ' A' + ae + ',' + s + ',0,' + M + ',0,' + R + ',' + (P + ad)));
      U += ' Z';
      var i = ad;
      X && (i /= 10);
      for (var o = 0; o < i; o += 10) {
        var d = al.path(U, void 0, void 0, '1000,1000').attr(S);
        ai.push(d);
        d.translate(0, -o);
      }
      U = al
        .path(
          ' M' + R + ',' + P + ' L' + R + ',' + (P + ad) + ' L' + Z + ',' + (V + ad) + ' L' + Z + ',' + V + ' L' + R + ',' + P + ' Z',
          void 0,
          void 0,
          '1000,1000'
        )
        .attr(S);
      ad = al
        .path(
          ' M' + I + ',' + j + ' L' + I + ',' + (j + ad) + ' L' + Q + ',' + (N + ad) + ' L' + Q + ',' + N + ' L' + I + ',' + j + ' Z',
          void 0,
          void 0,
          '1000,1000'
        )
        .attr(S);
      ai.push(U);
      ai.push(ad);
    }
    X
      ? (O ||
          (ah =
            ' A' + Y(ak - ag) + ',' + Y(aj - af) + ',' + Y(ak + ag) + ',' + Y(aj + af) + ',' + Y(Z) + ',' + Y(V) + ',' + Y(I) + ',' + Y(j)),
        (af = ' M' + Y(R) + ',' + Y(P) + ' L' + Y(Z) + ',' + Y(V) + ah + ' L' + Y(Q) + ',' + Y(N)))
      : (af = ' M' + R + ',' + P + ' L' + Z + ',' + V + (' A' + ag + ',' + af + ',0,' + M + ',1,' + I + ',' + j) + ' L' + Q + ',' + N);
    0 < ae &&
      (X
        ? O || (af += ' B' + (ak - ae) + ',' + (aj - s) + ',' + (ak + ae) + ',' + (aj + s) + ',' + Q + ',' + N + ',' + R + ',' + P)
        : (af += ' A' + ae + ',' + s + ',0,' + M + ',0,' + R + ',' + P));
    al.handDrawn &&
      ((ae = a.line(al, [R, Z], [P, V], ac.stroke, ac.thickness * Math.random() * al.handDrawThickness, ac['stroke-opacity'])),
      ai.push(ae));
    al = al.path(af + ' Z', void 0, void 0, '1000,1000').attr(ac);
    if (ab) {
      ae = [];
      for (s = 0; s < ab.length; s++) {
        ae.push(a.adjustLuminosity(ac.fill, ab[s]));
      }
      'radial' != W || a.isModern || (ae = []);
      0 < ae.length && al.gradient(W + 'Gradient', ae);
    }
    a.isModern &&
      'radial' == W &&
      al.grad &&
      (al.grad.setAttribute('gradientUnits', 'userSpaceOnUse'),
      al.grad.setAttribute('r', ag),
      al.grad.setAttribute('cx', ak),
      al.grad.setAttribute('cy', aj));
    al.pattern(aa, NaN, T);
    ai.wedge = al;
    ai.push(al);
    return ai;
  };
  a.rgb2hex = function (b) {
    return (b = b.match(/^rgba?[\s+]?\([\s+]?(\d+)[\s+]?,[\s+]?(\d+)[\s+]?,[\s+]?(\d+)[\s+]?/i)) && 4 === b.length
      ? '#' +
          ('0' + parseInt(b[1], 10).toString(16)).slice(-2) +
          ('0' + parseInt(b[2], 10).toString(16)).slice(-2) +
          ('0' + parseInt(b[3], 10).toString(16)).slice(-2)
      : '';
  };
  a.adjustLuminosity = function (g, d) {
    g && -1 != g.indexOf('rgb') && (g = a.rgb2hex(g));
    g = String(g).replace(/[^0-9a-f]/gi, '');
    6 > g.length && (g = String(g[0]) + String(g[0]) + String(g[1]) + String(g[1]) + String(g[2]) + String(g[2]));
    d = d || 0;
    var j = '#',
      i,
      h;
    for (h = 0; 3 > h; h++) {
      (i = parseInt(g.substr(2 * h, 2), 16)),
        (i = Math.round(Math.min(Math.max(0, i + i * d), 255)).toString(16)),
        (j += ('00' + i).substr(i.length));
    }
    return j;
  };
})();
(function () {
  var a = window.AmCharts;
  a.AmLegend = a.Class({
    construct: function (b) {
      this.enabled = !0;
      this.cname = 'AmLegend';
      this.createEvents(
        'rollOverMarker',
        'rollOverItem',
        'rollOutMarker',
        'rollOutItem',
        'showItem',
        'hideItem',
        'clickMarker',
        'clickLabel'
      );
      this.position = 'bottom';
      this.borderColor = this.color = '#000000';
      this.borderAlpha = 0;
      this.markerLabelGap = 5;
      this.verticalGap = 10;
      this.align = 'left';
      this.horizontalGap = 0;
      this.spacing = 10;
      this.markerDisabledColor = '#AAB3B3';
      this.markerType = 'square';
      this.markerSize = 16;
      this.markerBorderThickness = this.markerBorderAlpha = 1;
      this.marginBottom = this.marginTop = 0;
      this.marginLeft = this.marginRight = 20;
      this.autoMargins = !0;
      this.valueWidth = 50;
      this.switchable = !0;
      this.switchType = 'x';
      this.switchColor = '#FFFFFF';
      this.rollOverColor = '#CC0000';
      this.reversedOrder = !1;
      this.labelText = '[[title]]';
      this.valueText = '[[value]]';
      this.accessibleLabel = '[[title]]';
      this.useMarkerColorForLabels = !1;
      this.rollOverGraphAlpha = 1;
      this.textClickEnabled = !1;
      this.equalWidths = !0;
      this.backgroundColor = '#FFFFFF';
      this.backgroundAlpha = 0;
      this.useGraphSettings = !1;
      this.showEntries = !0;
      this.labelDx = 0;
      a.applyTheme(this, b, this.cname);
    },
    setData: function (b) {
      this.legendData = b;
      this.invalidateSize();
    },
    invalidateSize: function () {
      this.destroy();
      this.entries = [];
      this.valueLabels = [];
      var b = this.legendData;
      this.enabled && (a.ifArray(b) || a.ifArray(this.data)) && this.drawLegend();
    },
    drawLegend: function () {
      var i = this.chart,
        d = this.position,
        p = this.width,
        o = i.divRealWidth,
        n = i.divRealHeight,
        m = this.div,
        l = this.legendData;
      this.data && (l = this.combineLegend ? this.legendData.concat(this.data) : this.data);
      isNaN(this.fontSize) && (this.fontSize = i.fontSize);
      this.maxColumnsReal = this.maxColumns;
      if ('right' == d || 'left' == d) {
        (this.maxColumnsReal = 1), this.autoMargins && (this.marginLeft = this.marginRight = 10);
      } else {
        if (this.autoMargins) {
          this.marginRight = i.marginRight;
          this.marginLeft = i.marginLeft;
          var j = i.autoMarginOffset;
          'bottom' == d ? ((this.marginBottom = j), (this.marginTop = 0)) : ((this.marginTop = j), (this.marginBottom = 0));
        }
      }
      p = void 0 !== p ? a.toCoordinate(p, o) : 'right' != d && 'left' != d ? i.realWidth : 0 < this.ieW ? this.ieW : i.realWidth;
      'outside' == d
        ? ((p = m.offsetWidth), (n = m.offsetHeight), m.clientHeight && ((p = m.clientWidth), (n = m.clientHeight)))
        : (isNaN(p) || (m.style.width = p + 'px'), (m.className = 'amChartsLegend ' + i.classNamePrefix + '-legend-div'));
      this.divWidth = p;
      (d = this.container)
        ? ((d.container.innerHTML = ''), m.appendChild(d.container), (d.width = p), (d.height = n), d.setSize(p, n), d.addDefs(i))
        : (d = new a.AmDraw(m, p, n, i));
      this.container = d;
      this.lx = 0;
      this.ly = 8;
      n = this.markerSize;
      n > this.fontSize && (this.ly = n / 2 - 1);
      0 < n && (this.lx += n + this.markerLabelGap);
      this.titleWidth = 0;
      if ((n = this.title)) {
        (n = a.text(this.container, n, this.color, i.fontFamily, this.fontSize, 'start', !0)),
          a.setCN(i, n, 'legend-title'),
          n.translate(this.marginLeft, this.marginTop + this.verticalGap + this.ly + 1),
          (i = n.getBBox()),
          (this.titleWidth = i.width + 15),
          (this.titleHeight = i.height + 6);
      }
      this.index = this.maxLabelWidth = 0;
      if (this.showEntries) {
        for (i = 0; i < l.length; i++) {
          this.createEntry(l[i]);
        }
        for (i = this.index = 0; i < l.length; i++) {
          this.createValue(l[i]);
        }
      }
      this.arrangeEntries();
      this.updateValues();
    },
    arrangeEntries: function () {
      var Z = this.position,
        Y = this.marginLeft + this.titleWidth,
        X = this.marginRight,
        W = this.marginTop,
        V = this.marginBottom,
        U = this.horizontalGap,
        T = this.div,
        S = this.divWidth,
        R = this.maxColumnsReal,
        Q = this.verticalGap,
        P = this.spacing,
        O = S - X - Y,
        I = 0,
        K = 0,
        M = this.container;
      this.set && this.set.remove();
      var i = M.set();
      this.set = i;
      var L = M.set();
      i.push(L);
      var G = this.entries,
        o,
        s;
      for (s = 0; s < G.length; s++) {
        o = G[s].getBBox();
        var N = o.width;
        N > I && (I = N);
        o = o.height;
        o > K && (K = o);
      }
      var N = (K = 0),
        J = U,
        j = 0,
        d = 0;
      for (s = 0; s < G.length; s++) {
        var D = G[s];
        this.reversedOrder && (D = G[G.length - s - 1]);
        o = D.getBBox();
        var H;
        this.equalWidths ? (H = N * (I + P + this.markerLabelGap)) : ((H = J), (J = J + o.width + U + P));
        H + o.width > O && 0 < s && 0 !== N && (K++, (H = N = 0), (J = H + o.width + U + P), (j = j + d + Q), (d = 0));
        o.height > d && (d = o.height);
        D.translate(H, j);
        N++;
        !isNaN(R) && N >= R && ((N = 0), K++, (j = j + d + Q), (J = U), (d = 0));
        L.push(D);
      }
      o = L.getBBox();
      R = o.height + 2 * Q - 1;
      'left' == Z || 'right' == Z
        ? ((P = o.width + 2 * U), (S = P + Y + X), (T.style.width = S + 'px'), (this.ieW = S))
        : (P = S - Y - X - 1);
      X = a.polygon(
        this.container,
        [0, P, P, 0],
        [0, 0, R, R],
        this.backgroundColor,
        this.backgroundAlpha,
        1,
        this.borderColor,
        this.borderAlpha
      );
      a.setCN(this.chart, X, 'legend-bg');
      i.push(X);
      i.translate(Y, W);
      X.toBack();
      Y = U;
      if ('top' == Z || 'bottom' == Z || 'absolute' == Z || 'outside' == Z) {
        'center' == this.align ? (Y = U + (P - o.width) / 2) : 'right' == this.align && (Y = U + P - o.width);
      }
      L.translate(Y, Q + 1);
      this.titleHeight > R && (R = this.titleHeight);
      W = R + W + V + 1;
      0 > W && (W = 0);
      'absolute' != Z && 'outside' != Z && W > this.chart.divRealHeight && (T.style.top = '0px');
      T.style.height = Math.round(W) + 'px';
      M.setSize(this.divWidth, W);
    },
    createEntry: function (L) {
      if (!1 !== L.visibleInLegend && !L.hideFromLegend) {
        var K = this,
          J = K.chart,
          I = K.useGraphSettings,
          H = L.markerType;
        H && (I = !1);
        L.legendEntryWidth = K.markerSize;
        H || (H = K.markerType);
        var G = L.color,
          F = L.alpha;
        L.legendKeyColor && (G = L.legendKeyColor());
        L.legendKeyAlpha && (F = L.legendKeyAlpha());
        var E;
        !0 === L.hidden && (E = G = K.markerDisabledColor);
        var D = L.pattern,
          C,
          A = L.customMarker;
        A || (A = K.customMarker);
        var z = K.container,
          o = K.markerSize,
          s = 0,
          x = 0,
          d = o / 2;
        if (I) {
          I = L.type;
          K.switchType = void 0;
          if ('line' == I || 'step' == I || 'smoothedLine' == I || 'ohlc' == I) {
            (C = z.set()),
              L.hidden || ((G = L.lineColorR), (E = L.bulletBorderColorR)),
              (s = a.line(z, [0, 2 * o], [o / 2, o / 2], G, L.lineAlpha, L.lineThickness, L.dashLength)),
              a.setCN(J, s, 'graph-stroke'),
              C.push(s),
              L.bullet &&
                (L.hidden || (G = L.bulletColorR),
                (s = a.bullet(z, L.bullet, L.bulletSize, G, L.bulletAlpha, L.bulletBorderThickness, E, L.bulletBorderAlpha))) &&
                (a.setCN(J, s, 'graph-bullet'), s.translate(o + 1, o / 2), C.push(s)),
              (d = 0),
              (s = o),
              (x = o / 3);
          } else {
            L.getGradRotation && ((C = L.getGradRotation()), 0 === C && (C = 180));
            s = L.fillColorsR;
            !0 === L.hidden && (s = G);
            if ((C = K.createMarker('rectangle', s, L.fillAlphas, L.lineThickness, G, L.lineAlpha, C, D, L.dashLength))) {
              (d = o), C.translate(d, o / 2);
            }
            s = o;
          }
          a.setCN(J, C, 'graph-' + I);
          a.setCN(J, C, 'graph-' + L.id);
        } else {
          if (A) {
            C = z.image(A, 0, 0, o, o);
          } else {
            var v;
            isNaN(K.gradientRotation) || (v = 180 + K.gradientRotation);
            (C = K.createMarker(H, G, F, void 0, void 0, void 0, v, D)) && C.translate(o / 2, o / 2);
          }
        }
        a.setCN(J, C, 'legend-marker');
        K.addListeners(C, L);
        z = z.set([C]);
        K.switchable && L.switchable && z.setAttr('cursor', 'pointer');
        void 0 !== L.id && a.setCN(J, z, 'legend-item-' + L.id);
        a.setCN(J, z, L.className, !0);
        E = K.switchType;
        var j;
        E &&
          'none' != E &&
          0 < o &&
          ('x' == E ? ((j = K.createX()), j.translate(o / 2, o / 2)) : (j = K.createV()),
          (j.dItem = L),
          !0 !== L.hidden ? ('x' == E ? j.hide() : j.show()) : 'x' != E && j.hide(),
          K.switchable || j.hide(),
          K.addListeners(j, L),
          (L.legendSwitch = j),
          z.push(j),
          a.setCN(J, j, 'legend-switch'));
        E = K.color;
        L.showBalloon && K.textClickEnabled && void 0 !== K.selectedColor && (E = K.selectedColor);
        K.useMarkerColorForLabels && !D && (E = G);
        !0 === L.hidden && (E = K.markerDisabledColor);
        G = a.massReplace(K.labelText, { '[[title]]': L.title });
        void 0 !== K.tabIndex &&
          (z.setAttr('tabindex', K.tabIndex),
          z.setAttr('role', 'menuitem'),
          z.keyup(function (b) {
            13 == b.keyCode && K.clickMarker(L, b);
          }));
        J.accessible && K.accessibleLabel && ((D = a.massReplace(K.accessibleLabel, { '[[title]]': L.title })), J.makeAccessible(z, D));
        D = K.fontSize;
        C &&
          (o <= D && ((o = o / 2 + K.ly - D / 2 + (D + 2 - o) / 2 - x), C.translate(d, o), j && j.translate(j.x, o)),
          (L.legendEntryWidth = C.getBBox().width));
        var i;
        G &&
          ((G = a.fixBrakes(G)),
          (L.legendTextReal = G),
          (i = K.labelWidth),
          (i = isNaN(i)
            ? a.text(K.container, G, E, J.fontFamily, D, 'start')
            : a.wrappedText(K.container, G, E, J.fontFamily, D, 'start', !1, i, 0)),
          a.setCN(J, i, 'legend-label'),
          i.translate(K.lx + s, K.ly),
          z.push(i),
          (K.labelDx = s),
          (J = i.getBBox().width),
          K.maxLabelWidth < J && (K.maxLabelWidth = J));
        K.entries[K.index] = z;
        L.legendEntry = K.entries[K.index];
        L.legendMarker = C;
        L.legendLabel = i;
        K.index++;
      }
    },
    addListeners: function (e, d) {
      var f = this;
      e &&
        e
          .mouseover(function (b) {
            f.rollOverMarker(d, b);
          })
          .mouseout(function (b) {
            f.rollOutMarker(d, b);
          })
          .click(function (b) {
            f.clickMarker(d, b);
          });
    },
    rollOverMarker: function (d, c) {
      this.switchable && this.dispatch('rollOverMarker', d, c);
      this.dispatch('rollOverItem', d, c);
    },
    rollOutMarker: function (d, c) {
      this.switchable && this.dispatch('rollOutMarker', d, c);
      this.dispatch('rollOutItem', d, c);
    },
    clickMarker: function (d, c) {
      this.switchable && (!0 === d.hidden ? this.dispatch('showItem', d, c) : this.dispatch('hideItem', d, c));
      this.dispatch('clickMarker', d, c);
    },
    rollOverLabel: function (d, c) {
      d.hidden || (this.textClickEnabled && d.legendLabel && d.legendLabel.attr({ fill: this.rollOverColor }));
      this.dispatch('rollOverItem', d, c);
    },
    rollOutLabel: function (e, d) {
      if (!e.hidden && this.textClickEnabled && e.legendLabel) {
        var f = this.color;
        void 0 !== this.selectedColor && e.showBalloon && (f = this.selectedColor);
        this.useMarkerColorForLabels && ((f = e.lineColor), void 0 === f && (f = e.color));
        e.legendLabel.attr({ fill: f });
      }
      this.dispatch('rollOutItem', e, d);
    },
    clickLabel: function (d, c) {
      this.textClickEnabled
        ? d.hidden || this.dispatch('clickLabel', d, c)
        : this.switchable && (!0 === d.hidden ? this.dispatch('showItem', d, c) : this.dispatch('hideItem', d, c));
    },
    dispatch: function (e, d, f) {
      e = { type: e, dataItem: d, target: this, event: f, chart: this.chart };
      this.chart && this.chart.handleLegendEvent(e);
      this.fire(e);
    },
    createValue: function (r) {
      var q = this,
        p = q.fontSize,
        o = q.chart;
      if (!1 !== r.visibleInLegend && !r.hideFromLegend) {
        var n = q.maxLabelWidth;
        q.forceWidth && (n = q.labelWidth);
        q.equalWidths || (q.valueAlign = 'left');
        'left' == q.valueAlign && r.legendLabel && (n = r.legendLabel.getBBox().width);
        var m = n;
        if (q.valueText && 0 < q.valueWidth) {
          var j = q.color;
          q.useMarkerColorForValues && ((j = r.color), r.legendKeyColor && (j = r.legendKeyColor()));
          !0 === r.hidden && (j = q.markerDisabledColor);
          var i = q.valueText,
            n = n + q.lx + q.labelDx + q.markerLabelGap + q.valueWidth,
            d = 'end';
          'left' == q.valueAlign && ((n -= q.valueWidth), (d = 'start'));
          j = a.text(q.container, i, j, q.chart.fontFamily, p, d);
          a.setCN(o, j, 'legend-value');
          j.translate(n, q.ly);
          q.entries[q.index].push(j);
          m += q.valueWidth + 2 * q.markerLabelGap;
          j.dItem = r;
          q.valueLabels.push(j);
        }
        q.index++;
        o = q.markerSize;
        o < p + 7 && ((o = p + 7), a.VML && (o += 3));
        p = q.container.rect(r.legendEntryWidth, 0, m, o, 0, 0).attr({ stroke: 'none', fill: '#fff', 'fill-opacity': 0.005 });
        p.dItem = r;
        q.entries[q.index - 1].push(p);
        p.mouseover(function (b) {
          q.rollOverLabel(r, b);
        })
          .mouseout(function (b) {
            q.rollOutLabel(r, b);
          })
          .click(function (b) {
            q.clickLabel(r, b);
          });
      }
    },
    createV: function () {
      var b = this.markerSize;
      return a.polygon(this.container, [b / 5, b / 2, b - b / 5, b / 2], [b / 3, b - b / 5, b / 5, b / 1.7], this.switchColor);
    },
    createX: function () {
      var f = (this.markerSize - 4) / 2,
        d = { stroke: this.switchColor, 'stroke-width': 3 },
        h = this.container,
        g = a.line(h, [-f, f], [-f, f]).attr(d),
        f = a.line(h, [-f, f], [f, -f]).attr(d);
      return this.container.set([g, f]);
    },
    createMarker: function (v, u, t, s, r, q, p, o, j) {
      var i = this.markerSize,
        d = this.container;
      r || (r = this.markerBorderColor);
      r || (r = u);
      isNaN(s) && (s = this.markerBorderThickness);
      isNaN(q) && (q = this.markerBorderAlpha);
      return a.bullet(d, v, i, u, t, s, r, q, i, p, o, this.chart.path, j);
    },
    validateNow: function () {
      this.invalidateSize();
    },
    updateValues: function () {
      var v = this.valueLabels,
        u = this.chart,
        t,
        s = this.data;
      if (v) {
        for (t = 0; t < v.length; t++) {
          var r = v[t],
            q = r.dItem;
          q.periodDataItem = void 0;
          q.periodPercentDataItem = void 0;
          var p = ' ';
          if (s) {
            q.value ? r.text(q.value) : r.text('');
          } else {
            var o = null;
            if (void 0 !== q.type) {
              var o = q.currentDataItem,
                j = this.periodValueText;
              q.legendPeriodValueText && (j = q.legendPeriodValueText);
              q.legendPeriodValueTextR && (j = q.legendPeriodValueTextR);
              o
                ? ((p = this.valueText),
                  q.legendValueText && (p = q.legendValueText),
                  q.legendValueTextR && (p = q.legendValueTextR),
                  (p = u.formatString(p, o)))
                : j && u.formatPeriodString && ((j = a.massReplace(j, { '[[title]]': q.title })), (p = u.formatPeriodString(j, q)));
            } else {
              p = u.formatString(this.valueText, q);
            }
            j = q;
            o && (j = o);
            var i = this.valueFunction;
            i && (p = i(j, p, u.periodDataItem));
            var d;
            this.useMarkerColorForLabels && !o && q.lastDataItem && (o = q.lastDataItem);
            o ? (d = u.getBalloonColor(q, o)) : q.legendKeyColor && (d = q.legendKeyColor());
            q.legendColorFunction && (d = q.legendColorFunction(j, p, q.periodDataItem, q.periodPercentDataItem));
            r.text(p);
            if (!q.pattern && (this.useMarkerColorForValues && r.setAttr('fill', d), this.useMarkerColorForLabels)) {
              if ((r = q.legendMarker)) {
                r.setAttr('fill', d), r.setAttr('stroke', d);
              }
              (q = q.legendLabel) && q.setAttr('fill', d);
            }
          }
        }
      }
    },
    renderFix: function () {
      if (!a.VML && this.enabled) {
        var b = this.container;
        b && b.renderFix();
      }
    },
    destroy: function () {
      this.div.innerHTML = '';
      a.remove(this.set);
    }
  });
})();
(function () {
  var a = window.AmCharts;
  a.AmMap = a.Class({
    inherits: a.AmChart,
    construct: function (b) {
      this.cname = 'AmMap';
      this.type = 'map';
      this.theme = b;
      this.svgNotSupported = "This browser doesn't support SVG. Use Chrome, Firefox, Internet Explorer 9 or later.";
      this.createEvents(
        'rollOverMapObject',
        'rollOutMapObject',
        'clickMapObject',
        'mouseDownMapObject',
        'selectedObjectChanged',
        'homeButtonClicked',
        'zoomCompleted',
        'dragCompleted',
        'positionChanged',
        'writeDevInfo',
        'click',
        'descriptionClosed'
      );
      this.zoomDuration = 0.6;
      this.zoomControl = new a.ZoomControl(b);
      this.fitMapToContainer = !0;
      this.mouseWheelZoomEnabled = this.backgroundZoomsToTop = !1;
      this.allowClickOnSelectedObject = this.useHandCursorOnClickableOjects = this.showBalloonOnSelectedObject = !0;
      this.showObjectsAfterZoom = this.wheelBusy = !1;
      this.zoomOnDoubleClick = this.useObjectColorForBalloon = !0;
      this.allowMultipleDescriptionWindows = !1;
      this.dragMap = this.centerMap = this.linesAboveImages = !0;
      this.colorSteps = 5;
      this.forceNormalize = !1;
      this.showAreasInList = !0;
      this.showLinesInList = this.showImagesInList = !1;
      this.areasProcessor = new a.AreasProcessor(this);
      this.areasSettings = new a.AreasSettings(b);
      this.imagesProcessor = new a.ImagesProcessor(this);
      this.imagesSettings = new a.ImagesSettings(b);
      this.linesProcessor = new a.LinesProcessor(this);
      this.linesSettings = new a.LinesSettings(b);
      this.initialTouchZoom = 1;
      this.showDescriptionOnHover = !1;
      a.AmMap.base.construct.call(this, b);
      this.creditsPosition = 'bottom-left';
      this.product = 'ammap';
      this.areasClasses = {};
      this.updatableImages = [];
      a.applyTheme(this, b, this.cname);
    },
    initChart: function () {
      this.zoomInstantly = !0;
      var d = this.container;
      this.panRequired = !0;
      if (this.sizeChanged && a.hasSVG && this.chartCreated) {
        this.updatableImages = [];
        this.freeLabelsSet && this.freeLabelsSet.remove();
        this.freeLabelsSet = d.set();
        this.container.setSize(this.realWidth, this.realHeight);
        this.resizeMap();
        this.drawBackground();
        this.redrawLabels();
        this.drawTitles();
        this.processObjects(!0);
        this.rescaleObjects();
        this.zoomControl.init(this, d);
        this.drawBg();
        var c = this.smallMap;
        c && c.init(this, d);
        (c = this.valueLegend) && c.init(this, d);
        this.sizeChanged = !1;
        this.zoomToLongLat(this.zLevelTemp, this.zLongTemp, this.zLatTemp, !0);
        this.previousWidth = this.realWidth;
        this.previousHeight = this.realHeight;
        this.updateSmallMap();
        this.linkSet.toFront();
        this.zoomControl.update && this.zoomControl.update();
      } else {
        (a.AmMap.base.initChart.call(this), a.hasSVG)
          ? (this.dataChanged &&
              (this.parseData(), (this.dispatchDataUpdated = !0), (this.dataChanged = !1), (d = this.legend)) &&
              ((d.position = 'absolute'), d.invalidateSize()),
            this.createDescriptionsDiv(),
            (this.svgAreas = []),
            (this.svgAreasById = {}),
            this.drawChart())
          : ((this.chartDiv.style.textAlign = ''),
            this.chartDiv.setAttribute('class', 'ammapAlert'),
            (this.chartDiv.innerHTML = this.svgNotSupported),
            this.fire({ type: 'failed', chart: this }));
      }
    },
    storeTemp: function () {
      if (a.hasSVG && 0 < this.realWidth && 0 < this.realHeight) {
        var b = this.mapContainer.getBBox();
        0 < b.width &&
          0 < b.height &&
          ((b = this.zoomLongitude()),
          isNaN(b) || (this.zLongTemp = b),
          (b = this.zoomLatitude()),
          isNaN(b) || (this.zLatTemp = b),
          (b = this.zoomLevel()),
          isNaN(b) || (this.zLevelTemp = b));
      }
    },
    invalidateSize: function () {
      this.storeTemp();
      a.AmMap.base.invalidateSize.call(this);
    },
    validateSize: function () {
      this.storeTemp();
      a.AmMap.base.validateSize.call(this);
    },
    handleWheelReal: function (f) {
      if (!this.wheelBusy) {
        this.stopAnimation();
        var d = this.zoomLevel(),
          h = this.zoomControl,
          g = h.zoomFactor;
        this.wheelBusy = !0;
        f = a.fitToBounds(0 < f ? d * g : d / g, h.minZoomLevel, h.maxZoomLevel);
        g = this.mouseX / this.mapWidth;
        h = this.mouseY / this.mapHeight;
        g = (this.zoomX() - g) * (f / d) + g;
        d = (this.zoomY() - h) * (f / d) + h;
        this.zoomTo(f, g, d);
      }
    },
    addLegend: function (d, c) {
      d.position = 'absolute';
      d.autoMargins = !1;
      d.valueWidth = 0;
      d.switchable = !1;
      a.AmMap.base.addLegend.call(this, d, c);
      void 0 === d.enabled && (d.enabled = !0);
      return d;
    },
    handleLegendEvent: function () {},
    createDescriptionsDiv: function () {
      if (!this.descriptionsDiv) {
        var d = document.createElement('div'),
          c = d.style;
        c.position = 'absolute';
        c.left = '0px';
        c.top = '0px';
        this.descriptionsDiv = d;
      }
      this.containerDiv.appendChild(this.descriptionsDiv);
    },
    drawChart: function () {
      a.AmMap.base.drawChart.call(this);
      var e = this.dataProvider;
      this.dataProvider = e = a.extend(e, new a.MapData(), !0);
      this.areasSettings = a.processObject(this.areasSettings, a.AreasSettings, this.theme);
      this.imagesSettings = a.processObject(this.imagesSettings, a.ImagesSettings, this.theme);
      this.linesSettings = a.processObject(this.linesSettings, a.LinesSettings, this.theme);
      var d = this.container;
      this.mapContainer && this.mapContainer.remove();
      this.mapContainer = d.set();
      this.graphsSet.push(this.mapContainer);
      var f;
      e.map && (f = a.maps[e.map]);
      e.mapVar && (f = e.mapVar);
      f ? ((this.svgData = f.svg), this.getBounds(), this.buildEverything()) : (e = e.mapURL) && this.loadXml(e);
      this.balloonsSet.toFront();
    },
    drawBg: function () {
      var b = this;
      b.background.click(function () {
        b.handleBackgroundClick();
      });
      b.background.mouseover(function () {
        b.rollOutMapObject(b.previouslyHovered);
      });
    },
    buildEverything: function () {
      if (0 < this.realWidth && 0 < this.realHeight) {
        var e = this.container,
          d = this.dataProvider;
        this.projection || ((this.projection = d.projection), this.projection || (this.projection = 'equirectangular'));
        this.updatableImages = [];
        var f = this.projection;
        f && (this.projectionFunction = a[f]);
        this.projectionFunction || (this.projectionFunction = a.equirectangular);
        this.dpProjectionFunction = a[d.projection];
        this.dpProjectionFunction || (this.dpProjectionFunction = a.equirectangular);
        this.zoomControl = a.processObject(this.zoomControl, a.ZoomControl, this.theme);
        this.zoomControl.init(this, e);
        this.drawBg();
        this.buildSVGMap();
        (this.projectionFunction && f != d.projection) || this.forceNormalize
          ? (this.normalizeMap(), this.changeProjection())
          : this.fixMapPosition();
        if ((f = this.smallMap)) {
          (f = a.processObject(f, a.SmallMap, this.theme)), f.init(this, e), (this.smallMap = f);
        }
        isNaN(d.zoomX) &&
          isNaN(d.zoomY) &&
          isNaN(d.zoomLatitude) &&
          isNaN(d.zoomLongitude) &&
          (this.centerMap
            ? ((f = this.xyToCoordinates(this.mapWidth / 2, this.mapHeight / 2)),
              (d.zoomLongitudeC = f.longitude),
              (d.zoomLatitudeC = f.latitude))
            : ((d.zoomX = 0), (d.zoomY = 0)),
          (this.zoomInstantly = !0));
        this.selectObject(this.dataProvider);
        this.processAreas();
        if ((d = this.valueLegend)) {
          (this.valueLegend = d = a.processObject(d, a.ValueLegend, this.theme)), d.init(this, e);
        }
        this.objectList && (e = this.objectList = a.processObject(this.objectList, a.ObjectList)) && (this.clearObjectList(), e.init(this));
        this.dispDUpd();
        this.updateSmallMap();
        this.linkSet.toFront();
      } else {
        this.cleanChart();
      }
    },
    hideGroup: function (b) {
      this.showHideGroup(b, !1);
    },
    showGroup: function (b) {
      this.showHideGroup(b, !0);
    },
    showHideGroup: function (d, c) {
      this.showHideReal(this.imagesProcessor.allObjects, d, c);
      this.showHideReal(this.areasProcessor.allObjects, d, c);
      this.showHideReal(this.linesProcessor.allObjects, d, c);
    },
    showHideReal: function (h, f, l) {
      var j;
      for (j = 0; j < h.length; j++) {
        var k = h[j];
        if (k.groupId == f) {
          var i = k.displayObject;
          i && (l ? ((k.hidden = !1), i.show()) : ((k.hidden = !0), i.hide()));
        }
      }
    },
    makeObjectAccessible: function (d) {
      if (d.accessibleLabel) {
        var c = this.formatString(d.accessibleLabel, d);
        d.displayObject && this.makeAccessible(d.displayObject, c, 'menuitem');
      }
    },
    update: function () {
      if (a.hasSVG) {
        a.AmMap.base.update.call(this);
        this.zoomControl && this.zoomControl.update && this.zoomControl.update();
        for (var d = 0, c = this.updatableImages.length; d < c; d++) {
          this.updatableImages[d].update();
        }
      }
    },
    animateMap: function () {
      var b = this;
      b.totalFrames = b.zoomDuration * a.updateRate;
      b.totalFrames += 1;
      b.frame = 0;
      b.tweenPercent = 0;
      b.balloon.hide(0);
      setTimeout(function () {
        b.updateSize.call(b);
      }, 1000 / a.updateRate);
    },
    updateSize: function () {
      var d = this,
        c = d.totalFrames;
      d.preventHover = !0;
      d.frame <= c
        ? (d.frame++,
          (c = a.easeOutSine(0, d.frame, 0, 1, c)),
          1 <= c
            ? ((c = 1), (d.preventHover = !1), (d.wheelBusy = !1))
            : window.requestAnimationFrame
              ? window.requestAnimationFrame(function () {
                  d.updateSize.call(d);
                })
              : setTimeout(function () {
                  d.updateSize.call(d);
                }, 1000 / a.updateRate),
          0.8 < c && (d.preventHover = !1))
        : ((c = 1), (d.preventHover = !1), (d.wheelBusy = !1));
      d.tweenPercent = c;
      d.rescaleMapAndObjects();
    },
    rescaleMapAndObjects: function () {
      var h = this.initialScale,
        f = this.initialX,
        l = this.initialY,
        j = this.tweenPercent,
        h = h + (this.finalScale - h) * j;
      this.mapContainer.translate(f + (this.finalX - f) * j, l + (this.finalY - l) * j, h, !0);
      if (this.areasSettings.adjustOutlineThickness) {
        for (var f = this.svgAreas, k = 0; k < f.length; k++) {
          (l = f[k]) && l.setAttr('stroke-width', this.areasSettings.outlineThickness / h / this.mapScale);
        }
        if ((f = this.dataProvider.areas)) {
          for (k = 0; k < f.length; k++) {
            var l = f[k],
              i = l.displayObject;
            i && i.setAttr('stroke-width', l.outlineThicknessReal / h / this.mapScale);
          }
        }
      }
      this.rescaleObjects();
      this.positionChanged();
      this.updateSmallMap();
      1 == j && this.fire({ type: 'zoomCompleted', chart: this });
    },
    updateSmallMap: function () {
      this.smallMap && this.smallMap.update();
    },
    rescaleObjects: function () {
      var h = this.mapContainer.scale,
        e = this.imagesProcessor.objectsToResize,
        l;
      for (l = 0; l < e.length; l++) {
        var k = e[l].image,
          j = e[l].scale,
          i = e[l].mapImage;
        isNaN(i.selectedScaleReal) || i != this.selectedObject || ((i.tempScale = j), (j *= i.selectedScaleReal));
        k.translate(k.x, k.y, j / h, !0);
      }
      e = this.imagesProcessor.labelsToReposition;
      for (l = 0; l < e.length; l++) {
        (k = e[l]), k.imageLabel && this.imagesProcessor.positionLabel(k.imageLabel, k, k.labelPositionReal);
      }
      e = this.linesProcessor;
      if ((k = e.linesToResize)) {
        for (l = 0; l < k.length; l++) {
          (j = k[l]), j.line.setAttr('stroke-width', j.thickness / h);
        }
      }
      e = e.objectsToResize;
      for (l = 0; l < e.length; l++) {
        (k = e[l]), k.translate(k.x, k.y, 1 / h, !0);
      }
    },
    handleTouchEnd: function (b) {
      this.initialDistance = NaN;
      this.mouseIsDown = this.isDragging = !1;
      a.AmMap.base.handleTouchEnd.call(this, b);
    },
    handleMouseDown: function (f) {
      a.resetMouseOver();
      this.mouseIsDown = this.mouseIsOver = !0;
      this.balloon.hide(0);
      f && this.mouseIsOver && f.preventDefault && this.panEventsEnabled && f.preventDefault();
      if (
        this.chartCreated &&
        !this.preventHover &&
        ((this.initialTouchZoom = this.zoomLevel()),
        this.dragMap &&
          (this.stopAnimation(), (this.mapContainerClickX = this.mapContainer.x), (this.mapContainerClickY = this.mapContainer.y)),
        f || (f = window.event),
        f.shiftKey && !0 === this.developerMode && this.getDevInfo(),
        f && f.touches)
      ) {
        var d = this.mouseX,
          h = this.mouseY,
          g = f.touches.item(1);
        g &&
          this.panEventsEnabled &&
          this.boundingRect &&
          ((f = g.clientX - this.boundingRect.left),
          (g = g.clientY - this.boundingRect.top),
          (this.middleXP = (d + (f - d) / 2) / this.realWidth),
          (this.middleYP = (h + (g - h) / 2) / this.realHeight),
          (this.initialDistance = Math.sqrt(Math.pow(f - d, 2) + Math.pow(g - h, 2))));
      }
    },
    stopDrag: function () {
      this.isDragging = !1;
    },
    handleReleaseOutside: function () {
      if (a.isModern) {
        var f = this;
        a.AmMap.base.handleReleaseOutside.call(f);
        f.mouseIsDown = !1;
        setTimeout(function () {
          f.resetPinch.call(f);
        }, 100);
        if (!f.preventHover) {
          f.stopDrag();
          var d = f.zoomControl;
          d && d.draggerUp && d.draggerUp();
          f.mapWasDragged = !1;
          var d = f.mapContainer,
            h = f.mapContainerClickX,
            g = f.mapContainerClickY;
          isNaN(h) ||
            isNaN(g) ||
            !(3 < Math.abs(d.x - h) || 3 < Math.abs(d.y - g)) ||
            ((f.mapWasDragged = !0),
            (d = {
              type: 'dragCompleted',
              zoomX: f.zoomX(),
              zoomY: f.zoomY(),
              zoomLevel: f.zoomLevel(),
              chart: f
            }),
            f.fire(d));
          ((f.mouseIsOver && !f.mapWasDragged && !f.skipClick) ||
            (f.wasTouched && 3 > Math.abs(f.mouseX - f.tmx) && 3 > Math.abs(f.mouseY - f.tmy))) &&
            f.fire({ type: 'click', x: f.mouseX, y: f.mouseY, chart: f });
          f.mapContainerClickX = NaN;
          f.mapContainerClickY = NaN;
          f.objectWasClicked = !1;
          f.zoomOnDoubleClick &&
            f.mouseIsOver &&
            ((d = new Date().getTime()),
            200 > d - f.previousClickTime && 40 < d - f.previousClickTime && f.doDoubleClickZoom(),
            (f.previousClickTime = d));
        }
        f.wasTouched = !1;
      }
    },
    resetPinch: function () {
      this.mapWasPinched = !1;
    },
    handleMouseMove: function (E) {
      var D = this;
      a.AmMap.base.handleMouseMove.call(D, E);
      if (!E || !E.touches || !D.tapToActivate || D.tapped) {
        D.panEventsEnabled && D.mouseIsOver && E && E.preventDefault && E.preventDefault();
        var B = D.previuosMouseX,
          A = D.previuosMouseY,
          z = D.mouseX,
          x = D.mouseY,
          w = D.zoomControl;
        isNaN(B) && (B = z);
        isNaN(A) && (A = x);
        D.mouse2X = NaN;
        D.mouse2Y = NaN;
        E &&
          E.touches &&
          (E = E.touches.item(1)) &&
          D.panEventsEnabled &&
          D.boundingRect &&
          ((D.mouse2X = E.clientX - D.boundingRect.left), (D.mouse2Y = E.clientY - D.boundingRect.top));
        if ((E = D.mapContainer)) {
          var v = D.mouse2X,
            u = D.mouse2Y;
          D.pinchTO && clearTimeout(D.pinchTO);
          D.pinchTO = setTimeout(function () {
            D.resetPinch.call(D);
          }, 1000);
          var s = D.realHeight,
            o = D.realWidth,
            j = D.mapWidth,
            F = D.mapHeight;
          D.mouseIsDown &&
            D.dragMap &&
            (3 < Math.abs(D.previuosMouseX - D.mouseX) || 3 < Math.abs(D.previuosMouseY - D.mouseY)) &&
            (D.isDragging = !0);
          if (!isNaN(v)) {
            D.stopDrag();
            var d = Math.sqrt(Math.pow(v - z, 2) + Math.pow(u - x, 2)),
              i = D.initialDistance;
            isNaN(i) && (i = Math.sqrt(Math.pow(v - z, 2) + Math.pow(u - x, 2)));
            if (!isNaN(i)) {
              var v = (D.initialTouchZoom * d) / i,
                v = a.fitToBounds(v, w.minZoomLevel, w.maxZoomLevel),
                w = D.zoomLevel(),
                i = D.middleXP,
                u = D.middleYP,
                d = s / F,
                C = o / j,
                i = (D.zoomX() - i * C) * (v / w) + i * C,
                u = (D.zoomY() - u * d) * (v / w) + u * d;
              0.1 < Math.abs(v - w) && (D.zoomTo(v, i, u, !0), (D.mapWasPinched = !0), clearTimeout(D.pinchTO));
            }
          }
          v = E.scale;
          D.isDragging &&
            (D.balloon.hide(0),
            D.positionChanged(),
            (B = E.x + (z - B)),
            (A = E.y + (x - A)),
            D.preventDragOut &&
              ((F = -F * v + s / 2 - D.diffY * D.mapScale * v),
              (s = s / 2 - D.diffY * D.mapScale * v),
              (B = a.fitToBounds(B, -j * v + o / 2, o / 2)),
              (A = a.fitToBounds(A, F, s))),
            isNaN(B) || isNaN(A) || (E.translate(B, A, v, !0), D.updateSmallMap()));
          D.previuosMouseX = z;
          D.previuosMouseY = x;
        }
      }
    },
    selectObject: function (x, w) {
      var v = this;
      x || (x = v.dataProvider);
      x.isOver = !1;
      var u = x.linkToObject;
      a.isString(u) && (u = v.getObjectById(u));
      x.useTargetsZoomValues &&
        u &&
        ((x.zoomX = u.zoomX),
        (x.zoomY = u.zoomY),
        (x.zoomLatitude = u.zoomLatitude),
        (x.zoomLongitude = u.zoomLongitude),
        (x.zoomLevel = u.zoomLevel));
      var t = v.selectedObject;
      t && v.returnInitialColor(t);
      v.selectedObject = x;
      var s = !1,
        r,
        q;
      'MapArea' == x.objectType &&
        (x.autoZoomReal && (s = !0), (r = v.areasSettings.selectedOutlineColor), (q = v.areasSettings.selectedOutlineThickness));
      if (u && !s && (a.isString(u) && (u = v.getObjectById(u)), isNaN(x.zoomLevel) && isNaN(x.zoomX) && isNaN(x.zoomY))) {
        if (v.extendMapData(u)) {
          return;
        }
        v.selectObject(u);
        return;
      }
      v.allowMultipleDescriptionWindows || v.closeAllDescriptions();
      clearTimeout(v.selectedObjectTimeOut);
      clearTimeout(v.processObjectsTimeOut);
      u = v.zoomDuration;
      !s && isNaN(x.zoomLevel) && isNaN(x.zoomX) && isNaN(x.zoomY)
        ? (v.showDescriptionAndGetUrl(), w || v.processObjects())
        : ((v.selectedObjectTimeOut = setTimeout(
              function () {
                v.showDescriptionAndGetUrl.call(v);
              },
              1000 * u + 200
            )),
            v.showObjectsAfterZoom)
          ? w ||
            (v.processObjectsTimeOut = setTimeout(
              function () {
                v.processObjects.call(v);
              },
              1000 * u + 200
            ))
          : w || v.processObjects();
      u = x.displayObject;
      s = x.selectedColorReal;
      if ('MapImage' == x.objectType) {
        r = v.imagesSettings.selectedOutlineColor;
        q = v.imagesSettings.selectedOutlineThickness;
        var u = x.image,
          o = x.selectedScaleReal;
        if (!isNaN(o) && 1 != o) {
          var j = x.scale;
          isNaN(x.tempScale) || (j = x.tempScale);
          isNaN(j) && (j = 1);
          x.tempScale = j;
          var i = x.displayObject;
          i.translate(i.x, i.y, j * o, !0);
        }
      }
      if (u) {
        if (
          (a.removeCN(v, u, 'selected-object'),
          a.setCN(v, u, 'selected-object'),
          x.bringForwardOnHover && x.displayObject.toFront(),
          v.outlinesToFront(),
          !x.preserveOriginalAttributes)
        ) {
          u.setAttr('stroke', x.outlineColorReal);
          void 0 !== s && u.setAttr('fill', s);
          void 0 !== r && u.setAttr('stroke', r);
          void 0 !== q && u.setAttr('stroke-width', q);
          'MapLine' == x.objectType &&
            ((o = x.lineSvg) && o.setAttr('stroke', s), (o = x.arrowSvg)) &&
            (o.setAttr('fill', s), o.setAttr('stroke', s));
          if ((o = x.imageLabel)) {
            (j = x.selectedLabelColorReal), void 0 !== j && o.setAttr('fill', j);
          }
          x.selectable || (u.setAttr('cursor', 'default'), o && o.setAttr('cursor', 'default'));
        }
      } else {
        v.returnInitialColorReal(x);
      }
      if ((u = x.groupId)) {
        for (o = x.groupArray, o || ((o = v.getGroupById(u)), (x.groupArray = o)), j = 0; j < o.length; j++) {
          if (((i = o[j]), (i.isOver = !1), (u = i.displayObject), 'MapImage' == i.objectType && (u = i.image), u)) {
            var d = i.selectedColorReal;
            void 0 !== d && u.setAttr('fill', d);
            void 0 !== r && u.setAttr('stroke', r);
            void 0 !== q && u.setAttr('stroke-width', q);
            'MapLine' == i.objectType &&
              ((u = i.lineSvg) && u.setAttr('stroke', s), (u = i.arrowSvg)) &&
              (u.setAttr('fill', s), u.setAttr('stroke', s));
          }
        }
      }
      v.rescaleObjects();
      v.zoomToSelectedObject();
      t != x && v.fire({ type: 'selectedObjectChanged', chart: v });
    },
    returnInitialColor: function (f, e) {
      this.returnInitialColorReal(f);
      e && (f.isFirst = !1);
      if (this.selectedObject.bringForwardOnHover) {
        var h = this.selectedObject.displayObject;
        h && h.toFront();
      }
      if ((h = f.groupId)) {
        var h = this.getGroupById(h),
          g;
        for (g = 0; g < h.length; g++) {
          this.returnInitialColorReal(h[g]), e && (h[g].isFirst = !1);
        }
      }
      this.outlinesToFront();
    },
    outlinesToFront: function () {
      if (this.outlines) {
        for (var b = 0; b < this.outlines.length; b++) {
          this.outlines[b].toFront();
        }
      }
    },
    closeAllDescriptions: function () {
      this.descriptionsDiv.innerHTML = '';
    },
    fireClosed: function () {
      this.fire({ type: 'descriptionClosed', chart: this });
    },
    returnInitialColorReal: function (r) {
      r.isOver = !1;
      var q = r.displayObject;
      if (q) {
        a.removeCN(this, q, 'selected-object');
        q.toPrevious();
        if ('MapImage' == r.objectType) {
          var p = r.tempScale;
          isNaN(p) || q.translate(q.x, q.y, p, !0);
          r.tempScale = NaN;
          q = r.image;
        }
        p = r.colorReal;
        if ('MapLine' == r.objectType) {
          var o = r.lineSvg;
          o && o.setAttr('stroke', p);
          if ((o = r.arrowSvg)) {
            var n = r.arrowColor;
            void 0 === n && (n = p);
            o.setAttr('fill', n);
            o.setAttr('stroke', n);
          }
        }
        var o = r.alphaReal,
          n = r.outlineAlphaReal,
          m = r.outlineThicknessReal,
          j = r.outlineColorReal;
        if (r.showAsSelected) {
          var p = r.selectedColorReal,
            i,
            d;
          'MapImage' == r.objectType &&
            ((i = this.imagesSettings.selectedOutlineColor), (d = this.imagesSettings.selectedOutlineThickness));
          'MapArea' == r.objectType && ((i = this.areasSettings.selectedOutlineColor), (d = this.areasSettings.selectedOutlineThickness));
          void 0 !== i && (j = i);
          void 0 !== d && (m = d);
        }
        'bubble' == r.type && (p = void 0);
        void 0 !== p && q.setAttr('fill', p);
        if ((i = r.image)) {
          i.setAttr('fill', p),
            i.setAttr('stroke', j),
            i.setAttr('stroke-width', m),
            i.setAttr('fill-opacity', o),
            i.setAttr('stroke-opacity', n);
        }
        'MapArea' == r.objectType &&
          ((p = 1),
          this.areasSettings.adjustOutlineThickness && (p = this.zoomLevel() * this.mapScale),
          q.setAttr('stroke', j),
          q.setAttr('stroke-width', m / p),
          q.setAttr('fill-opacity', o),
          q.setAttr('stroke-opacity', n));
        (p = r.pattern) && q.pattern(p, this.mapScale, this.path);
        (q = r.imageLabel) &&
          !r.labelInactive &&
          (r.showAsSelected && void 0 !== r.selectedLabelColor
            ? q.setAttr('fill', r.selectedLabelColor)
            : q.setAttr('fill', r.labelColorReal));
      }
    },
    zoomToRectangle: function (i, d, p, o) {
      var n = this.realWidth,
        m = this.realHeight,
        l = this.mapSet.scale,
        j = this.zoomControl,
        n = a.fitToBounds(p / n > o / m ? (0.8 * n) / (p * l) : (0.8 * m) / (o * l), j.minZoomLevel, j.maxZoomLevel);
      this.zoomToMapXY(n, (i + p / 2) * l, (d + o / 2) * l);
    },
    zoomToLatLongRectangle: function (r, q, p, o) {
      var n = this.dataProvider,
        m = this.zoomControl,
        j = Math.abs(p - r),
        i = Math.abs(q - o),
        d = Math.abs(n.rightLongitude - n.leftLongitude),
        n = Math.abs(n.topLatitude - n.bottomLatitude),
        m = a.fitToBounds(j / d > i / n ? (0.8 * d) / j : (0.8 * n) / i, m.minZoomLevel, m.maxZoomLevel);
      this.zoomToLongLat(m, r + (p - r) / 2, o + (q - o) / 2);
    },
    getGroupById: function (d) {
      var c = [];
      this.getGroup(this.imagesProcessor.allObjects, d, c);
      this.getGroup(this.linesProcessor.allObjects, d, c);
      this.getGroup(this.areasProcessor.allObjects, d, c);
      return c;
    },
    zoomToGroup: function (t) {
      t = 'object' == typeof t ? t : this.getGroupById(t);
      var s, r, q, p, o;
      for (o = 0; o < t.length; o++) {
        var n = t[o].displayObject;
        if (n) {
          var j = n.getBBox(),
            n = j.y,
            i = j.y + j.height,
            e = j.x,
            j = j.x + j.width;
          if (n < s || isNaN(s)) {
            s = n;
          }
          if (i > p || isNaN(p)) {
            p = i;
          }
          if (e < r || isNaN(r)) {
            r = e;
          }
          if (j > q || isNaN(q)) {
            q = j;
          }
        }
      }
      r += this.diffX;
      q += this.diffX;
      p += this.diffY;
      s += this.diffY;
      this.zoomToRectangle(r, s, q - r, p - s);
    },
    getGroup: function (g, e, j) {
      if (g) {
        var i;
        for (i = 0; i < g.length; i++) {
          var h = g[i];
          h.groupId == e && j.push(h);
        }
      }
    },
    zoomToStageXY: function (h, d, l, k) {
      if (!this.objectWasClicked) {
        var j = this.zoomControl;
        h = a.fitToBounds(h, j.minZoomLevel, j.maxZoomLevel);
        var j = this.zoomLevel(),
          i = this.mapSet.getBBox();
        d = this.xyToCoordinates((d - this.mapContainer.x) / j - i.x * this.mapScale, (l - this.mapContainer.y) / j - i.y * this.mapScale);
        this.zoomToLongLat(h, d.longitude, d.latitude, k);
      }
    },
    zoomToLongLat: function (f, e, h, g) {
      e = this.coordinatesToXY(e, h);
      this.zoomToMapXY(f, e.x, e.y, g);
    },
    zoomToMapXY: function (h, e, l, k) {
      var j = this.mapWidth,
        i = this.mapHeight;
      this.zoomTo(h, -(e / j) * h + this.realWidth / j / 2, -(l / i) * h + this.realHeight / i / 2, k);
    },
    zoomToObject: function (r) {
      if (r) {
        var q = r.zoomLatitude,
          p = r.zoomLongitude;
        isNaN(r.zoomLatitudeC) || (q = r.zoomLatitudeC);
        isNaN(r.zoomLongitudeC) || (p = r.zoomLongitudeC);
        var o = r.zoomLevel,
          n = this.zoomInstantly,
          m = r.zoomX,
          j = r.zoomY,
          i = this.realWidth,
          d = this.realHeight;
        isNaN(o) || (isNaN(q) || isNaN(p) ? this.zoomTo(o, m, j, n) : this.zoomToLongLat(o, p, q, n));
        this.zoomInstantly = !1;
        'MapImage' == r.objectType &&
          isNaN(r.zoomX) &&
          isNaN(r.zoomY) &&
          isNaN(r.zoomLatitude) &&
          isNaN(r.zoomLongitude) &&
          !isNaN(r.latitude) &&
          !isNaN(r.longitude) &&
          this.zoomToLongLat(r.zoomLevel, r.longitude, r.latitude);
        'MapArea' == r.objectType &&
          ((n = r.displayObject.getBBox()),
          (m = this.mapScale),
          (q = (n.x + this.diffX) * m),
          (p = (n.y + this.diffY) * m),
          (o = n.width * m),
          (n = n.height * m),
          (i = r.autoZoomReal && isNaN(r.zoomLevel) ? (o / i > n / d ? (0.8 * i) / o : (0.8 * d) / n) : r.zoomLevel),
          (d = this.zoomControl),
          (i = a.fitToBounds(i, d.minZoomLevel, d.maxZoomLevel)),
          isNaN(r.zoomX) && isNaN(r.zoomY) && isNaN(r.zoomLatitude) && isNaN(r.zoomLongitude) && this.zoomToMapXY(i, q + o / 2, p + n / 2));
        this.zoomControl.update();
      }
    },
    zoomToSelectedObject: function () {
      this.zoomToObject(this.selectedObject);
    },
    zoomTo: function (g, d, j, i) {
      var h = this.zoomControl;
      g = a.fitToBounds(g, h.minZoomLevel, h.maxZoomLevel);
      h = this.zoomLevel();
      isNaN(d) && ((d = this.realWidth / this.mapWidth), (d = (this.zoomX() - 0.5 * d) * (g / h) + 0.5 * d));
      isNaN(j) && ((j = this.realHeight / this.mapHeight), (j = (this.zoomY() - 0.5 * j) * (g / h) + 0.5 * j));
      this.stopAnimation();
      isNaN(g) ||
        ((h = this.mapContainer),
        (this.initialX = h.x),
        (this.initialY = h.y),
        (this.initialScale = h.scale),
        (this.finalX = this.mapWidth * d),
        (this.finalY = this.mapHeight * j),
        (this.finalScale = g),
        this.finalX != this.initialX || this.finalY != this.initialY || this.finalScale != this.initialScale
          ? i
            ? ((this.tweenPercent = 1), this.rescaleMapAndObjects(), (this.wheelBusy = !1))
            : this.animateMap()
          : (this.wheelBusy = !1));
    },
    loadXml: function (d) {
      var c;
      window.XMLHttpRequest && (c = new XMLHttpRequest());
      c.overrideMimeType && c.overrideMimeType('text/xml');
      c.open('GET', d, !1);
      c.send();
      this.parseXMLObject(c.responseXML);
      this.svgData && this.buildEverything();
    },
    stopAnimation: function () {
      this.frame = this.totalFrames;
    },
    processObjects: function (h) {
      var e = this.selectedObject;
      if (0 < e.images.length || 0 < e.areas.length || 0 < e.lines.length || e == this.dataProvider || h) {
        h = this.container;
        var l = this.stageImagesContainer;
        l && l.remove();
        this.stageImagesContainer = l = h.set();
        this.trendLinesSet.push(l);
        var k = this.stageLinesContainer;
        k && k.remove();
        this.stageLinesContainer = k = h.set();
        this.trendLinesSet.push(k);
        var j = this.mapImagesContainer;
        j && j.remove();
        this.mapImagesContainer = j = h.set();
        this.mapContainer.push(j);
        var i = this.mapLinesContainer;
        i && i.remove();
        this.mapLinesContainer = i = h.set();
        this.mapContainer.push(i);
        this.linesAboveImages ? (j.toFront(), l.toFront(), i.toFront(), k.toFront()) : (i.toFront(), k.toFront(), j.toFront(), l.toFront());
        e &&
          (this.imagesProcessor.reset(),
          this.linesProcessor.reset(),
          this.linesAboveImages
            ? (this.imagesProcessor.process(e), this.linesProcessor.process(e))
            : (this.linesProcessor.process(e), this.imagesProcessor.process(e)));
        this.rescaleObjects();
      }
    },
    processAreas: function () {
      this.areasProcessor.process(this.dataProvider);
    },
    buildSVGMap: function () {
      a.remove(this.mapSet);
      var r = this.svgData.g.path,
        q = this.container,
        p = q.set();
      this.svgAreas = [];
      this.svgAreasById = {};
      void 0 === r.length && (r = [r]);
      var o;
      for (o = 0; o < r.length; o++) {
        var n = r[o],
          m = n.d,
          j = n.title;
        n.titleTr && (j = n.titleTr);
        var i = q.path(m);
        i.id = n.id;
        if (this.areasSettings.preserveOriginalAttributes) {
          i.customAttr = {};
          for (var d in n) {
            'd' != d && 'id' != d && 'title' != d && (i.customAttr[d] = n[d]);
          }
        }
        n.outline && (i.outline = !0);
        i.path = m;
        this.svgAreasById[n.id] = { area: i, title: j, className: n['class'] };
        this.svgAreas.push(i);
        p.push(i);
      }
      this.mapSet = p;
      this.mapContainer.push(p);
      this.resizeMap();
    },
    centerAlign: function () {},
    setProjection: function (b) {
      this.projection = b;
      this.chartCreated = !1;
      this.buildEverything();
    },
    addObjectEventListeners: function (e, d) {
      var f = this;
      e.mousedown(function (b) {
        f.mouseDownMapObject(d, b);
      })
        .mouseup(function (b) {
          f.clickMapObject(d, b);
        })
        .mouseover(function (b) {
          f.balloonX = NaN;
          f.rollOverMapObject(d, !0, b);
        })
        .mouseout(function (b) {
          f.balloonX = NaN;
          f.rollOutMapObject(d, b);
        })
        .touchend(function (b) {
          4 > Math.abs(f.mouseX - f.tmx) && 4 > Math.abs(f.mouseY - f.tmy) && (f.tapped = !0);
          (f.tapToActivate && !f.tapped) ||
            f.mapWasDragged ||
            f.mapWasPinched ||
            ((f.balloonX = NaN), f.rollOverMapObject(d, !0, b), f.clickMapObject(d, b));
        })
        .touchstart(function (b) {
          f.tmx = f.mouseX;
          f.tmy = f.mouseY;
          f.mouseDownMapObject(d, b);
        })
        .keyup(function (b) {
          13 == b.keyCode && f.clickMapObject(d, b);
        });
    },
    checkIfSelected: function (e) {
      var d = this.selectedObject;
      if (d == e) {
        return !0;
      }
      if ((d = d.groupId)) {
        var d = this.getGroupById(d),
          f;
        for (f = 0; f < d.length; f++) {
          if (d[f] == e) {
            return !0;
          }
        }
      }
      return !1;
    },
    clearMap: function () {
      this.chartDiv.innerHTML = '';
      this.clearObjectList();
    },
    clearObjectList: function () {
      var b = this.objectList;
      b && b.div && (b.div.innerHTML = '');
    },
    checkIfLast: function (d) {
      if (d) {
        var c = d.parentNode;
        if (c && c.lastChild == d) {
          return !0;
        }
      }
      return !1;
    },
    showAsRolledOver: function (g) {
      var d = g.displayObject;
      if (!g.showAsSelected && d && !g.isOver) {
        d.node.onmouseout = function () {};
        d.node.onmouseover = function () {};
        d.node.onclick = function () {};
        !g.isFirst && g.bringForwardOnHover && (d.toFront(), (g.isFirst = !0));
        var j = g.rollOverColorReal,
          i;
        g.preserveOriginalAttributes && (j = void 0);
        'bubble' == g.type && (j = void 0);
        void 0 == j && (isNaN(g.rollOverBrightnessReal) || (j = a.adjustLuminosity(g.colorReal, g.rollOverBrightnessReal / 100)));
        if (void 0 != j) {
          if ('MapImage' == g.objectType) {
            (i = g.image) && i.setAttr('fill', j);
          } else {
            if ('MapLine' == g.objectType) {
              if (((i = g.lineSvg) && i.setAttr('stroke', j), (i = g.arrowSvg))) {
                i.setAttr('fill', j), i.setAttr('stroke', j);
              }
            } else {
              d.setAttr('fill', j);
            }
          }
        }
        (j = g.imageLabel) && !g.labelInactive && ((i = g.labelRollOverColorReal), void 0 != i && j.setAttr('fill', i));
        j = g.rollOverOutlineColorReal;
        void 0 != j && ('MapImage' == g.objectType ? (i = g.image) && i.setAttr('stroke', j) : d.setAttr('stroke', j));
        'MapImage' == g.objectType
          ? ((j = this.imagesSettings.rollOverOutlineThickness), (i = g.image) && (isNaN(j) || i.setAttr('stroke-width', j)))
          : ((j = this.areasSettings.rollOverOutlineThickness), isNaN(j) || d.setAttr('stroke-width', j));
        if ('MapArea' == g.objectType) {
          j = this.areasSettings;
          i = g.rollOverAlphaReal;
          isNaN(i) || d.setAttr('fill-opacity', i);
          i = j.rollOverOutlineAlpha;
          isNaN(i) || d.setAttr('stroke-opacity', i);
          i = 1;
          this.areasSettings.adjustOutlineThickness && (i = this.zoomLevel() * this.mapScale);
          var h = j.rollOverOutlineThickness;
          isNaN(h) || d.setAttr('stroke-width', h / i);
          (j = j.rollOverPattern) && d.pattern(j, this.mapScale, this.path);
        }
        'MapImage' == g.objectType &&
          ((j = g.rollOverScaleReal),
          isNaN(j) || 1 == j || ((i = d.scale), isNaN(i) && (i = 1), (g.tempScale = i), d.translate(d.x, d.y, i * j, !0)));
        this.useHandCursorOnClickableOjects && this.checkIfClickable(g) && d.setAttr('cursor', 'pointer');
        g.mouseEnabled && this.addObjectEventListeners(d, g);
        g.isOver = !0;
      }
      this.outlinesToFront();
    },
    rollOverMapObject: function (g, e, j) {
      if (this.chartCreated) {
        this.handleMouseMove();
        var i = this.previouslyHovered;
        i && i != g
          ? (!1 === this.checkIfSelected(i) && (this.returnInitialColor(i, !0), (this.previouslyHovered = null)), this.balloon.hide(0))
          : clearTimeout(this.hoverInt);
        if (!this.preventHover) {
          if (!1 === this.checkIfSelected(g)) {
            if ((i = g.groupId)) {
              var i = this.getGroupById(i),
                h;
              for (h = 0; h < i.length; h++) {
                i[h] != g && this.showAsRolledOver(i[h]);
              }
            }
            this.showAsRolledOver(g);
          } else {
            (i = g.displayObject) && (this.allowClickOnSelectedObject ? i.setAttr('cursor', 'pointer') : i.setAttr('cursor', 'default'));
          }
          this.showDescriptionOnHover
            ? this.showDescription(g)
            : (!this.showBalloonOnSelectedObject && this.checkIfSelected(g)) ||
              !1 === e ||
              ((h = this.balloon),
              (this.balloon.fixedPosition = !1),
              (e = g.colorReal),
              (i = ''),
              (void 0 !== e && this.useObjectColorForBalloon) || (e = h.fillColor),
              (h = g.balloonTextReal) && (i = this.formatString(h, g)),
              this.balloonLabelFunction && (i = this.balloonLabelFunction(g, this)),
              i && '' !== i && this.showBalloon(i, e, !1, this.balloonX, this.balloonY));
          this.fire({
            type: 'rollOverMapObject',
            mapObject: g,
            chart: this,
            event: j
          });
          this.previouslyHovered = g;
        }
      }
    },
    longitudeToX: function (b) {
      return (this.longitudeToCoordinate(b) + this.diffX * this.mapScale) * this.zoomLevel() + this.mapContainer.x;
    },
    latitudeToY: function (b) {
      return (this.latitudeToCoordinate(b) + this.diffY * this.mapScale) * this.zoomLevel() + this.mapContainer.y;
    },
    latitudeToStageY: function (b) {
      return this.latitudeToCoordinate(b) * this.zoomLevel() + this.mapContainer.y + this.diffY * this.mapScale;
    },
    longitudeToStageX: function (b) {
      return this.longitudeToCoordinate(b) * this.zoomLevel() + this.mapContainer.x + this.diffX * this.mapScale;
    },
    stageXToLongitude: function (b) {
      b = (b - this.mapContainer.x) / this.zoomLevel();
      return this.coordinateToLongitude(b);
    },
    stageYToLatitude: function (b) {
      b = (b - this.mapContainer.y) / this.zoomLevel();
      return this.coordinateToLatitude(b);
    },
    rollOutMapObject: function (d, c) {
      this.hideBalloon();
      d &&
        this.chartCreated &&
        d.isOver &&
        (this.checkIfSelected(d) || this.returnInitialColor(d),
        this.fire({
          type: 'rollOutMapObject',
          mapObject: d,
          chart: this,
          event: c
        }));
    },
    formatString: function (i, d) {
      var n = this.nf,
        m = this.pf,
        l = d.title;
      d.titleTr && (l = d.titleTr);
      void 0 == l && (l = '');
      var k = d.value,
        k = isNaN(k) ? '' : a.formatNumber(k, n),
        n = d.percents,
        n = isNaN(n) ? '' : a.formatNumber(n, m),
        m = d.description;
      void 0 == m && (m = '');
      var j = d.customData;
      void 0 == j && (j = '');
      return (i = a.massReplace(i, {
        '[[title]]': l,
        '[[value]]': k,
        '[[percent]]': n,
        '[[description]]': m,
        '[[customData]]': j
      }));
    },
    mouseDownMapObject: function (d, c) {
      this.fire({
        type: 'mouseDownMapObject',
        mapObject: d,
        chart: this,
        event: c
      });
    },
    clickMapObject: function (g, e) {
      var j = this;
      e && (e.touches || (isNaN(g.zoomLevel) && isNaN(g.zoomX) && isNaN(g.zoomY)) || j.hideBalloon());
      if (j.chartCreated && !j.preventHover && j.checkTouchDuration(e) && !j.mapWasDragged && j.checkIfClickable(g) && !j.mapWasPinched) {
        j.selectObject(g);
        var i = j.zoomLevel(),
          h = j.mapSet.getBBox(),
          i = j.xyToCoordinates((j.mouseX - j.mapContainer.x) / i - h.x * j.mapScale, (j.mouseY - j.mapContainer.y) / i - h.y * j.mapScale);
        j.clickLatitude = i.latitude;
        j.clickLongitude = i.longitude;
        e &&
          e.touches &&
          setTimeout(function () {
            j.showBalloonAfterZoom.call(j);
          }, 1000 * j.zoomDuration);
        j.fire({ type: 'clickMapObject', mapObject: g, chart: j, event: e });
        j.objectWasClicked = !0;
      }
    },
    showBalloonAfterZoom: function () {
      var e = this.clickLongitude,
        d = this.clickLatitude,
        f = this.selectedObject;
      'MapImage' != f.objectType || isNaN(f.longitude) || ((e = f.longitude), (d = f.latitude));
      e = this.coordinatesToStageXY(e, d);
      this.balloonX = e.x;
      this.balloonY = e.y;
      this.rollOverMapObject(this.selectedObject, !0);
    },
    checkIfClickable: function (d) {
      var c = this.allowClickOnSelectedObject;
      return this.selectedObject == d && c
        ? !0
        : this.selectedObject != d || c
          ? !0 === d.selectable ||
            ('MapArea' == d.objectType && d.autoZoomReal) ||
            d.url ||
            d.linkToObject ||
            0 < d.images.length ||
            0 < d.lines.length ||
            !isNaN(d.zoomLevel) ||
            !isNaN(d.zoomX) ||
            !isNaN(d.zoomY) ||
            d.description
            ? !0
            : !1
          : !1;
    },
    resizeMap: function () {
      var h = this.mapSet;
      if (h) {
        var e = 1,
          l = h.getBBox(),
          k = this.realWidth,
          j = this.realHeight,
          i = l.width,
          l = l.height;
        0 < i &&
          0 < l &&
          (this.fitMapToContainer && (e = i / k > l / j ? k / i : j / l),
          h.translate(0, 0, e, !0),
          (this.mapScale = e),
          (this.mapHeight = l * e),
          (this.mapWidth = i * e));
      }
    },
    zoomIn: function () {
      var b = this.zoomLevel() * this.zoomControl.zoomFactor;
      this.zoomTo(b);
    },
    zoomOut: function () {
      var b = this.zoomLevel() / this.zoomControl.zoomFactor;
      this.zoomTo(b);
    },
    moveLeft: function () {
      var b = this.zoomX() + this.zoomControl.panStepSize;
      this.zoomTo(this.zoomLevel(), b, this.zoomY());
    },
    moveRight: function () {
      var b = this.zoomX() - this.zoomControl.panStepSize;
      this.zoomTo(this.zoomLevel(), b, this.zoomY());
    },
    moveUp: function () {
      var b = this.zoomY() + this.zoomControl.panStepSize;
      this.zoomTo(this.zoomLevel(), this.zoomX(), b);
    },
    moveDown: function () {
      var b = this.zoomY() - this.zoomControl.panStepSize;
      this.zoomTo(this.zoomLevel(), this.zoomX(), b);
    },
    zoomX: function () {
      return this.mapSet ? Math.round((10000 * this.mapContainer.x) / this.mapWidth) / 10000 : NaN;
    },
    zoomY: function () {
      return this.mapSet ? Math.round((10000 * this.mapContainer.y) / this.mapHeight) / 10000 : NaN;
    },
    goHome: function () {
      this.selectObject(this.dataProvider);
      this.fire({ type: 'homeButtonClicked', chart: this });
    },
    zoomLevel: function () {
      return Math.round(100000 * this.mapContainer.scale) / 100000;
    },
    showDescriptionAndGetUrl: function () {
      var e = this.selectedObject;
      if (e) {
        this.showDescription();
        var d = e.url;
        if (d) {
          a.getURL(d, e.urlTarget);
        } else {
          if ((d = e.linkToObject)) {
            if (a.isString(d)) {
              var f = this.getObjectById(d);
              if (f) {
                this.selectObject(f);
                return;
              }
            }
            d &&
              e.passZoomValuesToTarget &&
              ((d.zoomLatitude = this.zoomLatitude()), (d.zoomLongitude = this.zoomLongitude()), (d.zoomLevel = this.zoomLevel()));
            this.extendMapData(d) || this.selectObject(d);
          }
        }
      }
    },
    extendMapData: function (d) {
      var c = d.objectType;
      if ('MapImage' != c && 'MapArea' != c && 'MapLine' != c) {
        return a.extend(d, new a.MapData(), !0), (this.dataProvider = d), (this.zoomInstantly = !0), this.validateData(), !0;
      }
    },
    showDescription: function (r) {
      r || (r = this.selectedObject);
      this.allowMultipleDescriptionWindows || this.closeAllDescriptions();
      if (r.description) {
        var q = r.descriptionWindow;
        q && q.close();
        q = new a.DescriptionWindow();
        r.descriptionWindow = q;
        var p = r.descriptionWindowWidth,
          o = r.descriptionWindowHeight,
          n = r.descriptionWindowLeft,
          m = r.descriptionWindowTop,
          j = r.descriptionWindowRight,
          i = r.descriptionWindowBottom;
        isNaN(j) || (n = this.realWidth - j);
        isNaN(i) || (m = this.realHeight - i);
        var d = r.descriptionWindowX;
        isNaN(d) || (n = d);
        d = r.descriptionWindowY;
        isNaN(d) || (m = d);
        isNaN(n) && ((n = this.mouseX), (n = n > this.realWidth / 2 ? n - p - 20 : n + 20));
        isNaN(m) && (m = this.mouseY);
        q.maxHeight = o;
        d = r.title;
        r.titleTr && (d = r.titleTr);
        q.show(this, this.descriptionsDiv, r.description, d);
        r = q.div.style;
        r.position = 'absolute';
        r.width = p + 'px';
        r.maxHeight = o + 'px';
        isNaN(i) || (m -= q.div.offsetHeight);
        isNaN(j) || (n -= q.div.offsetWidth);
        r.left = n + 'px';
        r.top = m + 'px';
      }
    },
    parseXMLObject: function (d) {
      var c = { root: {} };
      this.parseXMLNode(c, 'root', d);
      this.svgData = c.root.svg;
      this.getBounds();
    },
    getBounds: function () {
      var f = this.dataProvider;
      try {
        var e = this.svgData.defs['amcharts:ammap'];
        f.leftLongitude = Number(e.leftLongitude);
        f.rightLongitude = Number(e.rightLongitude);
        f.topLatitude = Number(e.topLatitude);
        f.bottomLatitude = Number(e.bottomLatitude);
        f.projection = e.projection;
        var h = e.wrappedLongitudes;
        h && (f.rightLongitude += 360);
        f.wrappedLongitudes = h;
      } catch (g) {}
    },
    recalcLongitude: function (b) {
      return this.dataProvider.wrappedLongitudes ? (b < this.dataProvider.leftLongitude ? Number(b) + 360 : b) : b;
    },
    latitudeToCoordinate: function (f) {
      var e,
        h = this.dataProvider;
      if (this.mapSet) {
        e = h.topLatitude;
        var g = h.bottomLatitude;
        'mercator' == h.projection &&
          ((f = this.mercatorLatitudeToCoordinate(f)),
          (e = this.mercatorLatitudeToCoordinate(e)),
          (g = this.mercatorLatitudeToCoordinate(g)));
        e = ((f - e) / (g - e)) * this.mapHeight;
      }
      return e;
    },
    longitudeToCoordinate: function (e) {
      e = this.recalcLongitude(e);
      var d,
        f = this.dataProvider;
      this.mapSet && ((d = f.leftLongitude), (d = ((e - d) / (f.rightLongitude - d)) * this.mapWidth));
      return d;
    },
    mercatorLatitudeToCoordinate: function (b) {
      89.5 < b && (b = 89.5);
      -89.5 > b && (b = -89.5);
      b = a.degreesToRadians(b);
      return a.radiansToDegrees((0.5 * Math.log((1 + Math.sin(b)) / (1 - Math.sin(b)))) / 2);
    },
    zoomLatitude: function () {
      if (this.mapContainer) {
        var d = this.mapSet.getBBox(),
          c = (-this.mapContainer.x + this.previousWidth / 2) / this.zoomLevel() - d.x * this.mapScale,
          d = (-this.mapContainer.y + this.previousHeight / 2) / this.zoomLevel() - d.y * this.mapScale;
        return this.xyToCoordinates(c, d).latitude;
      }
    },
    zoomLongitude: function () {
      if (this.mapContainer) {
        var d = this.mapSet.getBBox(),
          c = (-this.mapContainer.x + this.previousWidth / 2) / this.zoomLevel() - d.x * this.mapScale,
          d = (-this.mapContainer.y + this.previousHeight / 2) / this.zoomLevel() - d.y * this.mapScale;
        return this.xyToCoordinates(c, d).longitude;
      }
    },
    getAreaCenterLatitude: function (e) {
      e = e.displayObject.getBBox();
      var d = this.mapScale,
        f = this.mapSet.getBBox();
      return this.xyToCoordinates((e.x + e.width / 2 + this.diffX) * d - f.x * d, (e.y + e.height / 2 + this.diffY) * d - f.y * d).latitude;
    },
    getAreaCenterLongitude: function (e) {
      e = e.displayObject.getBBox();
      var d = this.mapScale,
        f = this.mapSet.getBBox();
      return this.xyToCoordinates((e.x + e.width / 2 + this.diffX) * d - f.x * d, (e.y + e.height / 2 + this.diffY) * d - f.y * d)
        .longitude;
    },
    milesToPixels: function (d) {
      var c = this.dataProvider;
      return ((this.mapWidth / (c.rightLongitude - c.leftLongitude)) * d) / 69.172;
    },
    kilometersToPixels: function (d) {
      var c = this.dataProvider;
      return ((this.mapWidth / (c.rightLongitude - c.leftLongitude)) * d) / 111.325;
    },
    handleBackgroundClick: function () {
      if (this.backgroundZoomsToTop && !this.mapWasDragged) {
        var g = this.dataProvider;
        if (this.checkIfClickable(g)) {
          this.clickMapObject(g);
        } else {
          var e = g.zoomX,
            j = g.zoomY,
            i = g.zoomLongitude,
            h = g.zoomLatitude,
            g = g.zoomLevel;
          isNaN(e) || isNaN(j) || this.zoomTo(g, e, j);
          isNaN(i) || isNaN(h) || this.zoomToLongLat(g, i, h, !0);
        }
      }
    },
    parseXMLNode: function (x, w, v, u) {
      void 0 === u && (u = '');
      var t, s, r;
      if (v) {
        var q = v.childNodes.length;
        for (t = 0; t < q; t++) {
          s = v.childNodes[t];
          var o = s.nodeName,
            j = s.nodeValue ? this.trim(s.nodeValue) : '',
            i = !1;
          s.attributes && 0 < s.attributes.length && (i = !0);
          if (0 !== s.childNodes.length || '' !== j || !1 !== i) {
            if (3 == s.nodeType || 4 == s.nodeType) {
              if ('' !== j) {
                s = 0;
                for (r in x[w]) {
                  x[w].hasOwnProperty(r) && s++;
                }
                s ? (x[w]['#text'] = j) : (x[w] = j);
              }
            } else {
              if (1 == s.nodeType) {
                var e;
                void 0 !== x[w][o]
                  ? void 0 === x[w][o].length
                    ? ((e = x[w][o]), (x[w][o] = []), x[w][o].push(e), x[w][o].push({}), (e = x[w][o][1]))
                    : 'object' == typeof x[w][o] && (x[w][o].push({}), (e = x[w][o][x[w][o].length - 1]))
                  : ((x[w][o] = {}), (e = x[w][o]));
                if (s.attributes && s.attributes.length) {
                  for (j = 0; j < s.attributes.length; j++) {
                    e[s.attributes[j].name] = s.attributes[j].value;
                  }
                }
                void 0 !== x[w][o].length
                  ? this.parseXMLNode(x[w][o], x[w][o].length - 1, s, u + '  ')
                  : this.parseXMLNode(x[w], o, s, u + '  ');
              }
            }
          }
        }
        s = 0;
        v = '';
        for (r in x[w]) {
          '#text' == r ? (v = x[w][r]) : s++;
        }
        0 === s && void 0 === x[w].length && (x[w] = v);
      }
    },
    doDoubleClickZoom: function () {
      if (!this.mapWasDragged) {
        var b = this.zoomLevel() * this.zoomControl.zoomFactor;
        this.zoomToStageXY(b, this.mouseX, this.mouseY);
      }
    },
    getDevInfo: function () {
      var d = this.zoomLevel(),
        c = this.mapSet.getBBox(),
        c = this.xyToCoordinates(
          (this.mouseX - this.mapContainer.x) / d - c.x * this.mapScale,
          (this.mouseY - this.mapContainer.y) / d - c.y * this.mapScale
        ),
        d = {
          chart: this,
          type: 'writeDevInfo',
          zoomLevel: d,
          zoomX: this.zoomX(),
          zoomY: this.zoomY(),
          zoomLatitude: this.zoomLatitude(),
          zoomLongitude: this.zoomLongitude(),
          latitude: c.latitude,
          longitude: c.longitude,
          left: this.mouseX,
          top: this.mouseY,
          right: this.realWidth - this.mouseX,
          bottom: this.realHeight - this.mouseY,
          percentLeft: Math.round((this.mouseX / this.realWidth) * 100) + '%',
          percentTop: Math.round((this.mouseY / this.realHeight) * 100) + '%',
          percentRight: Math.round(((this.realWidth - this.mouseX) / this.realWidth) * 100) + '%',
          percentBottom: Math.round(((this.realHeight - this.mouseY) / this.realHeight) * 100) + '%'
        },
        c = 'zoomLevel:' + d.zoomLevel + ', zoomLongitude:' + d.zoomLongitude + ', zoomLatitude:' + d.zoomLatitude + '\n',
        c = c + ('zoomX:' + d.zoomX + ', zoomY:' + d.zoomY + '\n'),
        c = c + ('latitude:' + d.latitude + ', longitude:' + d.longitude + '\n'),
        c = c + ('left:' + d.left + ', top:' + d.top + '\n'),
        c = c + ('right:' + d.right + ', bottom:' + d.bottom + '\n'),
        c = c + ('left:' + d.percentLeft + ', top:' + d.percentTop + '\n'),
        c = c + ('right:' + d.percentRight + ', bottom:' + d.percentBottom + '\n');
      d.str = c;
      this.fire(d);
      return d;
    },
    getXY: function (e, d, f) {
      void 0 !== e &&
        (-1 != String(e).indexOf('%')
          ? ((e = Number(e.split('%').join(''))), f && (e = 100 - e), (e = (Number(e) * d) / 100))
          : f && (e = d - e));
      return e;
    },
    getObjectById: function (e) {
      var d = this.dataProvider;
      if (d.areas) {
        var f = this.getObject(e, d.areas);
        if (f) {
          return f;
        }
      }
      if ((f = this.getObject(e, d.images))) {
        return f;
      }
      if ((e = this.getObject(e, d.lines))) {
        return e;
      }
    },
    getObject: function (g, e) {
      if (e) {
        var j;
        for (j = 0; j < e.length; j++) {
          var i = e[j];
          if (i.id == g) {
            return i;
          }
          if (i.areas) {
            var h = this.getObject(g, i.areas);
            if (h) {
              return h;
            }
          }
          if ((h = this.getObject(g, i.images))) {
            return h;
          }
          if ((i = this.getObject(g, i.lines))) {
            return i;
          }
        }
      }
    },
    parseData: function () {
      var b = this.dataProvider;
      this.processObject(b.areas, b, 'area');
      this.processObject(b.images, b, 'image');
      this.processObject(b.lines, b, 'line');
    },
    processObject: function (g, d, j) {
      if (g) {
        var i;
        for (i = 0; i < g.length; i++) {
          var h = g[i];
          h.parentObject = d;
          'area' == j && a.extend(h, new a.MapArea(this.theme), !0);
          'image' == j && (h = a.extend(h, new a.MapImage(this.theme), !0));
          'line' == j && (h = a.extend(h, new a.MapLine(this.theme), !0));
          g[i] = h;
          h.areas && this.processObject(h.areas, h, 'area');
          h.images && this.processObject(h.images, h, 'image');
          h.lines && this.processObject(h.lines, h, 'line');
        }
      }
    },
    positionChanged: function () {
      var b = {
        type: 'positionChanged',
        zoomX: this.zoomX(),
        zoomY: this.zoomY(),
        zoomLevel: this.zoomLevel(),
        chart: this
      };
      this.fire(b);
    },
    getX: function (d, c) {
      return this.getXY(d, this.realWidth, c);
    },
    getY: function (d, c) {
      return this.getXY(d, this.realHeight, c);
    },
    trim: function (d) {
      if (d) {
        var c;
        for (c = 0; c < d.length; c++) {
          if (
            -1 ===
            ' \n\r\t\f\x0B\u00a0\u2000\u2001\u2002\u2003\u2004\u2005\u2006\u2007\u2008\u2009\u200a\u200b\u2028\u2029\u3000'.indexOf(
              d.charAt(c)
            )
          ) {
            d = d.substring(c);
            break;
          }
        }
        for (c = d.length - 1; 0 <= c; c--) {
          if (
            -1 ===
            ' \n\r\t\f\x0B\u00a0\u2000\u2001\u2002\u2003\u2004\u2005\u2006\u2007\u2008\u2009\u200a\u200b\u2028\u2029\u3000'.indexOf(
              d.charAt(c)
            )
          ) {
            d = d.substring(0, c + 1);
            break;
          }
        }
        return -1 ===
          ' \n\r\t\f\x0B\u00a0\u2000\u2001\u2002\u2003\u2004\u2005\u2006\u2007\u2008\u2009\u200a\u200b\u2028\u2029\u3000'.indexOf(
            d.charAt(0)
          )
          ? d
          : '';
      }
    },
    destroy: function () {
      a.AmMap.base.destroy.call(this);
    },
    x2c: function (d) {
      var c = this.dataProvider.leftLongitude;
      return Math.round(((this.unscaledMapWidth * (d - c)) / (this.dataProvider.rightLongitude - c)) * 100) / 100;
    },
    y2c: function (d) {
      var c = this.dataProvider.topLatitude;
      return Math.round(((this.unscaledMapHeight * (d - c)) / (this.dataProvider.bottomLatitude - c)) * 100) / 100;
    },
    normalize: function (e) {
      if (!e.pathsArray) {
        var d;
        if (e.normalized) {
          d = e.normalized;
        } else {
          var f = a.normalizePath(e.node);
          d = e.node.getAttribute('d');
          e.normalized = d;
          f.maxX > this.maxMapX && (this.maxMapX = f.maxX);
          f.minX < this.minMapX && (this.minMapX = f.minX);
          f.maxY > this.maxMapY && (this.maxMapY = f.maxY);
          f.minY < this.minMapY && (this.minMapY = f.minY);
        }
        e.node.setAttribute('d', d);
      }
    },
    redraw: function (i) {
      var e = i.normalized,
        e = e.split(' Z').join(''),
        e = e.split('M');
      i.pathsArray = [];
      for (var n = 0; n < e.length; n++) {
        var m = e[n];
        if (m) {
          for (var m = m.split('L'), l = [], k = 0; k < m.length; k++) {
            if (m[k]) {
              var j = m[k].split(' '),
                j = this.xyToCoordinates(
                  Number(j[1]) - this.minMapX,
                  Number(j[2]) - this.minMapY,
                  this.dpProjectionFunction,
                  this.sourceMapWidth,
                  this.sourceMapHeight
                );
              l.push([j.longitude, j.latitude]);
            }
          }
          i.pathsArray.push(l);
        }
      }
      e = '';
      for (n = 0; n < i.pathsArray.length; n++) {
        e += this.redrawArea(i.pathsArray[n]);
      }
      i.node.setAttribute('d', e);
      i.path = e;
    },
    redrawArea: function (i) {
      for (var d = !1, p = '', o = 0; o < i.length; o++) {
        var n = i[o][0],
          m = i[o][1],
          l = a.degreesToRadians(i[o][0]),
          j = a.degreesToRadians(i[o][1]),
          j = this.projectionFunction(l, j),
          l = a.roundTo(this.x2c(a.radiansToDegrees(j[0])), 3),
          j = a.roundTo(this.y2c(a.radiansToDegrees(j[1])), 3);
        l < this.minMapXX && ((this.minMapXX = l), (this.leftLongLat = { longitude: n, latitude: m }));
        l > this.maxMapXX && ((this.maxMapXX = l), (this.rightLongLat = { longitude: n, latitude: m }));
        j < this.minMapYY && ((this.minMapYY = j), (this.topLongLat = { longitude: n, latitude: m }));
        j > this.maxMapYY && ((this.maxMapYY = j), (this.bottomLongLat = { longitude: n, latitude: m }));
        d ? (p += ' L ') : ((p += ' M '), (d = !0));
        p += l + ' ' + j;
      }
      return p + ' Z ';
    },
    normalizeMap: function () {
      var t = a.degreesToRadians(this.dataProvider.leftLongitude),
        s = a.degreesToRadians(this.dataProvider.rightLongitude),
        r = a.degreesToRadians(this.dataProvider.topLatitude),
        q = a.degreesToRadians(this.dataProvider.bottomLatitude),
        p = t + (s - t) / 2,
        o = r + (q - r) / 2,
        n = this.dpProjectionFunction(p, r)[1],
        j = this.dpProjectionFunction(p, q)[1],
        i = this.dpProjectionFunction(t, o)[0],
        d = this.dpProjectionFunction(s, o)[0],
        r = a.equirectangular(p, r),
        q = a.equirectangular(p, q),
        n = (r[1] - q[1]) / (n - j),
        t = a.equirectangular(t, o),
        s = a.equirectangular(s, o),
        i = (t[0] - s[0]) / (i - d);
      this.minMapX = Infinity;
      this.maxMapX = -Infinity;
      this.minMapY = Infinity;
      this.maxMapY = -Infinity;
      for (d = 0; d < this.svgAreas.length; d++) {
        this.normalize(this.svgAreas[d]);
      }
      this.sourceMapHeight = Math.abs(this.maxMapY - this.minMapY);
      this.sourceMapWidth = Math.abs(this.maxMapX - this.minMapX);
      this.unscaledMapWidth = this.sourceMapWidth * i;
      this.unscaledMapHeight = this.sourceMapHeight * n;
      this.diffY = this.diffX = 0;
    },
    fixMapPosition: function () {
      var t = a.degreesToRadians(this.dataProvider.leftLongitude),
        s = a.degreesToRadians(this.dataProvider.rightLongitude),
        r = a.degreesToRadians(this.dataProvider.topLatitude),
        q = a.degreesToRadians(this.dataProvider.bottomLatitude),
        p = t + (s - t) / 2,
        o = r + (q - r) / 2,
        n = this.dpProjectionFunction(p, r)[1],
        j = this.dpProjectionFunction(p, q)[1],
        i = this.dpProjectionFunction(t, o)[0],
        d = this.dpProjectionFunction(s, o)[0];
      this.sourceMapHeight = this.mapHeight / this.mapScale;
      this.sourceMapWidth = this.mapWidth / this.mapScale;
      this.unscaledMapWidth = ((t - s) / (i - d)) * this.sourceMapWidth;
      this.unscaledMapHeight = ((r - q) / (n - j)) * this.sourceMapHeight;
      s = this.coordinatesToXY(a.radiansToDegrees(p), a.radiansToDegrees(r));
      t = this.coordinatesToXY(a.radiansToDegrees(t), a.radiansToDegrees(o));
      r = o = Infinity;
      for (q = 0; q < this.svgAreas.length; q++) {
        (p = this.svgAreas[q].getBBox()), p.y < o && (o = p.y), p.x < r && (r = p.x);
      }
      this.diffY = s.y / this.mapScale - o;
      this.diffX = t.x / this.mapScale - r;
      for (q = 0; q < this.svgAreas.length; q++) {
        this.svgAreas[q].translate(this.diffX, this.diffY);
      }
    },
    changeProjection: function () {
      this.minMapXX = Infinity;
      this.maxMapXX = -Infinity;
      this.minMapYY = Infinity;
      this.maxMapYY = -Infinity;
      this.projectionChanged = !1;
      for (var b = 0; b < this.svgAreas.length; b++) {
        this.redraw(this.svgAreas[b]);
      }
      this.projectionChanged = !0;
      this.resizeMap();
    },
    coordinatesToXY: function (f, d) {
      var h, g;
      h = !1;
      this.dataProvider && (h = this.dataProvider.wrappedLongitudes) && (f = this.recalcLongitude(f));
      this.projectionFunction
        ? ((g = this.projectionFunction(a.degreesToRadians(f), a.degreesToRadians(d))),
          (h = this.mapScale * a.roundTo(this.x2c(a.radiansToDegrees(g[0])), 3)),
          (g = this.mapScale * a.roundTo(this.y2c(a.radiansToDegrees(g[1])), 3)))
        : ((h = this.longitudeToCoordinate(f)), (g = this.latitudeToCoordinate(d)));
      return { x: h, y: g };
    },
    coordinatesToStageXY: function (f, e) {
      var h = this.coordinatesToXY(f, e),
        g = h.x * this.zoomLevel() + this.mapContainer.x,
        h = h.y * this.zoomLevel() + this.mapContainer.y;
      return { x: g, y: h };
    },
    stageXYToCoordinates: function (f, e) {
      var h = this.mapSet.getBBox(),
        g = (f - this.mapContainer.x) / this.zoomLevel() - h.x * this.mapScale,
        h = (e - this.mapContainer.y) / this.zoomLevel() - h.y * this.mapScale;
      return this.xyToCoordinates(g, h);
    },
    xyToCoordinates: function (x, w, v, u, t) {
      var s;
      isNaN(u) && (u = this.mapWidth);
      isNaN(t) && (t = this.mapHeight);
      v || (v = this.projectionFunction);
      if ((s = v.invert)) {
        var r = this.dataProvider.leftLongitude,
          q = this.dataProvider.rightLongitude,
          o = this.dataProvider.topLatitude,
          j = this.dataProvider.bottomLatitude,
          i = r + (q - r) / 2,
          d = o + (j - o) / 2,
          o = a.radiansToDegrees(v(a.degreesToRadians(i), a.degreesToRadians(o))[1]),
          j = a.radiansToDegrees(v(a.degreesToRadians(i), a.degreesToRadians(j))[1]),
          r = a.radiansToDegrees(v(a.degreesToRadians(r), a.degreesToRadians(d))[0]),
          q = a.radiansToDegrees(v(a.degreesToRadians(q), a.degreesToRadians(d))[0]);
        this.projectionChanged &&
          ((o = a.radiansToDegrees(v(a.degreesToRadians(this.topLongLat.longitude), a.degreesToRadians(this.topLongLat.latitude))[1])),
          (j = a.radiansToDegrees(v(a.degreesToRadians(this.bottomLongLat.longitude), a.degreesToRadians(this.bottomLongLat.latitude))[1])),
          (r = a.radiansToDegrees(v(a.degreesToRadians(this.leftLongLat.longitude), a.degreesToRadians(this.leftLongLat.latitude))[0])),
          (q = a.radiansToDegrees(v(a.degreesToRadians(this.rightLongLat.longitude), a.degreesToRadians(this.rightLongLat.latitude))[0])));
        x = a.degreesToRadians((x / u) * (q - r) + r);
        w = a.degreesToRadians((w / t) * (j - o) + o);
        w = s(x, w);
        s = a.radiansToDegrees(w[0]);
        w = a.radiansToDegrees(w[1]);
      } else {
        (s = this.coordinateToLongitude(x)), (w = this.coordinateToLatitude(w));
      }
      return { longitude: a.roundTo(s, 4), latitude: a.roundTo(w, 4) };
    },
    coordinateToLatitude: function (g, d) {
      var j;
      void 0 === d && (d = this.mapHeight);
      if (this.mapSet) {
        var i = this.dataProvider,
          h = i.bottomLatitude;
        j = i.topLatitude;
        'mercator' == i.projection
          ? ((i = this.mercatorLatitudeToCoordinate(h)),
            (j = this.mercatorLatitudeToCoordinate(j)),
            (j = 2 * a.degreesToRadians((g * (i - j)) / d + j)),
            (j = a.radiansToDegrees(2 * Math.atan(Math.exp(j)) - 0.5 * Math.PI)))
          : (j = (g / d) * (h - j) + j);
      }
      return Math.round(1000000 * j) / 1000000;
    },
    coordinateToLongitude: function (f, e) {
      var h,
        g = this.dataProvider;
      void 0 === e && (e = this.mapWidth);
      this.mapSet && (h = (f / e) * (g.rightLongitude - g.leftLongitude) + g.leftLongitude);
      return Math.round(1000000 * h) / 1000000;
    }
  });
})();
(function () {
  var a = window.AmCharts;
  a.ZoomControl = a.Class({
    construct: function (b) {
      this.cname = 'ZoomControl';
      this.panStepSize = 0.1;
      this.zoomFactor = 2;
      this.maxZoomLevel = 64;
      this.minZoomLevel = 1;
      this.panControlEnabled = !1;
      this.zoomControlEnabled = !0;
      this.buttonRollOverColor = '#DADADA';
      this.buttonFillColor = '#FFFFFF';
      this.buttonFillAlpha = 1;
      this.buttonBorderColor = '#000000';
      this.buttonBorderAlpha = 0.1;
      this.buttonIconAlpha = this.buttonBorderThickness = 1;
      this.gridColor = this.buttonIconColor = '#000000';
      this.homeIconFile = 'homeIcon.gif';
      this.gridBackgroundColor = '#000000';
      this.draggerAlpha = this.gridAlpha = this.gridBackgroundAlpha = 0;
      this.draggerSize = this.buttonSize = 31;
      this.iconSize = 11;
      this.homeButtonEnabled = !0;
      this.buttonCornerRadius = 2;
      this.gridHeight = 5;
      this.roundButtons = !0;
      this.top = this.left = 10;
      a.applyTheme(this, b, this.cname);
    },
    init: function (af, ae) {
      var ad = this;
      ad.chart = af;
      a.remove(ad.set);
      var ac = ae.set();
      a.setCN(af, ac, 'zoom-control');
      var ab = ad.buttonSize,
        aa = ad.zoomControlEnabled,
        Z = ad.panControlEnabled,
        Y = ad.buttonFillColor,
        X = ad.buttonFillAlpha,
        W = ad.buttonBorderThickness,
        V = ad.buttonBorderColor,
        U = ad.buttonBorderAlpha,
        N = ad.buttonCornerRadius,
        Q = ad.buttonRollOverColor,
        S = ad.gridHeight,
        i = ad.zoomFactor,
        R = ad.minZoomLevel,
        L = ad.maxZoomLevel,
        s = ad.buttonIconAlpha,
        J = ad.buttonIconColor,
        T = ad.roundButtons,
        P = af.svgIcons,
        j = af.getX(ad.left),
        d = af.getY(ad.top);
      isNaN(ad.right) || ((j = af.getX(ad.right, !0)), (j = Z ? j - 3 * ab : j - ab));
      isNaN(ad.bottom) ||
        ((d = af.getY(ad.bottom, !0)), aa && (d -= S + 3 * ab), (d = Z ? d - 3 * ab : ad.homeButtonEnabled ? d - 0.5 * ab : d + ab));
      ac.translate(j, d);
      ad.previousDY = NaN;
      var K,
        j = ab / 4 - 1;
      if (aa) {
        K = ae.set();
        a.setCN(af, K, 'zoom-control-zoom');
        ac.push(K);
        ad.set = ac;
        ad.zoomSet = K;
        5 < S &&
          ((aa = a.rect(ae, ab + 6, S + 2 * ab + 6, ad.gridBackgroundColor, ad.gridBackgroundAlpha, 0, '#000000', 0, 4)),
          a.setCN(af, aa, 'zoom-bg'),
          aa.translate(-3, -3),
          aa
            .mouseup(function () {
              ad.handleBgUp();
            })
            .touchend(function () {
              ad.handleBgUp();
            }),
          K.push(aa));
        var M = ab;
        T && (M = ab / 1.5);
        ad.draggerSize = M;
        var o = Math.log(L / R) / Math.log(i) + 1;
        1000 < o && (o = 1000);
        var aa = S / o,
          I,
          O = ae.set();
        O.translate((ab - M) / 2 + 1, 1, NaN, !0);
        K.push(O);
        for (I = 1; I < o; I++) {
          (d = ab + I * aa), (d = a.line(ae, [1, M - 2], [d, d], ad.gridColor, ad.gridAlpha, 1)), a.setCN(af, d, 'zoom-grid'), O.push(d);
        }
        d = new a.SimpleButton();
        d.setDownHandler(ad.draggerDown, ad);
        d.setClickHandler(ad.draggerUp, ad);
        d.init(ae, M, aa, Y, X, W, V, U, N, Q);
        a.setCN(af, d.set, 'zoom-dragger');
        K.push(d.set);
        d.set.setAttr('opacity', ad.draggerAlpha);
        ad.dragger = d.set;
        ad.previousY = NaN;
        d = new a.SimpleButton();
        P
          ? ((M = ae.set()),
            (o = a.line(ae, [-j, j], [0, 0], J, s, 1)),
            (I = a.line(ae, [0, 0], [-j, j], J, s, 1)),
            M.push(o),
            M.push(I),
            (d.svgIcon = M))
          : d.setIcon(af.pathToImages + 'plus.gif', ad.iconSize);
        d.setClickHandler(af.zoomIn, af);
        d.init(ae, ab, ab, Y, X, W, V, U, N, Q, s, J, T);
        a.setCN(af, d.set, 'zoom-in');
        K.push(d.set);
        d = new a.SimpleButton();
        P ? (d.svgIcon = a.line(ae, [-j, j], [0, 0], J, s, 1)) : d.setIcon(af.pathToImages + 'minus.gif', ad.iconSize);
        d.setClickHandler(af.zoomOut, af);
        d.init(ae, ab, ab, Y, X, W, V, U, N, Q, s, J, T);
        d.set.translate(0, S + ab);
        a.setCN(af, d.set, 'zoom-out');
        K.push(d.set);
        S -= aa;
        L = Math.log(L / 100) / Math.log(i);
        ad.realStepSize = S / (L - Math.log(R / 100) / Math.log(i));
        ad.realGridHeight = S;
        ad.stepMax = L;
      }
      Z &&
        ((Z = ae.set()),
        a.setCN(af, Z, 'zoom-control-pan'),
        ac.push(Z),
        K && K.translate(ab, 4 * ab),
        (i = new a.SimpleButton()),
        P
          ? (i.svgIcon = a.line(ae, [j / 5, -j + j / 5, j / 5], [-j, 0, j], J, s, 1))
          : i.setIcon(af.pathToImages + 'panLeft.gif', ad.iconSize),
        i.setClickHandler(af.moveLeft, af),
        i.init(ae, ab, ab, Y, X, W, V, U, N, Q, s, J, T),
        i.set.translate(0, ab),
        a.setCN(af, i.set, 'pan-left'),
        Z.push(i.set),
        (i = new a.SimpleButton()),
        P
          ? (i.svgIcon = a.line(ae, [-j / 5, j - j / 5, -j / 5], [-j, 0, j], J, s, 1))
          : i.setIcon(af.pathToImages + 'panRight.gif', ad.iconSize),
        i.setClickHandler(af.moveRight, af),
        i.init(ae, ab, ab, Y, X, W, V, U, N, Q, s, J, T),
        i.set.translate(2 * ab, ab),
        a.setCN(af, i.set, 'pan-right'),
        Z.push(i.set),
        (i = new a.SimpleButton()),
        P
          ? (i.svgIcon = a.line(ae, [-j, 0, j], [j / 5, -j + j / 5, j / 5], J, s, 1))
          : i.setIcon(af.pathToImages + 'panUp.gif', ad.iconSize),
        i.setClickHandler(af.moveUp, af),
        i.init(ae, ab, ab, Y, X, W, V, U, N, Q, s, J, T),
        i.set.translate(ab, 0),
        a.setCN(af, i.set, 'pan-up'),
        Z.push(i.set),
        (i = new a.SimpleButton()),
        P
          ? (i.svgIcon = a.line(ae, [-j, 0, j], [-j / 5, j - j / 5, -j / 5], J, s, 1))
          : i.setIcon(af.pathToImages + 'panDown.gif', ad.iconSize),
        i.setClickHandler(af.moveDown, af),
        i.init(ae, ab, ab, Y, X, W, V, U, N, Q, s, J, T),
        i.set.translate(ab, 2 * ab),
        a.setCN(af, i.set, 'pan-down'),
        Z.push(i.set),
        ac.push(Z));
      ad.homeButtonEnabled &&
        ((Z = new a.SimpleButton()),
        P
          ? (Z.svgIcon = a.polygon(
              ae,
              [-j, 0, j, j - 1, j - 1, 2, 2, -2, -2, -j + 1, -j + 1],
              [0, -j, 0, 0, j - 1, j - 1, 2, 2, j - 1, j - 1, 0],
              J,
              s,
              1,
              J,
              s
            ))
          : Z.setIcon(af.pathToImages + ad.homeIconFile, ad.iconSize),
        Z.setClickHandler(af.goHome, af),
        ad.panControlEnabled && (U = X = 0),
        Z.init(ae, ab, ab, Y, X, W, V, U, N, Q, s, J, T),
        ad.panControlEnabled ? Z.set.translate(ab, ab) : K && K.translate(0, 1.5 * ab),
        a.setCN(af, Z.set, 'pan-home'),
        ac.push(Z.set));
      ad.update();
    },
    draggerDown: function () {
      this.chart.stopDrag();
      this.isDragging = !0;
    },
    draggerUp: function () {
      this.isDragging = !1;
    },
    handleBgUp: function () {
      var b = this.chart;
      b.zoomTo(
        100 *
          Math.pow(
            this.zoomFactor,
            this.stepMax - (b.mouseY - this.zoomSet.y - this.set.y - this.buttonSize - this.realStepSize / 2) / this.realStepSize
          )
      );
    },
    update: function () {
      var i;
      i = this.zoomFactor;
      var d = this.realStepSize,
        n = this.stepMax,
        m = this.dragger,
        l = this.buttonSize,
        k,
        j = this.chart;
      j &&
        (this.isDragging
          ? (j.stopDrag(),
            (k = m.y + (j.mouseY - this.previousY)),
            (k = a.fitToBounds(k, l, this.realGridHeight + l)),
            j.zoomTo(100 * Math.pow(i, n - (k - l) / d), NaN, NaN, !0))
          : ((i = Math.log(j.zoomLevel() / 100) / Math.log(i)), (k = (n - i) * d + l)),
        (this.previousY = j.mouseY),
        this.previousDY != k && m && (m.translate((this.buttonSize - this.draggerSize) / 2, k), (this.previousDY = k)));
    }
  });
})();
(function () {
  var a = window.AmCharts;
  a.SimpleButton = a.Class({
    construct: function () {},
    init: function (C, B, A, z, y, x, w, v, u, s, o, j, D) {
      var d = this;
      d.rollOverColor = s;
      d.color = z;
      d.container = C;
      s = C.set();
      d.set = s;
      D ? ((B /= 2), (z = a.circle(C, B, z, y, x, w, v)), z.translate(B, B)) : (z = a.rect(C, B, A, z, y, x, w, v, u));
      s.push(z);
      y = d.iconPath;
      var i;
      y && ((i = d.iconSize), (x = (B - i) / 2), D && (x = (2 * B - i) / 2), (i = C.image(y, x, (A - i) / 2, i, i)));
      d.svgIcon && ((i = d.svgIcon), D ? i.translate(B, B) : i.translate(B / 2, B / 2));
      s.setAttr('cursor', 'pointer');
      i && (s.push(i), i.setAttr('opacity', o), (i.node.style.pointerEvents = 'none'));
      z.mousedown(function () {
        d.handleDown();
      })
        .touchstart(function () {
          d.handleDown();
        })
        .mouseup(function () {
          d.handleUp();
        })
        .touchend(function () {
          d.handleUp();
        })
        .mouseover(function () {
          d.handleOver();
        })
        .mouseout(function () {
          d.handleOut();
        });
      d.bg = z;
    },
    setIcon: function (d, c) {
      this.iconPath = d;
      this.iconSize = c;
    },
    setClickHandler: function (d, c) {
      this.clickHandler = d;
      this.scope = c;
    },
    setDownHandler: function (d, c) {
      this.downHandler = d;
      this.scope = c;
    },
    handleUp: function () {
      var b = this.clickHandler;
      b && b.call(this.scope);
    },
    handleDown: function () {
      var b = this.downHandler;
      b && b.call(this.scope);
    },
    handleOver: function () {
      this.container.chart.skipClick = !0;
      this.bg.setAttr('fill', this.rollOverColor);
    },
    handleOut: function () {
      this.container.chart.skipClick = !1;
      this.bg.setAttr('fill', this.color);
    }
  });
})();
(function () {
  var a = window.AmCharts;
  a.SmallMap = a.Class({
    construct: function (b) {
      this.cname = 'SmallMap';
      this.mapColor = '#e6e6e6';
      this.rectangleColor = '#FFFFFF';
      this.top = this.right = 10;
      this.minimizeButtonWidth = 23;
      this.backgroundColor = '#9A9A9A';
      this.backgroundAlpha = 1;
      this.borderColor = '#FFFFFF';
      this.iconColor = '#000000';
      this.borderThickness = 3;
      this.borderAlpha = 1;
      this.size = 0.2;
      this.enabled = !0;
      a.applyTheme(this, b, this.cname);
    },
    init: function (C, B) {
      var A = this;
      if (A.enabled) {
        A.chart = C;
        A.container = B;
        A.width = C.realWidth * A.size;
        A.height = C.realHeight * A.size;
        a.remove(A.mapSet);
        a.remove(A.allSet);
        a.remove(A.set);
        var z = B.set();
        A.set = z;
        a.setCN(C, z, 'small-map');
        var y = B.set();
        A.allSet = y;
        z.push(y);
        A.buildSVGMap();
        var x = A.borderThickness,
          w = A.borderColor,
          v = a.rect(B, A.width + x, A.height + x, A.backgroundColor, A.backgroundAlpha, x, w, A.borderAlpha);
        a.setCN(C, v, 'small-map-bg');
        v.translate(-x / 2, -x / 2);
        y.push(v);
        v.toBack();
        var u,
          s,
          v = A.minimizeButtonWidth,
          o = new a.SimpleButton(),
          j = v / 2;
        C.svgIcons
          ? (o.svgIcon = a.line(B, [-j / 2, 0, j / 2], [-j / 4, j / 4, -j / 4], A.iconColor, 1, 1))
          : o.setIcon(C.pathToImages + 'arrowDown.gif', v);
        o.setClickHandler(A.minimize, A);
        o.init(B, v, v, w, 1, 1, w, 1);
        a.setCN(C, o.set, 'small-map-down');
        o = o.set;
        A.downButtonSet = o;
        z.push(o);
        var D = new a.SimpleButton();
        C.svgIcons
          ? (D.svgIcon = a.line(B, [-j / 2, 0, j / 2], [j / 4, -j / 4, j / 4], A.iconColor, 1, 1))
          : D.setIcon(C.pathToImages + 'arrowUp.gif', v);
        D.setClickHandler(A.maximize, A);
        D.init(B, v, v, w, 1, 1, w, 1);
        a.setCN(C, D.set, 'small-map-up');
        w = D.set;
        A.upButtonSet = w;
        w.hide();
        z.push(w);
        var d, i;
        isNaN(A.top) || ((u = C.getY(A.top) + x), (i = 0));
        isNaN(A.bottom) || ((u = C.getY(A.bottom, !0) - A.height - x), (i = A.height - v + x / 2));
        isNaN(A.left) || ((s = C.getX(A.left) + x), (d = -x / 2));
        isNaN(A.right) || ((s = C.getX(A.right, !0) - A.width - x), (d = A.width - v + x / 2));
        x = B.set();
        x.clipRect(1, 1, A.width, A.height);
        y.push(x);
        A.rectangleC = x;
        z.translate(s, u);
        o.translate(d, i);
        w.translate(d, i);
        y.mouseup(function () {
          A.handleMouseUp();
        });
        A.drawRectangle();
      } else {
        a.remove(A.allSet), a.remove(A.downButtonSet), a.remove(A.upButtonSet);
      }
    },
    minimize: function () {
      this.downButtonSet.hide();
      this.upButtonSet.show();
      this.allSet.hide();
    },
    maximize: function () {
      this.downButtonSet.show();
      this.upButtonSet.hide();
      this.allSet.show();
    },
    buildSVGMap: function () {
      var i = this.chart,
        d = { fill: this.mapColor, stroke: this.mapColor, 'stroke-opacity': 1 },
        p = this.container,
        o = p.set();
      a.setCN(i, o, 'small-map-image');
      var n;
      for (n = 0; n < i.svgAreas.length; n++) {
        var m = p.path(i.svgAreas[n].path).attr(d);
        o.push(m);
      }
      this.allSet.push(o);
      d = o.getBBox();
      p = this.size * i.mapScale;
      n = -d.x * p;
      var m = -d.y * p,
        l = 0,
        j = 0;
      i.centerMap && ((l = (this.width - d.width * p) / 2), (j = (this.height - d.height * p) / 2));
      this.mapWidth = d.width * p;
      this.mapHeight = d.height * p;
      n += l;
      m += j;
      this.dx = l;
      this.dy = j;
      o.translate(n, m, p);
      this.mapSet = o;
      this.mapX = n;
      this.mapY = m;
    },
    update: function () {
      var i = this.chart;
      if (i) {
        var e = i.zoomLevel(),
          n = this.width,
          m = this.height,
          l = n / (i.realWidth * e),
          k = i.mapContainer.getBBox(),
          n = n / e,
          m = m / e,
          j = this.rectangle;
        j.translate(-(i.mapContainer.x + k.x * e) * l + this.dx, -(i.mapContainer.y + k.y * e) * l + this.dy);
        0 < n && 0 < m && (j.setAttr('width', Math.ceil(n + 1)), j.setAttr('height', Math.ceil(m + 1)));
        this.rWidth = n;
        this.rHeight = m;
      }
    },
    drawRectangle: function () {
      var b = this.rectangle;
      a.remove(b);
      b = a.rect(this.container, 10, 10, '#000', 0, 1, this.rectangleColor, 1);
      a.setCN(this.chart, b, 'small-map-rectangle');
      this.rectangleC.push(b);
      this.rectangle = b;
    },
    handleMouseUp: function () {
      var d = this.chart,
        c = d.zoomLevel();
      d.zoomToMapXY(
        c,
        (d.mouseX - this.set.x - this.mapX) / this.size + d.diffX * d.mapScale,
        (d.mouseY - this.set.y - this.mapY) / this.size + d.diffY * d.mapScale
      );
    }
  });
})();
(function () {
  var a = window.AmCharts;
  a.AreasProcessor = a.Class({
    construct: function (b) {
      this.chart = b;
    },
    process: function (r) {
      this.updateAllAreas();
      this.allObjects = [];
      r = r.areas;
      var q = this.chart;
      q.outlines = [];
      var p = r.length,
        o,
        n,
        m = 0,
        j = !1,
        i = !1,
        e = 0;
      for (o = 0; o < p; o++) {
        if (((n = r[o]), (n.value = Number(n.value)), (n = n.value), !isNaN(n))) {
          if (!1 === j || j < n) {
            j = n;
          }
          if (!1 === i || i > n) {
            i = n;
          }
          m += Math.abs(n);
          e++;
        }
      }
      this.minValue = i;
      this.maxValue = j;
      isNaN(q.minValue) || (this.minValue = q.minValue);
      isNaN(q.maxValue) || (this.maxValue = q.maxValue);
      q.maxValueReal = j;
      q.minValueReal = i;
      for (o = 0; o < p; o++) {
        (n = r[o]), isNaN(n.value) ? (n.percents = void 0) : ((n.percents = ((n.value - i) / m) * 100), i == j && (n.percents = 100));
      }
      for (o = 0; o < p; o++) {
        (n = r[o]), this.createArea(n);
      }
      q.outlinesToFront();
    },
    updateAllAreas: function () {
      var y = this.chart,
        x = y.areasSettings,
        w = x.unlistedAreasColor,
        v = x.unlistedAreasAlpha,
        u = x.unlistedAreasOutlineColor,
        s = x.unlistedAreasOutlineAlpha,
        r = y.svgAreas,
        q = y.dataProvider,
        o = q.areas,
        j = {},
        i;
      for (i = 0; i < o.length; i++) {
        j[o[i].id] = o[i];
      }
      for (i = 0; i < r.length; i++) {
        o = r[i];
        if (x.preserveOriginalAttributes) {
          if (o.customAttr) {
            for (var d in o.customAttr) {
              o.setAttr(d, o.customAttr[d]);
            }
          }
        } else {
          void 0 != w && o.setAttr('fill', w);
          isNaN(v) || o.setAttr('fill-opacity', v);
          void 0 != u && o.setAttr('stroke', u);
          isNaN(s) || o.setAttr('stroke-opacity', s);
          var z = x.outlineThickness;
          x.adjustOutlineThickness && (z = z / y.zoomLevel() / y.mapScale);
          o.setAttr('stroke-width', z);
        }
        a.setCN(y, o, 'map-area-unlisted');
        q.getAreasFromMap &&
          !j[o.id] &&
          ((z = new a.MapArea(y.theme)), (z.parentObject = q), (z.id = o.id), (z.outline = o.outline), q.areas.push(z));
      }
    },
    createArea: function (R) {
      var Q = this.chart,
        P = Q.svgAreasById[R.id],
        O = Q.areasSettings;
      if (P && P.className) {
        var N = Q.areasClasses[P.className];
        N && (O = a.processObject(N, a.AreasSettings, Q.theme));
      }
      var M = O.color,
        L = O.alpha,
        K = O.outlineThickness,
        J = O.rollOverColor,
        I = O.selectedColor,
        H = O.rollOverAlpha,
        G = O.rollOverBrightness,
        s = O.outlineColor,
        z = O.outlineAlpha,
        E = O.balloonText,
        d = O.selectable,
        D = O.pattern,
        o = O.rollOverOutlineColor,
        i = O.bringForwardOnHover,
        j = O.preserveOriginalAttributes;
      this.allObjects.push(R);
      R.chart = Q;
      R.baseSettings = O;
      R.autoZoomReal = void 0 == R.autoZoom ? O.autoZoom : R.autoZoom;
      N = R.color;
      void 0 == N && (N = M);
      var F = R.alpha;
      isNaN(F) && (F = L);
      L = R.rollOverAlpha;
      isNaN(L) && (L = H);
      isNaN(L) && (L = F);
      H = R.rollOverColor;
      void 0 == H && (H = J);
      J = R.pattern;
      void 0 == J && (J = D);
      D = R.selectedColor;
      void 0 == D && (D = I);
      I = R.balloonText;
      void 0 === I && (I = E);
      void 0 == O.colorSolid ||
        isNaN(R.value) ||
        ((E = Math.floor((R.value - this.minValue) / ((this.maxValue - this.minValue) / Q.colorSteps))),
        E == Q.colorSteps && E--,
        (E *= 1 / (Q.colorSteps - 1)),
        this.maxValue == this.minValue && (E = 1),
        (R.colorReal = a.getColorFade(N, O.colorSolid, E)));
      void 0 != R.color && (R.colorReal = R.color);
      void 0 == R.selectable && (R.selectable = d);
      void 0 == R.colorReal && (R.colorReal = M);
      M = R.outlineColor;
      void 0 == M && (M = s);
      s = R.outlineAlpha;
      isNaN(s) && (s = z);
      z = R.outlineThickness;
      isNaN(z) && (z = K);
      K = R.rollOverOutlineColor;
      void 0 == K && (K = o);
      o = R.rollOverBrightness;
      void 0 == o && (o = G);
      void 0 == R.bringForwardOnHover && (R.bringForwardOnHover = i);
      void 0 == R.preserveOriginalAttributes && (R.preserveOriginalAttributes = j);
      isNaN(O.selectedBrightness) || (D = a.adjustLuminosity(R.colorReal, O.selectedBrightness / 100));
      R.alphaReal = F;
      R.rollOverColorReal = H;
      R.rollOverAlphaReal = L;
      R.balloonTextReal = I;
      R.selectedColorReal = D;
      R.outlineColorReal = M;
      R.outlineAlphaReal = s;
      R.rollOverOutlineColorReal = K;
      R.outlineThicknessReal = z;
      R.patternReal = J;
      R.rollOverBrightnessReal = o;
      R.accessibleLabel || (R.accessibleLabel = O.accessibleLabel);
      a.processDescriptionWindow(O, R);
      if (
        P &&
        ((G = P.area),
        (i = P.title),
        (R.enTitle = P.title),
        i && !R.title && (R.title = i),
        (P = Q.language) ? (i = a.mapTranslations) && (P = i[P]) && P[R.enTitle] && (R.titleTr = P[R.enTitle]) : (R.titleTr = void 0),
        G)
      ) {
        P = R.tabIndex;
        void 0 === P && (P = O.tabIndex);
        void 0 !== P && G.setAttr('tabindex', P);
        R.displayObject = G;
        R.outline &&
          ((F = 0),
          (R.alphaReal = 0),
          (R.rollOverAlphaReal = 0),
          (R.mouseEnabled = !1),
          Q.outlines.push(G),
          G.node.setAttribute('pointer-events', 'none'));
        R.mouseEnabled && Q.addObjectEventListeners(G, R);
        var x;
        void 0 != N && (x = N);
        void 0 != R.colorReal && (x = R.showAsSelected || Q.selectedObject == R ? R.selectedColorReal : R.colorReal);
        G.node.setAttribute('class', '');
        a.setCN(Q, G, 'map-area');
        a.setCN(Q, G, 'map-area-' + G.id);
        O.adjustOutlineThickness && (z = z / Q.zoomLevel() / Q.mapScale);
        R.preserveOriginalAttributes ||
          (G.setAttr('fill', x),
          G.setAttr('stroke', M),
          G.setAttr('stroke-opacity', s),
          G.setAttr('stroke-width', z),
          G.setAttr('fill-opacity', F));
        Q.makeObjectAccessible(R);
        J && G.pattern(J, Q.mapScale, Q.path);
        R.hidden && G.hide();
      }
    }
  });
})();
(function () {
  var a = window.AmCharts;
  a.AreasSettings = a.Class({
    construct: function (b) {
      this.cname = 'AreasSettings';
      this.alpha = 1;
      this.autoZoom = !1;
      this.balloonText = '[[title]]';
      this.color = '#FFCC00';
      this.colorSolid = '#990000';
      this.unlistedAreasAlpha = 1;
      this.unlistedAreasColor = '#DDDDDD';
      this.outlineColor = '#FFFFFF';
      this.outlineThickness = this.outlineAlpha = 1;
      this.selectedColor = this.rollOverOutlineColor = '#CC0000';
      this.unlistedAreasOutlineColor = '#FFFFFF';
      this.unlistedAreasOutlineAlpha = 1;
      this.descriptionWindowWidth = 250;
      this.bringForwardOnHover = this.adjustOutlineThickness = !0;
      this.accessibleLabel = '[[title]] [[value]] [[description]]';
      a.applyTheme(this, b, this.cname);
    }
  });
})();
(function () {
  var a = window.AmCharts;
  a.ImagesProcessor = a.Class({
    construct: function (b) {
      this.chart = b;
      this.reset();
    },
    process: function (f) {
      var e = f.images,
        h;
      for (h = e.length - 1; 0 <= h; h--) {
        var g = e[h];
        this.createImage(g, h);
        g.parentArray = e;
      }
      this.counter = h;
      f.parentObject && f.remainVisible && this.process(f.parentObject);
    },
    createImage: function (af, ae) {
      af = a.processObject(af, a.MapImage);
      af.arrays = [];
      isNaN(ae) && (this.counter++, (ae = this.counter));
      var ad = this.chart,
        ac = ad.container,
        ab = ad.mapImagesContainer,
        aa = ad.stageImagesContainer,
        Z = ad.imagesSettings;
      af.remove && af.remove();
      var Y = Z.color,
        X = Z.alpha,
        W = Z.rollOverColor,
        V = Z.rollOverOutlineColor,
        U = Z.selectedColor,
        N = Z.balloonText,
        Q = Z.outlineColor,
        S = Z.outlineAlpha,
        i = Z.outlineThickness,
        R = Z.selectedScale,
        L = Z.rollOverScale,
        s = Z.selectable,
        J = Z.labelPosition,
        T = Z.labelColor,
        P = Z.labelFontSize,
        j = Z.bringForwardOnHover,
        d = Z.labelRollOverColor,
        K = Z.rollOverBrightness,
        M = Z.selectedLabelColor;
      af.index = ae;
      af.chart = ad;
      af.baseSettings = ad.imagesSettings;
      var o = ac.set();
      af.displayObject = o;
      var I = af.color;
      void 0 == I && (I = Y);
      Y = af.alpha;
      isNaN(Y) && (Y = X);
      void 0 == af.bringForwardOnHover && (af.bringForwardOnHover = j);
      X = af.outlineAlpha;
      isNaN(X) && (X = S);
      S = af.rollOverColor;
      void 0 == S && (S = W);
      W = af.selectedColor;
      void 0 == W && (W = U);
      U = af.balloonText;
      void 0 === U && (U = N);
      N = af.outlineColor;
      void 0 == N && (N = Q);
      af.outlineColorReal = N;
      Q = af.outlineThickness;
      isNaN(Q) && (Q = i);
      (i = af.labelPosition) || (i = J);
      J = af.labelColor;
      void 0 == J && (J = T);
      T = af.labelRollOverColor;
      void 0 == T && (T = d);
      d = af.selectedLabelColor;
      void 0 == d && (d = M);
      M = af.labelFontSize;
      isNaN(M) && (M = P);
      P = af.selectedScale;
      isNaN(P) && (P = R);
      R = af.rollOverScale;
      isNaN(R) && (R = L);
      L = af.rollOverBrightness;
      void 0 == L && (L = K);
      void 0 == af.selectable && (af.selectable = s);
      af.colorReal = I;
      isNaN(Z.selectedBrightness) || (W = a.adjustLuminosity(af.colorReal, Z.selectedBrightness / 100));
      af.alphaReal = Y;
      af.rollOverColorReal = S;
      af.balloonTextReal = U;
      af.selectedColorReal = W;
      af.labelColorReal = J;
      af.labelRollOverColorReal = T;
      af.selectedLabelColorReal = d;
      af.labelFontSizeReal = M;
      af.labelPositionReal = i;
      af.selectedScaleReal = P;
      af.rollOverScaleReal = R;
      af.rollOverOutlineColorReal = V;
      af.rollOverBrightnessReal = L;
      af.accessibleLabel || (af.accessibleLabel = Z.accessibleLabel);
      a.processDescriptionWindow(Z, af);
      af.centeredReal = void 0 == af.centered ? Z.centered : af.centered;
      V = af.type;
      L = af.imageURL;
      R = af.svgPath;
      P = af.width;
      M = af.height;
      s = af.scale;
      isNaN(af.percentWidth) || (P = (af.percentWidth / 100) * ad.realWidth);
      isNaN(af.percentHeight) || (M = (af.percentHeight / 100) * ad.realHeight);
      var O;
      L || V || R || ((V = 'circle'), (P = 1), (X = Y = 0));
      S = K = 0;
      Z = af.selectedColorReal;
      if (V) {
        isNaN(P) && (P = 10);
        isNaN(M) && (M = 10);
        'kilometers' == af.widthAndHeightUnits && ((P = ad.kilometersToPixels(af.width)), (M = ad.kilometersToPixels(af.height)));
        'miles' == af.widthAndHeightUnits && ((P = ad.milesToPixels(af.width)), (M = ad.milesToPixels(af.height)));
        if ('circle' == V || 'bubble' == V) {
          M = P;
        }
        O = this.createPredefinedImage(I, N, Q, V, P, M);
        S = K = 0;
        af.centeredReal ? (isNaN(af.right) || (K = P * s), isNaN(af.bottom) || (S = M * s)) : ((K = (P * s) / 2), (S = (M * s) / 2));
        O.translate(K, S, s, !0);
      } else {
        L
          ? (isNaN(P) && (P = 10),
            isNaN(M) && (M = 10),
            (O = ac.image(L, 0, 0, P, M)),
            O.node.setAttribute('preserveAspectRatio', 'none'),
            O.setAttr('opacity', Y),
            af.centeredReal &&
              ((K = isNaN(af.right) ? -P / 2 : P / 2), (S = isNaN(af.bottom) ? -M / 2 : M / 2), O.translate(K, S, NaN, !0)))
          : R &&
            ((O = ac.path(R)),
            (L = O.getBBox()),
            af.centeredReal
              ? ((K = -L.x * s - (L.width * s) / 2),
                isNaN(af.right) || (K = -K),
                (S = -L.y * s - (L.height * s) / 2),
                isNaN(af.bottom) || (S = -S))
              : (K = S = 0),
            O.translate(K, S, s, !0),
            (O.x = K),
            (O.y = S));
      }
      O &&
        (o.push(O),
        (af.image = O),
        O.setAttr('stroke-opacity', X),
        O.setAttr('stroke-width', Q),
        O.setAttr('stroke', N),
        O.setAttr('fill-opacity', Y),
        'bubble' != V && O.setAttr('fill', I),
        a.setCN(ad, O, 'map-image'),
        void 0 != af.id && a.setCN(ad, O, 'map-image-' + af.id));
      I = af.labelColorReal;
      (!af.showAsSelected && ad.selectedObject != af) || void 0 == Z || (O && O.setAttr('fill', Z), (I = af.selectedLabelColorReal));
      O = null;
      void 0 !== af.label &&
        ((O = a.text(ac, af.label, I, ad.fontFamily, af.labelFontSizeReal, af.labelAlign)),
        a.setCN(ad, O, 'map-image-label'),
        void 0 !== af.id && a.setCN(ad, O, 'map-image-label-' + af.id),
        (I = af.labelBackgroundAlpha),
        (Y = af.labelBackgroundColor) &&
          0 < I &&
          ((X = O.getBBox()),
          (ac = a.rect(ac, X.width + 16, X.height + 10, Y, I)),
          a.setCN(ad, ac, 'map-image-label-background'),
          void 0 != af.id && a.setCN(ad, ac, 'map-image-label-background-' + af.id),
          o.push(ac),
          (af.labelBG = ac)),
        (af.imageLabel = O),
        o.push(O),
        a.setCN(ad, o, 'map-image-container'),
        void 0 != af.id && a.setCN(ad, o, 'map-image-container-' + af.id),
        this.labelsToReposition.push(af),
        af.arrays.push({ arr: this.labelsToReposition, el: af }));
      ac = isNaN(af.latitude) || isNaN(af.longitude) ? !0 : !1;
      af.lineId && (O = this.chart.getObjectById(af.lineId)) && 0 < O.longitudes.length && (ac = !1);
      ac ? aa.push(o) : ab.push(o);
      o.toBack();
      o &&
        ((o.rotation = af.rotation),
        isNaN(af.rotation) || o.rotate(af.rotation),
        af.arrays.push({ arr: this.allSvgObjects, el: o }),
        this.allSvgObjects.push(o));
      this.allObjects.push(af);
      ad.makeObjectAccessible(af);
      ab = af.tabIndex;
      void 0 === ab && (ab = ad.imagesSettings.tabIndex);
      void 0 !== ab && o.setAttr('tabindex', ab);
      af.arrays.push({ arr: this.allObjects, el: af });
      isNaN(af.longitude) ||
        isNaN(af.latitude) ||
        !af.fixedSize ||
        ((af.objToResize = { image: o, mapImage: af, scale: 1 }),
        this.objectsToResize.push(af.objToResize),
        af.arrays.push({ arr: this.objectsToResize, el: af.objToResize }));
      this.updateSizeAndPosition(af);
      af.mouseEnabled && ad.addObjectEventListeners(o, af);
      af.hidden && o.hide();
      a.removeFromArray(ad.updatableImages, af);
      af.animateAlongLine && (ad.updatableImages.push(af), af.delayAnimateAlong());
      return af;
    },
    updateSizeAndPosition: function (A) {
      var z = this.chart,
        y = A.displayObject,
        x = z.getX(A.left),
        w = z.getY(A.top),
        v,
        u = A.image.getBBox();
      isNaN(A.right) || (x = z.getX(A.right, !0) - u.width * A.scale);
      isNaN(A.bottom) || (w = z.getY(A.bottom, !0) - u.height * A.scale);
      var s = A.longitude,
        q = A.latitude,
        o = A.positionOnLine,
        u = A.imageLabel,
        j = this.chart.zoomLevel(),
        i,
        B;
      A.lineId && (A.line = this.chart.getObjectById(A.lineId));
      if (A.line && A.line.getCoordinates) {
        A.line.chart = z;
        var d = A.line.getCoordinates(o, A.lineSegment);
        d &&
          ((s = z.coordinateToLongitude(d.x)),
          (q = z.coordinateToLatitude(d.y)),
          (i = d.x),
          (B = d.y),
          A.animateAngle && (v = a.radiansToDegrees(d.angle)));
      }
      isNaN(v) || y.rotate(v + A.extraAngle);
      if (!isNaN(x) && !isNaN(w)) {
        y.translate(x, w, NaN, !0);
      } else {
        if (!isNaN(q) && !isNaN(s)) {
          if (((w = z.coordinatesToXY(s, q)), (x = w.x), (w = w.y), isNaN(i) || (x = i), isNaN(B) || (w = B), A.fixedSize)) {
            i = A.positionScale;
            isNaN(i) ? (i = 0) : (--i, (i *= 1 - 2 * Math.abs(o - 0.5)));
            if ((o = A.objectToResize)) {
              o.scale = 1 + i;
            }
            y.translate(x, w, 1 / j + i, !0);
          } else {
            y.translate(x, w, NaN, !0);
          }
        }
      }
      this.positionLabel(u, A, A.labelPositionReal);
    },
    positionLabel: function (t, s, r) {
      if (t) {
        var q = s.image,
          p = 0,
          o = 0,
          n = 0,
          j = 0;
        q &&
          ((j = q.getBBox()),
          (o = q.y + j.y),
          (p = q.x + j.x),
          (n = j.width),
          (j = j.height),
          s.svgPath && ((n *= s.scale), (j *= s.scale)));
        var q = t.getBBox(),
          i = q.width,
          e = q.height;
        'right' == r && ((p += n + i / 2 + 5), (o += j / 2 - 2));
        'left' == r && ((p += -i / 2 - 5), (o += j / 2 - 2));
        'top' == r && ((o -= e / 2 + 3), (p += n / 2));
        'bottom' == r && ((o += j + e / 2), (p += n / 2));
        'middle' == r && ((p += n / 2), (o += j / 2));
        t.translate(p + s.labelShiftX, o + s.labelShiftY, NaN, !0);
        t = s.labelFontSizeReal;
        s.labelBG && s.labelBG.translate(p - q.width / 2 + s.labelShiftX - 9, o - t / 2 + s.labelShiftY - 4, NaN, !0);
      }
    },
    createPredefinedImage: function (i, d, p, o, n, m) {
      var l = this.chart.container,
        j;
      switch (o) {
        case 'circle':
          j = a.circle(l, n / 2, i, 1, p, d, 1);
          break;
        case 'rectangle':
          j = a.polygon(l, [-n / 2, n / 2, n / 2, -n / 2], [m / 2, m / 2, -m / 2, -m / 2], i, 1, p, d, 1, 0, !0);
          break;
        case 'bubble':
          j = a.circle(l, n / 2, i, 1, p, d, 1, !0);
          break;
        case 'hexagon':
          (n /= Math.sqrt(3)),
            (j = a.polygon(
              l,
              [0.866 * n, 0 * n, -0.866 * n, -0.866 * n, 0 * n, 0.866 * n],
              [0.5 * n, 1 * n, 0.5 * n, -0.5 * n, -1 * n, -0.5 * n],
              i,
              1,
              p,
              d,
              1
            ));
      }
      return j;
    },
    reset: function () {
      this.objectsToResize = [];
      this.allSvgObjects = [];
      this.allObjects = [];
      this.allLabels = [];
      this.labelsToReposition = [];
    }
  });
})();
(function () {
  var a = window.AmCharts;
  a.ImagesSettings = a.Class({
    construct: function (b) {
      this.cname = 'ImagesSettings';
      this.balloonText = '[[title]]';
      this.alpha = 1;
      this.borderAlpha = 0;
      this.borderThickness = 1;
      this.labelPosition = 'right';
      this.labelColor = '#000000';
      this.labelFontSize = 11;
      this.color = '#000000';
      this.labelRollOverColor = '#00CC00';
      this.centered = !0;
      this.rollOverScale = this.selectedScale = 1;
      this.descriptionWindowWidth = 250;
      this.bringForwardOnHover = !0;
      this.outlineColor = 'transparent';
      this.adjustAnimationSpeed = !1;
      this.baseAnimationDistance = 500;
      this.pauseDuration = 0;
      this.easingFunction = a.easeInOutQuad;
      this.animationDuration = 3;
      this.positionScale = 1;
      this.accessibleLabel = '[[title]] [[description]]';
      a.applyTheme(this, b, this.cname);
    }
  });
})();
(function () {
  var a = window.AmCharts;
  a.LinesProcessor = a.Class({
    construct: function (b) {
      this.chart = b;
      this.reset();
    },
    process: function (f) {
      var e = f.lines,
        h;
      for (h = 0; h < e.length; h++) {
        var g = e[h];
        this.createLine(g, h);
        g.parentArray = e;
      }
      this.counter = h;
      f.parentObject && f.remainVisible && this.process(f.parentObject);
    },
    createLine: function (aw, av) {
      aw = a.processObject(aw, a.MapLine);
      isNaN(av) && (this.counter++, (av = this.counter));
      aw.index = av;
      aw.remove && aw.remove();
      var au = this.chart,
        at = au.linesSettings,
        ar = this.objectsToResize,
        aq = au.mapLinesContainer,
        ap = au.stageLinesContainer,
        ao = at.thickness,
        an = at.dashLength,
        am = at.arrow,
        al = at.arrowSize,
        ak = at.arrowColor,
        ah = at.arrowAlpha,
        ai = at.color,
        aj = at.alpha,
        ac = at.rollOverColor,
        Z = at.selectedColor,
        ag = at.rollOverAlpha,
        ae = at.balloonText,
        af = at.bringForwardOnHover,
        aa = at.arc,
        Y = at.rollOverBrightness,
        ad = au.container;
      aw.chart = au;
      aw.baseSettings = at;
      var ab = ad.set();
      aw.displayObject = ab;
      var V = aw.tabIndex;
      void 0 === V && (V = at.tabIndex);
      void 0 !== V && ab.setAttr('tabindex', V);
      this.allSvgObjects.push(ab);
      aw.arrays.push({ arr: this.allSvgObjects, el: ab });
      this.allObjects.push(aw);
      aw.arrays.push({ arr: this.allObjects, el: aw });
      aw.mouseEnabled && au.addObjectEventListeners(ab, aw);
      if (aw.remainVisible || au.selectedObject == aw.parentObject) {
        V = aw.thickness;
        isNaN(V) && (V = ao);
        ao = aw.dashLength;
        isNaN(ao) && (ao = an);
        an = aw.color;
        void 0 == an && (an = ai);
        ai = aw.alpha;
        isNaN(ai) && (ai = aj);
        aj = aw.rollOverAlpha;
        isNaN(aj) && (aj = ag);
        isNaN(aj) && (aj = ai);
        ag = aw.rollOverColor;
        void 0 == ag && (ag = ac);
        ac = aw.selectedColor;
        void 0 == ac && (ac = Z);
        Z = aw.balloonText;
        void 0 === Z && (Z = ae);
        ae = aw.arc;
        isNaN(ae) && (ae = aa);
        aa = aw.arrow;
        if (!aa || ('none' == aa && 'none' != am)) {
          aa = am;
        }
        am = aw.arrowColor;
        void 0 == am && (am = ak);
        void 0 == am && (am = an);
        ak = aw.arrowAlpha;
        isNaN(ak) && (ak = ah);
        isNaN(ak) && (ak = ai);
        ah = aw.arrowSize;
        isNaN(ah) && (ah = al);
        al = aw.rollOverBrightness;
        void 0 == al && (al = Y);
        aw.colorReal = an;
        aw.arrowColor = am;
        isNaN(at.selectedBrightness) || (ac = a.adjustLuminosity(aw.colorReal, at.selectedBrightness / 100));
        aw.alphaReal = ai;
        aw.rollOverColorReal = ag;
        aw.rollOverAlphaReal = aj;
        aw.balloonTextReal = Z;
        aw.selectedColorReal = ac;
        aw.thicknessReal = V;
        aw.rollOverBrightnessReal = al;
        aw.accessibleLabel || (aw.accessibleLabel = at.accessibleLabel);
        void 0 === aw.shiftArrow && (aw.shiftArrow = at.shiftArrow);
        void 0 == aw.bringForwardOnHover && (aw.bringForwardOnHover = af);
        a.processDescriptionWindow(at, aw);
        af = this.processCoordinates(aw.x, au.realWidth);
        Y = this.processCoordinates(aw.y, au.realHeight);
        al = aw.longitudes;
        at = aw.latitudes;
        aj = al.length;
        if (0 < aj) {
          for (af = [], Y = [], ag = 0; ag < aj; ag++) {
            (Z = au.coordinatesToXY(al[ag], at[ag])), af.push(Z.x), Y.push(Z.y);
          }
        }
        if (0 < af.length) {
          aw.segments = af.length;
          a.dx = 0;
          a.dy = 0;
          var W,
            T,
            U,
            aj = 10 * (1 - Math.abs(ae));
          10 <= aj && (aj = NaN);
          1 > aj && (aj = 1);
          aw.arcRadius = [];
          aw.distances = [];
          al = au.mapContainer.scale;
          if (isNaN(aj)) {
            for (aj = 0; aj < af.length - 1; aj++) {
              (T = Math.sqrt(Math.pow(af[aj + 1] - af[aj], 2) + Math.pow(Y[aj + 1] - Y[aj], 2))), (aw.distances[aj] = T);
            }
            aj = a.line(ad, af, Y, an, 1, V / al, ao, !1, !1, !0);
            an = a.line(ad, af, Y, an, 0.001, 5 / al, ao, !1, !1, !0);
            aj.setAttr('stroke-linecap', 'round');
          } else {
            ag = 1;
            0 > ae && (ag = 0);
            Z = {
              fill: 'none',
              stroke: an,
              'stroke-opacity': 1,
              'stroke-width': V / al,
              'fill-opacity': 0,
              'stroke-linecap': 'round'
            };
            void 0 !== ao && 0 < ao && (Z['stroke-dasharray'] = ao);
            for (var ao = '', X = 0; X < af.length - 1; X++) {
              var Q = af[X],
                R = af[X + 1],
                s = Y[X],
                i = Y[X + 1];
              T = Math.sqrt(Math.pow(R - Q, 2) + Math.pow(i - s, 2));
              U = (T / 2) * aj;
              W = 270 + (180 * Math.acos(T / 2 / U)) / Math.PI;
              isNaN(W) && (W = 270);
              if (Q < R) {
                var d = Q,
                  Q = R,
                  R = d,
                  d = s,
                  s = i,
                  i = d;
                W = -W;
              }
              0 < ae && (W = -W);
              ao += 'M' + Q + ',' + s + 'A' + U + ',' + U + ',0,0,' + ag + ',' + R + ',' + i;
              aw.arcRadius[X] = U;
              aw.distances[X] = T;
            }
            aj = ad.path(ao).attr(Z);
            an = ad.path(ao).attr({
              'fill-opacity': 0,
              stroke: an,
              'stroke-width': 5 / al,
              'stroke-opacity': 0.001,
              fill: 'none'
            });
          }
          a.setCN(au, aj, 'map-line');
          void 0 != aw.id && a.setCN(au, aj, 'map-line-' + aw.id);
          a.dx = 0.5;
          a.dy = 0.5;
          ab.push(aj);
          ab.push(an);
          aj.setAttr('opacity', ai);
          if ('none' != aa) {
            var S, o, j;
            if ('end' == aa || 'both' == aa) {
              (ag = af[af.length - 1]),
                (X = Y[Y.length - 1]),
                1 < af.length ? ((Z = af[af.length - 2]), (S = Y[Y.length - 2])) : ((Z = ag), (S = X)),
                (S = (180 * Math.atan((X - S) / (ag - Z))) / Math.PI),
                isNaN(W) || (S += W),
                (o = ag),
                (j = X),
                (S = 0 > ag - Z ? S - 90 : S + 90);
            }
            ai = [-ah / 2 - 0.5, -0.5, ah / 2 - 0.5];
            ao = [ah, -0.5, ah];
            aw.shiftArrow && 'middle' != aa && (ao = [0, 1.2 * -ah, 0]);
            'both' == aa &&
              ((ah = a.polygon(ad, ai, ao, am, ak, 1, am, ak, void 0, !0)),
              ab.push(ah),
              ah.translate(o, j, 1 / al, !0),
              isNaN(S) || ah.rotate(S),
              a.setCN(au, aj, 'map-line-arrow'),
              void 0 != aw.id && a.setCN(au, aj, 'map-line-arrow-' + aw.id),
              aw.fixedSize && ar.push(ah));
            if ('start' == aa || 'both' == aa) {
              (ah = af[0]),
                (j = Y[0]),
                1 < af.length ? ((ag = af[1]), (o = Y[1])) : ((ag = ah), (o = j)),
                (S = (180 * Math.atan((j - o) / (ah - ag))) / Math.PI),
                isNaN(W) || (S -= W),
                (o = ah),
                (S = 0 > ah - ag ? S - 90 : S + 90);
            }
            'middle' == aa &&
              ((ag = af[af.length - 1]),
              (X = Y[Y.length - 1]),
              1 < af.length ? ((Z = af[af.length - 2]), (S = Y[Y.length - 2])) : ((Z = ag), (S = X)),
              (o = Z + (ag - Z) / 2),
              (j = S + (X - S) / 2),
              (S = (180 * Math.atan((X - S) / (ag - Z))) / Math.PI),
              isNaN(W) ||
                ((W = T / 2),
                (U -= Math.sqrt(U * U - W * W)),
                0 > ae && (U = -U),
                (W = Math.sin((S / 180) * Math.PI)),
                -1 == W && (W = 1),
                (o -= W * U),
                (j += Math.cos((S / 180) * Math.PI) * U)),
              (S = 0 > ag - Z ? S - 90 : S + 90));
            ah = a.polygon(ad, ai, ao, am, ak, 1, am, ak, void 0, !0);
            a.setCN(au, aj, 'map-line-arrow');
            void 0 != aw.id && a.setCN(au, aj, 'map-line-arrow-' + aw.id);
            ab.push(ah);
            ah.translate(o, j, 1 / al, !0);
            isNaN(S) || ah.rotate(S);
            aw.fixedSize && (ar.push(ah), aw.arrays.push({ arr: ar, el: ah }));
            aw.arrowSvg = ah;
          }
          aw.fixedSize &&
            aj &&
            ((ar = { line: aj, thickness: V }),
            this.linesToResize.push(ar),
            aw.arrays.push({ arr: this.linesToResize, el: ar }),
            (ar = { line: an, thickness: 5 }),
            this.linesToResize.push(ar),
            aw.arrays.push({ arr: this.linesToResize, el: ar }));
          aw.lineSvg = aj;
          aw.showAsSelected && !isNaN(ac) && aj.setAttr('stroke', ac);
          0 < at.length ? aq.push(ab) : ap.push(ab);
          aw.hidden && ab.hide();
          au.makeObjectAccessible(aw);
        }
      }
    },
    processCoordinates: function (h, e) {
      var l = [],
        k;
      for (k = 0; k < h.length; k++) {
        var j = h[k],
          i = Number(j);
        isNaN(i) && (i = (Number(j.replace('%', '')) * e) / 100);
        isNaN(i) || l.push(i);
      }
      return l;
    },
    reset: function () {
      this.objectsToResize = [];
      this.allSvgObjects = [];
      this.allObjects = [];
      this.linesToResize = [];
    }
  });
})();
(function () {
  var a = window.AmCharts;
  a.LinesSettings = a.Class({
    construct: function (b) {
      this.cname = 'LinesSettings';
      this.balloonText = '[[title]]';
      this.thickness = 1;
      this.dashLength = 0;
      this.arrowSize = 10;
      this.arrowAlpha = 1;
      this.arrow = 'none';
      this.color = '#990000';
      this.descriptionWindowWidth = 250;
      this.bringForwardOnHover = !0;
      a.applyTheme(this, b, this.cname);
    }
  });
})();
(function () {
  var a = window.AmCharts;
  a.MapObject = a.Class({
    construct: function (b) {
      this.fixedSize = this.mouseEnabled = !0;
      this.images = [];
      this.lines = [];
      this.areas = [];
      this.remainVisible = !0;
      this.passZoomValuesToTarget = !1;
      this.objectType = this.cname;
      a.applyTheme(this, b, 'MapObject');
      this.arrays = [];
    },
    deleteObject: function () {
      this.remove();
      this.parentArray && a.removeFromArray(this.parentArray, this);
      if (this.arrays) {
        for (var b = 0; b < this.arrays.length; b++) {
          a.removeFromArray(this.arrays[b].arr, this.arrays[b].el);
        }
      }
      this.arrays = [];
    }
  });
})();
(function () {
  var a = window.AmCharts;
  a.MapArea = a.Class({
    inherits: a.MapObject,
    construct: function (b) {
      this.cname = 'MapArea';
      a.MapArea.base.construct.call(this, b);
      a.applyTheme(this, b, this.cname);
    },
    validate: function () {
      this.chart.areasProcessor.createArea(this);
    }
  });
})();
(function () {
  var a = window.AmCharts;
  a.MapLine = a.Class({
    inherits: a.MapObject,
    construct: function (b) {
      this.cname = 'MapLine';
      this.longitudes = [];
      this.latitudes = [];
      this.x = [];
      this.y = [];
      this.segments = 0;
      this.arrow = 'none';
      a.MapLine.base.construct.call(this, b);
      a.applyTheme(this, b, this.cname);
    },
    validate: function () {
      this.chart.linesProcessor.createLine(this);
    },
    remove: function () {
      var b = this.displayObject;
      b && b.remove();
    },
    getCoordinates: function (x, w) {
      isNaN(w) && (w = 0);
      isNaN(this.arc) || this.isValid || ((this.isValid = !0), this.validate());
      if (!isNaN(x)) {
        var v, u, t, s, r, q;
        if (1 < this.longitudes.length) {
          u = this.chart.coordinatesToXY(this.longitudes[w], this.latitudes[w]);
          var o = this.chart.coordinatesToXY(this.longitudes[w + 1], this.latitudes[w + 1]);
          v = u.x;
          t = o.x;
          u = u.y;
          s = o.y;
        } else {
          1 < this.x.length && ((v = this.x[w]), (t = this.x[w + 1]), (u = this.y[w]), (s = this.y[w + 1]));
        }
        o = Math.sqrt(Math.pow(t - v, 2) + Math.pow(s - u, 2));
        v < t && !isNaN(this.arc) && 0 !== this.arc && (x = 1 - x);
        r = v + (t - v) * x;
        q = u + (s - u) * x;
        var j = Math.atan2(s - u, t - v);
        if (!isNaN(this.arc) && 0 !== this.arc && this.arcRadius) {
          var i = 0;
          v < t && ((i = v), (v = t), (t = i), (i = u), (u = s), (s = i), (i = Math.PI));
          q = this.arcRadius[w];
          0 > this.arc && (o = -o);
          r = v + (t - v) / 2 + (Math.sqrt(q * q - (o / 2) * (o / 2)) * (u - s)) / o;
          var d = u + (s - u) / 2 + (Math.sqrt(q * q - (o / 2) * (o / 2)) * (t - v)) / o;
          v = (180 * Math.atan2(u - d, v - r)) / Math.PI;
          t = (180 * Math.atan2(s - d, t - r)) / Math.PI;
          180 < t - v && (t -= 360);
          j = a.degreesToRadians(v + (t - v) * x);
          r += q * Math.cos(j);
          q = d + q * Math.sin(j);
          j = 0 < this.arc ? j + Math.PI / 2 : j - Math.PI / 2;
          j += i;
        }
        this.distance = o;
        return { x: r, y: q, angle: j };
      }
    },
    fixToStage: function () {
      if (0 < this.latitudes.length) {
        this.y = [];
        for (var d = 0; d < this.latitudes.length; d++) {
          var c = this.chart.coordinatesToStageXY(this.longitudes[d], this.latitudes[d]);
          this.y.push(c.y);
          this.x.push(c.x);
        }
        this.latitudes = [];
        this.longitudes = [];
      }
      this.validate();
    },
    fixToMap: function () {
      if (0 < this.y.length) {
        this.latitudes = [];
        for (var d = 0; d < this.y.length; d++) {
          var c = this.chart.stageXYToCoordinates(this.x[d], this.y[d]);
          this.latitudes.push(c.latitude);
          this.longitudes.push(c.longitude);
        }
        this.y = [];
        this.x = [];
      }
      this.validate();
    }
  });
})();
(function () {
  var a = window.AmCharts;
  a.MapImage = a.Class({
    inherits: a.MapObject,
    construct: function (b) {
      this.cname = 'MapImage';
      this.scale = 1;
      this.widthAndHeightUnits = 'pixels';
      this.labelShiftY = this.labelShiftX = 0;
      this.positionOnLine = 0.5;
      this.direction = 1;
      this.lineSegment = this.extraAngle = 0;
      this.animateAngle = !0;
      this.createEvents('animationStart', 'animationEnd');
      a.MapImage.base.construct.call(this, b);
      a.applyTheme(this, b, this.cname);
      this.delayCounter = 0;
    },
    validate: function () {
      this.chart.imagesProcessor.createImage(this);
    },
    updatePosition: function () {
      this.chart.imagesProcessor.updateSizeAndPosition(this);
    },
    remove: function () {
      var b = this.displayObject;
      b && b.remove();
      (b = this.imageLabel) && b.remove();
    },
    animateTo: function (f, e, h, g) {
      isNaN(h) || (this.animationDuration = h);
      g && (this.easingFunction = g);
      this.finalX = f;
      this.finalY = e;
      isNaN(this.longitude) || (this.initialX = this.longitude);
      isNaN(this.left) || (this.initialX = this.left);
      isNaN(this.right) || (this.initialX = this.right);
      isNaN(this.latitude) || (this.initialY = this.latitude);
      isNaN(this.top) || (this.initialY = this.top);
      isNaN(this.bottom) || (this.initialY = this.bottom);
      this.animatingAlong = !1;
      this.animate();
    },
    animateAlong: function (e, d, f) {
      1 == this.positionOnLine && this.flipDirection && ((this.direction = -1), (this.extraAngle = 180));
      isNaN(d) || (this.animationDuration = d);
      f && (this.easingFunction = f);
      e && (this.line = this.chart.getObjectById(e));
      this.animateAlongLine = this.line;
      this.animatingAlong = !0;
      this.animate();
    },
    animate: function () {
      var d = this.chart.imagesSettings,
        c = this.animationDuration;
      isNaN(c) && (c = d.animationDuration);
      this.totalFrames = c * a.updateRate;
      c = 1;
      this.line &&
        d.adjustAnimationSpeed &&
        (this.line.distances &&
          ((c = this.line.distances[this.lineSegment] * this.chart.zoomLevel()), (c = Math.abs(c / d.baseAnimationDistance))),
        (this.totalFrames = Math.round(c * this.totalFrames)));
      this.frame = 0;
      this.fire({
        type: 'animationStart',
        chart: this.chart,
        image: this,
        lineSegment: this.lineSegment,
        direction: this.direction
      });
    },
    update: function () {
      var e = this.totalFrames;
      this.frame++;
      this.delayCounter--;
      0 === this.delayCounter && this.animateAlong();
      if (!(0 < this.delayCounter)) {
        if (this.frame <= e) {
          this.updatePosition();
          var d = this.chart.imagesSettings,
            f = this.easingFunction;
          f || (f = d.easingFunction);
          e = f(0, this.frame, 0, 1, e);
          -1 == this.direction && (e = 1 - e);
          this.animatingAlong
            ? (this.positionOnLine = e)
            : ((d = this.initialX + (this.finalX - this.initialX) * e),
              isNaN(this.longitude) || (this.longitude = d),
              isNaN(this.left) || (this.left = d),
              isNaN(this.right) || (this.right = d),
              (e = this.initialY + (this.finalY - this.initialY) * e),
              isNaN(this.latitude) || (this.latitude = e),
              isNaN(this.top) || (this.top = e),
              isNaN(this.bottom) || (this.bottom = e));
        } else {
          this.frame == e + 1 &&
            (this.fire({
              type: 'animationEnd',
              chart: this.chart,
              image: this,
              lineSegment: this.lineSegment,
              direction: this.direction
            }),
            this.line &&
              this.animatingAlong &&
              (1 == this.direction
                ? this.lineSegment < this.line.segments - 2
                  ? (this.lineSegment++, this.delayAnimateAlong(), (this.positionOnLine = 0))
                  : this.flipDirection
                    ? ((this.direction = -1), (this.extraAngle = 180), this.delayAnimateAlong())
                    : this.loop && (this.delayAnimateAlong(), (this.lineSegment = 0))
                : 0 < this.lineSegment
                  ? (this.lineSegment--, this.delayAnimateAlong(), (this.positionOnLine = 0))
                  : this.loop && this.flipDirection
                    ? ((this.direction = 1), (this.extraAngle = 0), this.delayAnimateAlong())
                    : this.loop && this.delayAnimateAlong()));
        }
      }
    },
    delayAnimateAlong: function () {
      this.animateAlongLine && (this.delayCounter = this.chart.imagesSettings.pauseDuration * a.updateRate);
    },
    fixToStage: function () {
      if (!isNaN(this.longitude)) {
        var b = this.chart.coordinatesToStageXY(this.longitude, this.latitude);
        this.left = b.x;
        this.top = b.y;
        this.latitude = this.longitude = void 0;
      }
      this.validate();
    },
    fixToMap: function () {
      if (!isNaN(this.left)) {
        var b = this.chart.stageXYToCoordinates(this.left, this.top);
        this.longitude = b.longitude;
        this.latitude = b.latitude;
        this.top = this.left = void 0;
      }
      this.validate();
    }
  });
})();
(function () {
  var a = window.AmCharts;
  a.degreesToRadians = function (b) {
    return (b / 180) * Math.PI;
  };
  a.radiansToDegrees = function (b) {
    return (b / Math.PI) * 180;
  };
  a.getColorFade = function (i, d, n) {
    var m = a.hex2RGB(d);
    d = m[0];
    var l = m[1],
      m = m[2],
      k = a.hex2RGB(i);
    i = k[0];
    var j = k[1],
      k = k[2];
    i += Math.round((d - i) * n);
    j += Math.round((l - j) * n);
    k += Math.round((m - k) * n);
    return 'rgb(' + i + ',' + j + ',' + k + ')';
  };
  a.hex2RGB = function (b) {
    return [parseInt(b.substring(1, 3), 16), parseInt(b.substring(3, 5), 16), parseInt(b.substring(5, 7), 16)];
  };
  a.processDescriptionWindow = function (d, c) {
    isNaN(c.descriptionWindowX) && (c.descriptionWindowX = d.descriptionWindowX);
    isNaN(c.descriptionWindowY) && (c.descriptionWindowY = d.descriptionWindowY);
    isNaN(c.descriptionWindowLeft) && (c.descriptionWindowLeft = d.descriptionWindowLeft);
    isNaN(c.descriptionWindowRight) && (c.descriptionWindowRight = d.descriptionWindowRight);
    isNaN(c.descriptionWindowTop) && (c.descriptionWindowTop = d.descriptionWindowTop);
    isNaN(c.descriptionWindowBottom) && (c.descriptionWindowBottom = d.descriptionWindowBottom);
    isNaN(c.descriptionWindowWidth) && (c.descriptionWindowWidth = d.descriptionWindowWidth);
    isNaN(c.descriptionWindowHeight) && (c.descriptionWindowHeight = d.descriptionWindowHeight);
  };
  a.normalizePath = function (y) {
    for (
      var x = '', w = a.parsePath(y.getAttribute('d')), v, u, s = Infinity, r = -Infinity, q = Infinity, o = -Infinity, j = 0;
      j < w.length;
      j++
    ) {
      var i = w[j],
        d = i.letter,
        z = i.x,
        i = i.y;
      'h' == d && ((d = 'L'), (z += v), (i = u));
      'H' == d && ((d = 'L'), (i = u));
      'v' == d && ((d = 'L'), (z = v), (i += u));
      'V' == d && ((d = 'L'), (z = v));
      if ('m' === d || 'l' === d) {
        (d = d.toUpperCase()), (z += v), (i += u);
      }
      z = a.roundTo(z, 3);
      i = a.roundTo(i, 3);
      v = z;
      u = i;
      z > r && (r = z);
      z < s && (s = z);
      i > o && (o = i);
      i < q && (q = i);
      x = 'z' == d.toLowerCase() ? x + 'Z ' : x + (d + ' ' + z + ' ' + i + ' ');
    }
    y.setAttribute('d', x);
    return { minX: s, maxX: r, minY: q, maxY: o };
  };
  a.mercatorLatitudeToRadians = function (b) {
    return Math.log(Math.tan(Math.PI / 4 + a.degreesToRadians(b) / 2));
  };
  a.parsePath = function (g) {
    g = g.match(/([MmLlHhVvZz]{1}[0-9.,\-\s]*)/g);
    for (var e = [], j = 0; j < g.length; j++) {
      var i = g[j].match(/([MmLlHhVvZz]{1})|([0-9.\-]+)/g),
        h = { letter: i[0] };
      switch (i[0]) {
        case 'Z':
        case 'Z':
        case 'z':
          break;
        case 'V':
        case 'v':
          h.y = Number(i[1]);
          break;
        case 'H':
        case 'h':
          h.x = Number(i[1]);
          break;
        default:
          (h.x = Number(i[1])), (h.y = Number(i[2]));
      }
      e.push(h);
    }
    return e;
  };
  a.acos = function (b) {
    return 1 < b ? 0 : -1 > b ? Math.PI : Math.acos(b);
  };
  a.asin = function (b) {
    return 1 < b ? Math.PI / 2 : -1 > b ? -Math.PI / 2 : Math.asin(b);
  };
  a.sinci = function (b) {
    return b ? b / Math.sin(b) : 1;
  };
  a.asqrt = function (b) {
    return 0 < b ? Math.sqrt(b) : 0;
  };
  a.winkel3 = function (e, d) {
    var f = a.aitoff(e, d);
    return [(f[0] + (e / Math.PI) * 2) / 2, (f[1] + d) / 2];
  };
  a.winkel3.invert = function (P, O) {
    var N = P,
      M = O,
      L = 25,
      K = Math.PI / 2;
    do {
      var J = Math.cos(M),
        I = Math.sin(M),
        H = Math.sin(2 * M),
        G = I * I,
        F = J * J,
        E = Math.sin(N),
        s = Math.cos(N / 2),
        x = Math.sin(N / 2),
        C = x * x,
        d = 1 - F * s * s,
        z = d ? a.acos(J * s) * Math.sqrt((o = 1 / d)) : (o = 0),
        o,
        d = 0.5 * (2 * z * J * x + N / K) - P,
        i = 0.5 * (z * I + M) - O,
        j = 0.5 * o * (F * C + z * J * s * G) + 0.5 / K,
        D = o * ((E * H) / 4 - z * I * x),
        I = 0.125 * o * (H * x - z * I * F * E),
        G = 0.5 * o * (G * s + z * C * J) + 0.5,
        J = D * I - G * j,
        D = (i * D - d * G) / J,
        d = (d * I - i * j) / J,
        N = N - D,
        M = M - d;
    } while ((0.000001 < Math.abs(D) || 0.000001 < Math.abs(d)) && 0 < --L);
    return [N, M];
  };
  a.aitoff = function (f, d) {
    var h = Math.cos(d),
      g = a.sinci(a.acos(h * Math.cos((f /= 2))));
    return [2 * h * Math.sin(f) * g, Math.sin(d) * g];
  };
  a.orthographic = function (d, c) {
    return [Math.cos(c) * Math.sin(d), Math.sin(c)];
  };
  a.equirectangular = function (d, c) {
    return [d, c];
  };
  a.equirectangular.invert = function (d, c) {
    return [d, c];
  };
  a.eckert5 = function (e, d) {
    var f = Math.PI;
    return [(e * (1 + Math.cos(d))) / Math.sqrt(2 + f), (2 * d) / Math.sqrt(2 + f)];
  };
  a.eckert5.invert = function (f, e) {
    var h = Math.sqrt(2 + Math.PI),
      g = (e * h) / 2;
    return [(h * f) / (1 + Math.cos(g)), g];
  };
  a.eckert6 = function (h, e) {
    for (var l = Math.PI, k = (1 + l / 2) * Math.sin(e), j = 0, i = Infinity; 10 > j && 0.00001 < Math.abs(i); j++) {
      e -= i = (e + Math.sin(e) - k) / (1 + Math.cos(e));
    }
    k = Math.sqrt(2 + l);
    return [(h * (1 + Math.cos(e))) / k, (2 * e) / k];
  };
  a.eckert6.invert = function (f, d) {
    var h = 1 + Math.PI / 2,
      g = Math.sqrt(h / 2);
    return [(2 * f * g) / (1 + Math.cos((d *= g))), a.asin((d + Math.sin(d)) / h)];
  };
  a.mercator = function (d, c) {
    c >= Math.PI / 2 - 0.02 && (c = Math.PI / 2 - 0.02);
    c <= -Math.PI / 2 + 0.02 && (c = -Math.PI / 2 + 0.02);
    return [d, Math.log(Math.tan(Math.PI / 4 + c / 2))];
  };
  a.mercator.invert = function (d, c) {
    return [d, 2 * Math.atan(Math.exp(c)) - Math.PI / 2];
  };
  a.miller = function (d, c) {
    return [d, 1.25 * Math.log(Math.tan(Math.PI / 4 + 0.4 * c))];
  };
  a.miller.invert = function (d, c) {
    return [d, 2.5 * Math.atan(Math.exp(0.8 * c)) - 0.625 * Math.PI];
  };
  a.eckert3 = function (f, e) {
    var h = Math.PI,
      g = Math.sqrt(h * (4 + h));
    return [(2 / g) * f * (1 + Math.sqrt(1 - (4 * e * e) / (h * h))), (4 / g) * e];
  };
  a.eckert3.invert = function (f, d) {
    var h = Math.PI,
      g = Math.sqrt(h * (4 + h)) / 2;
    return [(f * g) / (1 + a.asqrt(1 - (d * d * (4 + h)) / (4 * h))), (d * g) / 2];
  };
})();
(function () {
  var a = window.AmCharts;
  a.MapData = a.Class({
    inherits: a.MapObject,
    construct: function () {
      this.cname = 'MapData';
      a.MapData.base.construct.call(this);
      this.projection = 'mercator';
      this.topLatitude = 90;
      this.bottomLatitude = -90;
      this.leftLongitude = -180;
      this.rightLongitude = 180;
      this.zoomLevel = 1;
      this.getAreasFromMap = !1;
    }
  });
})();
(function () {
  var a = window.AmCharts;
  a.DescriptionWindow = a.Class({
    construct: function () {},
    show: function (r, q, p, o) {
      var n = this;
      n.chart = r;
      var m = document.createElement('div');
      m.style.position = 'absolute';
      var j = r.classNamePrefix + '-description-';
      m.className = 'ammapDescriptionWindow ' + j + 'div';
      n.div = m;
      q.appendChild(m);
      var i = '.gif';
      r.svgIcons && (i = '.svg');
      var e = document.createElement('img');
      e.className = 'ammapDescriptionWindowCloseButton ' + j + 'close-img';
      e.src = r.pathToImages + 'xIcon' + i;
      e.style.cssFloat = 'right';
      e.style.cursor = 'pointer';
      e.onclick = function () {
        n.close();
      };
      e.onmouseover = function () {
        e.src = r.pathToImages + 'xIconH' + i;
      };
      e.onmouseout = function () {
        e.src = r.pathToImages + 'xIcon' + i;
      };
      m.appendChild(e);
      q = document.createElement('div');
      q.className = 'ammapDescriptionTitle ' + j + 'title-div';
      q.onmousedown = function () {
        n.div.style.zIndex = 1000;
      };
      m.appendChild(q);
      q.innerHTML = o;
      o = q.offsetHeight;
      q = document.createElement('div');
      q.className = 'ammapDescriptionText ' + j + 'text-div';
      q.style.maxHeight = n.maxHeight - o - 20 + 'px';
      m.appendChild(q);
      q.innerHTML = p;
    },
    close: function () {
      try {
        this.div.parentNode.removeChild(this.div), this.chart.fireClosed();
      } catch (b) {}
    }
  });
})();
(function () {
  var a = window.AmCharts;
  a.ValueLegend = a.Class({
    construct: function (b) {
      this.cname = 'ValueLegend';
      this.enabled = !0;
      this.showAsGradient = !1;
      this.minValue = 0;
      this.height = 12;
      this.width = 200;
      this.bottom = this.left = 10;
      this.borderColor = '#FFFFFF';
      this.borderAlpha = this.borderThickness = 1;
      this.color = '#000000';
      this.fontSize = 11;
      a.applyTheme(this, b, this.cname);
    },
    init: function (x, w) {
      if (this.enabled) {
        var v = x.areasSettings.color,
          u = x.areasSettings.colorSolid,
          t = x.colorSteps;
        a.remove(this.set);
        var s = w.set();
        this.set = s;
        a.setCN(x, s, 'value-legend');
        var r = 0,
          q = this.minValue,
          o = this.fontSize,
          j = x.fontFamily,
          i = this.color,
          d = {
            precision: x.precision,
            decimalSeparator: x.decimalSeparator,
            thousandsSeparator: x.thousandsSeparator
          };
        void 0 == q && (q = a.formatNumber(x.minValueReal, d));
        void 0 !== q &&
          ((r = a.text(w, q, i, j, o, 'left')),
          r.translate(0, o / 2 - 1),
          a.setCN(x, r, 'value-legend-min-label'),
          s.push(r),
          (r = r.getBBox().height));
        q = this.maxValue;
        void 0 === q && (q = a.formatNumber(x.maxValueReal, d));
        void 0 !== q &&
          ((r = a.text(w, q, i, j, o, 'right')),
          r.translate(this.width, o / 2 - 1),
          a.setCN(x, r, 'value-legend-max-label'),
          s.push(r),
          (r = r.getBBox().height));
        if (this.showAsGradient) {
          (v = a.rect(w, this.width, this.height, [v, u], 1, this.borderThickness, this.borderColor, 1, 0, 0)),
            a.setCN(x, v, 'value-legend-gradient'),
            v.translate(0, r),
            s.push(v);
        } else {
          for (o = this.width / t, j = 0; j < t; j++) {
            (i = a.getColorFade(v, u, (1 * j) / (t - 1))),
              (i = a.rect(w, o, this.height, i, 1, this.borderThickness, this.borderColor, 1)),
              a.setCN(x, i, 'value-legend-color'),
              a.setCN(x, i, 'value-legend-color-' + j),
              i.translate(o * j, r),
              s.push(i);
          }
        }
        u = v = 0;
        t = s.getBBox();
        r = x.getY(this.bottom, !0);
        o = x.getY(this.top);
        j = x.getX(this.right, !0);
        i = x.getX(this.left);
        isNaN(o) || (v = o);
        isNaN(r) || (v = r - t.height);
        isNaN(i) || (u = i);
        isNaN(j) || (u = j - t.width);
        s.translate(u, v);
      } else {
        a.remove(this.set);
      }
    }
  });
})();
(function () {
  var a = window.AmCharts;
  a.ObjectList = a.Class({
    construct: function (b) {
      this.divId = b;
    },
    init: function (d) {
      this.chart = d;
      var c = this.divId;
      this.container && (c = this.container);
      this.div = 'object' != typeof c ? document.getElementById(c) : c;
      c = document.createElement('div');
      c.className = 'ammapObjectList ' + d.classNamePrefix + '-object-list-div';
      this.div.appendChild(c);
      this.addObjects(d.dataProvider, c);
    },
    addObjects: function (h, e) {
      var l = this.chart,
        k = document.createElement('ul');
      k.className = l.classNamePrefix + '-object-list-ul';
      var j;
      if (h.areas) {
        for (j = 0; j < h.areas.length; j++) {
          var i = h.areas[j];
          void 0 === i.showInList && (i.showInList = l.showAreasInList);
          this.addObject(i, k);
        }
      }
      if (h.images) {
        for (j = 0; j < h.images.length; j++) {
          (i = h.images[j]), void 0 === i.showInList && (i.showInList = l.showImagesInList), this.addObject(i, k);
        }
      }
      if (h.lines) {
        for (j = 0; j < h.lines.length; j++) {
          (i = h.lines[j]), void 0 === i.showInList && (i.showInList = l.showLinesInList), this.addObject(i, k);
        }
      }
      0 < k.childNodes.length && e.appendChild(k);
    },
    addObject: function (i, e) {
      var n = this;
      if (i.showInList && void 0 !== i.title) {
        var m = n.chart,
          l = document.createElement('li');
        l.className = m.classNamePrefix + '-object-list-li';
        var k = i.titleTr;
        k || (k = i.title);
        var k = document.createTextNode(k),
          j = document.createElement('a');
        j.className = m.classNamePrefix + '-object-list-a';
        j.appendChild(k);
        l.appendChild(j);
        e.appendChild(l);
        this.addObjects(i, l);
        j.onmouseover = function () {
          n.chart.rollOverMapObject(i, !1);
        };
        j.onmouseout = function () {
          n.chart.rollOutMapObject(i);
        };
        j.onclick = function () {
          n.chart.clickMapObject(i);
        };
      }
    }
  });
})();
