<div class="row">
  <div class="col-md-6">
    <app-card cardTitle="Bar Simple Chart" [options]="false">
      <apx-chart
        [series]="barSimpleChart.series"
        [chart]="barSimpleChart.chart"
        [dataLabels]="barSimpleChart.dataLabels"
        [plotOptions]="barSimpleChart.plotOptions"
        [yaxis]="barSimpleChart.yaxis"
        [legend]="barSimpleChart.legend"
        [fill]="barSimpleChart.fill"
        [stroke]="barSimpleChart.stroke"
        [tooltip]="barSimpleChart.tooltip"
        [xaxis]="barSimpleChart.xaxis"
      ></apx-chart>
    </app-card>
  </div>
  <div class="col-md-6">
    <app-card cardTitle="Bar Stacked Chart" [options]="false">
      <apx-chart
        [series]="barStackedChart.series"
        [chart]="barStackedChart.chart"
        [dataLabels]="barStackedChart.dataLabels"
        [plotOptions]="barStackedChart.plotOptions"
        [responsive]="barStackedChart.responsive"
        [xaxis]="barStackedChart.xaxis"
        [legend]="barStackedChart.legend"
        [fill]="barStackedChart.fill"
      ></apx-chart>
    </app-card>
  </div>
  <div class="col-md-6">
    <app-card cardTitle="Area Angle Chart" [options]="false">
      <apx-chart
        [series]="areaAngleChart.series"
        [chart]="areaAngleChart.chart"
        [fill]="areaAngleChart.fill"
        [stroke]="areaAngleChart.stroke"
        [xaxis]="areaAngleChart.xaxis"
        [tooltip]="areaAngleChart.tooltip"
      ></apx-chart>
    </app-card>
  </div>
  <div class="col-md-6">
    <app-card cardTitle="Area Smooth Chart" [options]="false">
      <apx-chart
        [series]="areaSmoothChart.series"
        [chart]="areaSmoothChart.chart"
        [xaxis]="areaSmoothChart.xaxis"
        [stroke]="areaSmoothChart.stroke"
        [tooltip]="areaSmoothChart.tooltip"
        [dataLabels]="areaSmoothChart.dataLabels"
      ></apx-chart>
    </app-card>
  </div>
  <div class="col-md-6">
    <app-card cardTitle="Line Angle Chart" [options]="false">
      <apx-chart
        [series]="lineAreaChart.series"
        [chart]="lineAreaChart.chart"
        [xaxis]="lineAreaChart.xaxis"
        [dataLabels]="lineAreaChart.dataLabels"
        [grid]="lineAreaChart.grid"
        [stroke]="lineAreaChart.stroke"
        [title]="lineAreaChart.title"
      ></apx-chart>
    </app-card>
  </div>
  <div class="col-md-6">
    <app-card cardTitle="Line Angle Chart" [options]="false">
      <apx-chart
        [series]="donutChart.series"
        [chart]="donutChart.chart"
        [labels]="donutChart.labels"
        [colors]="donutChart.colors"
        [legend]="donutChart.legend"
        [dataLabels]="donutChart.dataLabels"
        [plotOptions]="donutChart.plotOptions"
      ></apx-chart>
    </app-card>
  </div>
</div>
