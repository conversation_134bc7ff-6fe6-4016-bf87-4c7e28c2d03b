import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { CreateEmployeeDto } from '../../../../core/models/create-employee.dto';

@Component({
  selector: 'app-create-employee',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule],
  templateUrl: './create-employee.component.html',
  styleUrls: ['./create-employee.component.scss']
})
export class CreateEmployeeComponent implements OnInit {
  employeeForm: FormGroup;
  isSubmitting = false;
  isSupervisor = false;

  // Mock data for supervisors dropdown
  supervisors = [
    { id: '123e4567-e89b-12d3-a456-426614174001', name: '<PERSON>', email: '<EMAIL>' },
    { id: '123e4567-e89b-12d3-a456-426614174002', name: '<PERSON>', email: '<EMAIL>' },
    { id: '123e4567-e89b-12d3-a456-************', name: '<PERSON>', email: '<EMAIL>' },
    { id: '123e4567-e89b-12d3-a456-************', name: 'Emily Davis', email: '<EMAIL>' },
    { id: '123e4567-e89b-12d3-a456-************', name: 'David <PERSON>', email: '<EMAIL>' }
  ];

  constructor(private fb: FormBuilder) {
    this.employeeForm = this.createForm();
  }

  ngOnInit(): void {
    // Initialize form with mock company ID
    this.employeeForm.patchValue({
      company_id: '123e4567-e89b-12d3-a456-************', // Mock company UUID
      is_first_login: true,
      force_password_change: true,
      isActive: true,
      isDeleted: false
    });

    // Watch for supervisor checkbox changes
    this.employeeForm.get('isSupervisor')?.valueChanges.subscribe(value => {
      this.isSupervisor = value;
      if (value) {
        // If employee is supervisor, clear supervisor selection
        this.employeeForm.patchValue({ supervisor_id: '' });
      }
    });
  }

  private createForm(): FormGroup {
    return this.fb.group({
      company_id: ['', [Validators.required]],
      email: ['', [Validators.required, Validators.email]],
      password_hash: ['', [Validators.required, Validators.minLength(8)]],
      confirmPassword: ['', [Validators.required]],
      isSupervisor: [false],
      supervisor_id: [''],
      is_first_login: [true],
      force_password_change: [true],
      isActive: [true],
      isDeleted: [false]
    }, { validators: this.passwordMatchValidator });
  }

  // Custom validator to check if passwords match
  passwordMatchValidator(form: FormGroup) {
    const password = form.get('password_hash');
    const confirmPassword = form.get('confirmPassword');
    
    if (password && confirmPassword && password.value !== confirmPassword.value) {
      confirmPassword.setErrors({ passwordMismatch: true });
      return { passwordMismatch: true };
    }
    
    if (confirmPassword?.hasError('passwordMismatch')) {
      delete confirmPassword.errors!['passwordMismatch'];
      if (Object.keys(confirmPassword.errors!).length === 0) {
        confirmPassword.setErrors(null);
      }
    }
    
    return null;
  }

  onSubmit(): void {
    if (this.employeeForm.valid) {
      this.isSubmitting = true;
      
      const formData = { ...this.employeeForm.value };
      
      // Remove confirmPassword and isSupervisor from the data to be sent
      delete formData.confirmPassword;
      delete formData.isSupervisor;
      
      // If employee is supervisor, don't set supervisor_id
      if (this.isSupervisor) {
        delete formData.supervisor_id;
      }
      
      const employeeData: CreateEmployeeDto = formData;
      
      // Log the form data (mock API call)
      console.log('=== CREATE EMPLOYEE FORM DATA ===');
      console.log(JSON.stringify(employeeData, null, 2));
      
      // Simulate API call delay
      setTimeout(() => {
        this.isSubmitting = false;
        alert('Employee created successfully! Check console for form data.');
        this.resetForm();
      }, 1500);
    } else {
      this.markFormGroupTouched();
    }
  }

  resetForm(): void {
    this.employeeForm.reset();
    this.employeeForm.patchValue({
      company_id: '123e4567-e89b-12d3-a456-************', // Keep mock UUID
      is_first_login: true,
      force_password_change: true,
      isActive: true,
      isDeleted: false,
      isSupervisor: false
    });
    this.isSupervisor = false;
  }

  private markFormGroupTouched(): void {
    Object.keys(this.employeeForm.controls).forEach(key => {
      const control = this.employeeForm.get(key);
      control?.markAsTouched();
    });
  }

  // Helper methods for template
  isFieldInvalid(fieldName: string): boolean {
    const field = this.employeeForm.get(fieldName);
    return !!(field && field.invalid && (field.dirty || field.touched));
  }

  getFieldError(fieldName: string): string {
    const field = this.employeeForm.get(fieldName);
    if (field?.errors) {
      if (field.errors['required']) return `${fieldName.replace('_', ' ')} is required`;
      if (field.errors['email']) return 'Please enter a valid email address';
      if (field.errors['minlength']) return `Minimum ${field.errors['minlength'].requiredLength} characters required`;
      if (field.errors['passwordMismatch']) return 'Passwords do not match';
    }
    return '';
  }
}
