<div class="row">
  <!-- [ basic-collapse ] start -->
  <div class="col-sm-12">
    <h5 class="mb-3">Basic Collapse</h5>
    <hr />
    <div class="card">
      <div class="card-header">
        <button
          type="button"
          class="btn btn-primary"
          (click)="collapse.toggle()"
          (keydown)="collapse.toggle()"
          (keyup)="collapse.toggle()"
          [attr.aria-expanded]="!isCollapsed"
          title="Toggle Collapse"
        >
          Toggle with Function
        </button>
        <button
          type="button"
          class="btn btn-primary ms-2"
          (click)="isCollapsed = !isCollapsed"
          [attr.aria-expanded]="!isCollapsed"
          aria-controls="collapseExample"
        >
          Toggle with Two Way Binding
        </button>
      </div>
      <div #collapse="ngbCollapse" [(ngbCollapse)]="isCollapsed">
        <div class="card-body">
          <div>You can collapse this card by clicking one Toggle button</div>
        </div>
      </div>
    </div>
  </div>
  <!-- [ basic-collapse ] end -->
  <!-- [ multiple-collapse ] start -->
  <div class="col-sm-12 mb-3">
    <h5 class="mb-3">Multiple Targets</h5>
    <hr />
    <div class="card mb-0">
      <div class="card-header">
        <a
          class="btn btn-primary text-white me-2 mb-2"
          (click)="multiCollapsed1 = !multiCollapsed1"
          [attr.aria-expanded]="!multiCollapsed1"
          aria-controls="multiCollapseExample1"
        >
          Toggle first element
        </a>
        <button
          class="btn btn-primary me-2 mb-2"
          (click)="multiCollapsed2 = !multiCollapsed2"
          [attr.aria-expanded]="!multiCollapsed2"
          type="button"
          aria-controls="multiCollapseExample2"
        >
          Toggle second element
        </button>
        <button
          class="btn btn-primary mb-2"
          type="button"
          (click)="multiCollapsed1 = !multiCollapsed1; multiCollapsed2 = !multiCollapsed2"
          aria-controls="multiCollapseExample1 multiCollapseExample2"
        >
          Toggle both elements
        </button>
      </div>
    </div>

    <div class="row">
      <div class="col-sm-6">
        <div class="multi-collapse mt-2" id="multiCollapseExample1" [ngbCollapse]="multiCollapsed1">
          <div class="card">
            <div class="card-body">
              <p class="mb-0">
                Anim pariatur cliche reprehenderit, enim eiusmod high life accusamus terry richardson ad squid. Nihil anim keffiyeh
                helvetica, craft beer labore wes anderson cred nesciunt sapiente ea proident.
              </p>
            </div>
          </div>
        </div>
      </div>
      <div class="col-sm-6">
        <div class="multi-collapse mt-2" id="multiCollapseExample2" [ngbCollapse]="multiCollapsed2">
          <div class="card">
            <div class="card-body">
              <p class="mb-0">
                Anim pariatur cliche reprehenderit, enim eiusmod high life accusamus terry richardson ad squid. Nihil anim keffiyeh
                helvetica, craft beer labore wes anderson cred nesciunt sapiente ea proident.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <!-- [ multiple-collapse ] end -->
  <!-- [ accordion-collapse ] start -->
  <div class="col-sm-12">
    <app-card cardTitle="Accordion">
      <div ngbAccordion>
        <div ngbAccordionItem [collapsed]="false">
          <h2 ngbAccordionHeader>
            <button ngbAccordionButton>Simple</button>
          </h2>
          <div ngbAccordionCollapse>
            <div ngbAccordionBody>
              <ng-template>
                {{ loremText }}
              </ng-template>
            </div>
          </div>
        </div>
        <div ngbAccordionItem>
          <h2 ngbAccordionHeader>
            <button ngbAccordionButton>
              <span>
                &#9733;
                <b>Fancy</b>
                title &#9733;
              </span>
            </button>
          </h2>
          <div ngbAccordionCollapse>
            <div ngbAccordionBody>
              <ng-template>
                {{ loremText }}
              </ng-template>
            </div>
          </div>
        </div>
        <div ngbAccordionItem [disabled]="true">
          <h2 ngbAccordionHeader>
            <button ngbAccordionButton>Disabled</button>
          </h2>
          <div ngbAccordionCollapse>
            <div ngbAccordionBody>
              <ng-template>
                {{ loremText }}
              </ng-template>
            </div>
          </div>
        </div>
      </div>
    </app-card>

    <app-card cardTitle="Toggle Accordion">
      <div ngbAccordion #accordion="ngbAccordion">
        <div ngbAccordionItem="first">
          <h2 ngbAccordionHeader>
            <button ngbAccordionButton>First panel</button>
          </h2>
          <div ngbAccordionCollapse>
            <div ngbAccordionBody>
              <ng-template>
                {{ loremText }}
              </ng-template>
            </div>
          </div>
        </div>
        <div ngbAccordionItem="second">
          <h2 ngbAccordionHeader>
            <button ngbAccordionButton>Second panel</button>
          </h2>
          <div ngbAccordionCollapse>
            <div ngbAccordionBody>
              <ng-template>
                {{ loremText }}
              </ng-template>
            </div>
          </div>
        </div>
      </div>

      <hr />

      <button class="btn btn-sm btn-outline-primary me-2" (click)="accordion.toggle('first')">Toggle first</button>
      <button class="btn btn-sm btn-outline-primary me-2" (click)="accordion.toggle('second')">Toggle second</button>
      <button class="btn btn-sm btn-outline-primary me-2" (click)="accordion.expandAll()">Expand all</button>
      <button class="btn btn-sm btn-outline-primary me-2" (click)="accordion.collapseAll()">Collapse all</button>
    </app-card>
  </div>
  <!-- [ accordion-collapse ] end -->
</div>
