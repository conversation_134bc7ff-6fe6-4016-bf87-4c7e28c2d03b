import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Router } from '@angular/router';
import { ContactSubmissionService } from '../../../core/services/contact-submission.service';
import { ContactSubmission, ContactSubmissionFilters } from '../../../core/models/contact-submission.model';

@Component({
  selector: 'app-contact-submissions',
  standalone: true,
  imports: [CommonModule, FormsModule],
  templateUrl: './contact-submissions.component.html',
  styleUrls: ['./contact-submissions.component.scss']
})
export class ContactSubmissionsComponent implements OnInit {
  submissions: ContactSubmission[] = [];
  loading = false;
  error = '';
  
  // Pagination
  currentPage = 1;
  totalPages = 1;
  totalSubmissions = 0;
  pageSize = 10;
  
  // Filters
  searchTerm = '';
  statusFilter = 'all';
  sortBy = 'createdAt';
  sortOrder: 'asc' | 'desc' = 'desc';
  
  // Status options
  statusOptions = [
    { value: 'all', label: 'All Status' },
    { value: 'new', label: 'New' },
    { value: 'in_progress', label: 'In Progress' },
    { value: 'resolved', label: 'Resolved' },
    { value: 'closed', label: 'Closed' }
  ];

  constructor(
    private contactSubmissionService: ContactSubmissionService,
    private router: Router
  ) {}

  ngOnInit(): void {
    this.loadSubmissions();
  }

  loadSubmissions(): void {
    this.loading = true;
    this.error = '';
    
    const filters: ContactSubmissionFilters = {
      page: this.currentPage,
      limit: this.pageSize,
      sortBy: this.sortBy,
      sortOrder: this.sortOrder
    };

    if (this.statusFilter !== 'all') {
      filters.status = this.statusFilter;
    }

    if (this.searchTerm.trim()) {
      filters.search = this.searchTerm.trim();
    }

    this.contactSubmissionService.getSubmissions(filters).subscribe({
      next: (response) => {
        this.submissions = response.data.submissions;
        this.totalSubmissions = response.data.total;
        this.currentPage = response.data.page;
        this.totalPages = response.data.totalPages;
        this.loading = false;
      },
      error: (error) => {
        this.error = 'Failed to load contact submissions. Please try again.';
        this.loading = false;
        console.error('Error loading submissions:', error);
      }
    });
  }

  onSearchChange(): void {
    this.currentPage = 1;
    this.loadSubmissions();
  }

  onStatusFilterChange(): void {
    this.currentPage = 1;
    this.loadSubmissions();
  }

  onSortChange(field: string): void {
    if (this.sortBy === field) {
      this.sortOrder = this.sortOrder === 'asc' ? 'desc' : 'asc';
    } else {
      this.sortBy = field;
      this.sortOrder = 'desc';
    }
    this.loadSubmissions();
  }

  onPageChange(page: number): void {
    this.currentPage = page;
    this.loadSubmissions();
  }

  clearSearch(): void {
    this.searchTerm = '';
    this.onSearchChange();
  }

  refreshList(): void {
    this.loadSubmissions();
  }

  viewSubmission(submissionId: string): void {
    // TODO: Implement view functionality
    console.log('View submission:', submissionId);
  }

  updateStatus(submissionId: string, newStatus: string): void {
    this.contactSubmissionService.updateSubmissionStatus(submissionId, newStatus).subscribe({
      next: () => {
        this.loadSubmissions(); // Refresh the list
      },
      error: (error) => {
        console.error('Error updating status:', error);
        this.error = 'Failed to update status. Please try again.';
      }
    });
  }

  deleteSubmission(submissionId: string): void {
    if (confirm('Are you sure you want to delete this submission?')) {
      this.contactSubmissionService.deleteSubmission(submissionId).subscribe({
        next: () => {
          this.loadSubmissions(); // Refresh the list
        },
        error: (error) => {
          console.error('Error deleting submission:', error);
          this.error = 'Failed to delete submission. Please try again.';
        }
      });
    }
  }

  getStatusBadgeClass(status: string): string {
    switch (status) {
      case 'new':
        return 'badge-primary';
      case 'in_progress':
        return 'badge-warning';
      case 'resolved':
        return 'badge-success';
      case 'closed':
        return 'badge-secondary';
      default:
        return 'badge-light';
    }
  }

  getStatusText(status: string): string {
    switch (status) {
      case 'new':
        return 'New';
      case 'in_progress':
        return 'In Progress';
      case 'resolved':
        return 'Resolved';
      case 'closed':
        return 'Closed';
      default:
        return status;
    }
  }

  formatDate(dateString: string): string {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  }

  getFullName(submission: ContactSubmission): string {
    return `${submission.firstName} ${submission.lastName}`;
  }

  truncateMessage(message: string, maxLength: number = 100): string {
    if (message.length <= maxLength) {
      return message;
    }
    return message.substring(0, maxLength) + '...';
  }

  getSortIcon(field: string): string {
    if (this.sortBy !== field) {
      return 'feather icon-minus';
    }
    return this.sortOrder === 'asc' ? 'feather icon-chevron-up' : 'feather icon-chevron-down';
  }

  getPaginationArray(): number[] {
    const pages: number[] = [];
    const start = Math.max(1, this.currentPage - 2);
    const end = Math.min(this.totalPages, this.currentPage + 2);

    for (let i = start; i <= end; i++) {
      pages.push(i);
    }

    return pages;
  }

  trackBySubmissionId(index: number, submission: ContactSubmission): string {
    return submission.id;
  }

  getEndRange(): number {
    return Math.min(this.currentPage * this.pageSize, this.totalSubmissions);
  }
}
