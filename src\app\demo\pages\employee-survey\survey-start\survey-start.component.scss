// Survey Start Styling
.survey-start {
  text-align: center;
  padding: 1rem 0;
}

// Name Display
.name-display {
  margin-bottom: 3rem;

  .name-title {
    font-size: 1.8rem;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 0.5rem;
    min-height: 2.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
  }

  .name-underline {
    height: 3px;
    background: #e0e0e0;
    border-radius: 2px;
    margin: 0 auto;
    width: 80%;
    transition: all 0.3s ease;

    &.active {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      box-shadow: 0 2px 10px rgba(102, 126, 234, 0.3);
    }
  }
}

// Virtual Keyboard
.virtual-keyboard {
  margin-bottom: 2rem;

  .keyboard-row {
    display: flex;
    justify-content: center;
    gap: 0.5rem;
    margin-bottom: 0.75rem;
    flex-wrap: wrap;

    &.action-row {
      margin-top: 1rem;
      gap: 0.75rem;
    }
  }

  .key-button {
    width: 2.5rem;
    height: 2.5rem;
    border: 2px solid #e0e0e0;
    background: white;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 600;
    color: #2c3e50;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

    &:hover:not(:disabled) {
      border-color: #667eea;
      background: #f8f9ff;
      transform: translateY(-1px);
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
    }

    &:active:not(:disabled) {
      transform: translateY(0);
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    &:disabled {
      opacity: 0.5;
      cursor: not-allowed;
      background: #f5f5f5;
    }

    &.letter-key {
      font-size: 1.1rem;
    }

    &.action-key {
      width: auto;
      padding: 0 1rem;
      font-size: 0.8rem;
      gap: 0.3rem;

      i {
        font-size: 0.9rem;
      }

      &.space-key {
        background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
        color: white;
        border-color: #0984e3;

        &:hover:not(:disabled) {
          background: linear-gradient(135deg, #0984e3 0%, #74b9ff 100%);
        }
      }

      &.backspace-key {
        background: linear-gradient(135deg, #fd79a8 0%, #e84393 100%);
        color: white;
        border-color: #e84393;

        &:hover:not(:disabled) {
          background: linear-gradient(135deg, #e84393 0%, #fd79a8 100%);
        }
      }

      &.clear-key {
        background: linear-gradient(135deg, #fd7f7f 0%, #e17055 100%);
        color: white;
        border-color: #e17055;

        &:hover:not(:disabled) {
          background: linear-gradient(135deg, #e17055 0%, #fd7f7f 100%);
        }
      }
    }
  }
}

// Character Counter
.character-counter {
  margin-bottom: 2rem;

  .counter-text {
    font-size: 0.9rem;
    color: #7f8c8d;
    font-weight: 500;
  }
}

// Start Section
.start-section {
  margin: 3rem 0;
  animation: fadeInUp 0.5s ease;

  .start-button {
    background: linear-gradient(135deg, #00b894 0%, #00a085 100%);
    color: white;
    border: none;
    padding: 1rem 2rem;
    border-radius: 12px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin: 0 auto 1rem;
    box-shadow: 0 4px 15px rgba(0, 184, 148, 0.3);

    &:hover:not(:disabled) {
      background: linear-gradient(135deg, #00a085 0%, #00b894 100%);
      transform: translateY(-2px);
      box-shadow: 0 6px 20px rgba(0, 184, 148, 0.4);
    }

    &:disabled {
      opacity: 0.6;
      cursor: not-allowed;
      transform: none;
    }

    i {
      font-size: 1.2rem;
    }
  }

  .start-instruction {
    color: #7f8c8d;
    font-size: 0.9rem;
    margin: 0;
  }
}

// Instructions
.instructions {
  margin-top: 2rem;

  .instruction-card {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    text-align: left;

    .instruction-icon {
      font-size: 2rem;
      color: #667eea;
      margin-bottom: 1rem;
      display: block;
      text-align: center;
    }

    h3 {
      color: #2c3e50;
      margin-bottom: 1rem;
      text-align: center;
      font-size: 1.2rem;
    }

    p {
      color: #7f8c8d;
      margin-bottom: 1rem;
      text-align: center;
    }

    .instruction-list {
      list-style: none;
      padding: 0;
      margin: 0;

      li {
        color: #5a6c7d;
        margin-bottom: 0.5rem;
        padding-left: 1.5rem;
        position: relative;

        &:before {
          content: '•';
          color: #667eea;
          font-weight: bold;
          position: absolute;
          left: 0;
        }
      }
    }
  }
}

// Animations
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// Responsive Design
@media (max-width: 480px) {
  .virtual-keyboard {
    .key-button {
      width: 2.2rem;
      height: 2.2rem;
      font-size: 0.9rem;

      &.action-key {
        padding: 0 0.75rem;
        font-size: 0.75rem;
      }
    }

    .keyboard-row {
      gap: 0.4rem;
    }
  }

  .name-display .name-title {
    font-size: 1.5rem;
  }
}
