<!-- Survey Quiz Interface -->
<div class="survey-quiz" *ngIf="currentQuestion">
  <!-- Progress Header -->
  <div class="progress-header">
    <div class="employee-info">
      <h3 class="employee-name">{{ getEmployeeName() }}</h3>
    </div>
    
    <div class="progress-info">
      <div class="progress-bar">
        <div class="progress-fill" [style.width.%]="progress.percentage"></div>
      </div>
      <span class="progress-text">{{ progress.current + 1 }} of {{ progress.total }}</span>
    </div>
  </div>

  <!-- Question Section -->
  <div class="question-section">
    <div class="question-header">
      <span class="question-number">Question {{ getQuestionNumber() }}</span>

      <!-- Next Button in Header -->
      <div class="header-actions" *ngIf="showConfirmation && !isSubmitting">
        <button class="next-button" (click)="onConfirmAnswer()">
          <i class="feather icon-arrow-right"></i>
          Next
        </button>
      </div>
    </div>

    <div class="question-content">
      <h2 class="question-text">{{ currentQuestion.question_text }}</h2>
    </div>
  </div>

  <!-- Options Section -->
  <div class="options-section">
    <div class="options-grid compact">
      <button
        class="option-button compact"
        *ngFor="let option of currentQuestion.options; let i = index"
        [class.selected]="selectedOption?.id === option.id"
        [disabled]="isSubmitting"
        (click)="onOptionSelect(option)">

        <div class="option-header">
          <span class="option-content">
             <span class="option-text">{{ option.option_text }}</span>
          </span>
          <div class="option-indicator" [class.active]="selectedOption?.id === option.id">
            <i class="feather icon-check" *ngIf="selectedOption?.id === option.id"></i>
          </div>
        </div>

        <!-- <div class="option-content">
         
        </div> -->
      </button>
    </div>
  </div>

  <!-- Selected Answer Display -->
  <!-- <div class="selected-answer-section" *ngIf="showConfirmation && !isSubmitting">
    <div class="selected-answer-card">
      <div class="selected-answer-content">
        <i class="feather icon-check-circle answer-icon"></i>
        <div class="answer-info">
          <span class="answer-label">You selected:</span>
          <span class="answer-text">{{ selectedOption?.option_text }}</span>
        </div>
      </div>
    </div>
  </div> -->

  <!-- Submitting State -->
  <div class="submitting-section" *ngIf="isSubmitting">
    <div class="submitting-card">
      <div class="spinner"></div>
      <p class="submitting-text">Submitting your answer...</p>
    </div>
  </div>

  <!-- Instructions -->
  <div class="instructions" *ngIf="!showConfirmation && !isSubmitting">
    <div class="instruction-text">
      <i class="feather icon-info instruction-icon"></i>
      Tap an option above to select your answer
    </div>
  </div>
</div>

<!-- Loading State -->
<div class="loading-state" *ngIf="!currentQuestion">
  <div class="loading-card">
    <div class="spinner"></div>
    <p class="loading-text">Loading your survey...</p>
  </div>
</div>
