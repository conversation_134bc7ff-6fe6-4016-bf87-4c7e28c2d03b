export interface CreateEmployeeDto {
  company_id: string;
  supervisor_id?: string;
  email: string;
  password_hash: string;
  is_first_login: boolean;
  force_password_change: boolean;
  isActive: boolean;
  createdBy?: string;
  updatedBy?: string;
  isDeleted: boolean;
  deletedBy?: string;
}

export interface Employee {
  id: string;
  company_id: string;
  supervisor_id?: string;
  email: string;
  password_hash: string;
  is_first_login: boolean;
  force_password_change: boolean;
  isActive: boolean;
  createdAt: Date;
  createdBy?: string;
  updatedAt: Date;
  updatedBy?: string;
  isDeleted: boolean;
  deletedBy?: string;
}
