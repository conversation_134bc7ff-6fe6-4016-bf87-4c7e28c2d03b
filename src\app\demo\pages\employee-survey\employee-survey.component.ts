import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';

@Component({
  selector: 'app-employee-survey',
  standalone: true,
  imports: [CommonModule, RouterModule],
  templateUrl: './employee-survey.component.html',
  styleUrls: ['./employee-survey.component.scss']
})
export class EmployeeSurveyComponent {
  currentTime = new Date().toLocaleTimeString('en-US', { 
    hour: '2-digit', 
    minute: '2-digit',
    hour12: true 
  });

  constructor() {
    // Update time every minute
    setInterval(() => {
      this.currentTime = new Date().toLocaleTimeString('en-US', { 
        hour: '2-digit', 
        minute: '2-digit',
        hour12: true 
      });
    }, 60000);
  }
}
