// src/app/app-routing.module.ts
import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';
import { AuthGuard } from './core/guards/auth.guard';
import { GuestComponent } from './theme/layout/guest/guest.component';
import { HTTP_INTERCEPTORS, provideHttpClient } from '@angular/common/http';
import { AppComponent } from './app.component';
import { AdminComponent } from './theme/layout/admin/admin.component';

const routes: Routes = [
  {
    path: '',
    component: AdminComponent,
    children: [
      {
        path: '',
        redirectTo: 'company',
        pathMatch: 'full'
      },
      {
        path: 'questions',
        loadChildren: () => import('./demo/pages/questions/question.module').then(m => m.QuestionModule),
        canActivate: [AuthGuard],
        // data: { roles: ['ProductOwner'] }
      },
      {
        path: 'company-questions',
        loadChildren: () => import('./demo/pages/company-questions/company-questions.module').then(m => m.CompanyQuestionsModule),
        canActivate: [AuthGuard],
        data: { roles: ['ProductOwner'] }
      },
      {
        path: 'company',
        loadChildren: () => import('./demo/pages/company/company.module').then(m => m.CompanyModule),
        canActivate: [AuthGuard],
        // data: { roles: ['ProductOwner'] }
      },
      {
        path: 'inquiry',
        loadChildren: () => import('./demo/pages/contact-submissions/contact-submissions.module').then(m => m.ContactSubmissionsModule),
       
        canActivate: [AuthGuard],
        // data: { roles: ['ProductOwner', 'Admin'] }
      },

    ]
  },
  {
    path: 'employee-survey',
    loadChildren: () => import('./demo/pages/employee-survey/employee-survey.module').then(m => m.EmployeeSurveyModule)
  },
  {
    path: '',
    component: GuestComponent,
    children: [
      {
        path: 'auth',
        loadChildren: () => import('./demo/pages/authentication/authentication.module').then((m) => m.AuthenticationModule)
      }
    ]
  }
];

@NgModule({
  imports: [RouterModule.forRoot(routes)],
  exports: [RouterModule],
  // providers: [
  //   provideHttpClient(),
  //   { provide: HTTP_INTERCEPTORS, useClass: AuthInterceptor, multi: true }
  // ],
})
export class AppRoutingModule { }