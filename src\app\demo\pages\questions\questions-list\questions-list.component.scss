// Questions List Component Styles

.questions-container {
  padding: 20px;
  background-color: #f8f9fa;
  min-height: 100vh;
}

// Page Header
.page-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  padding: 2rem;
  color: white;
  box-shadow: 0 4px 20px rgba(102, 126, 234, 0.3);

  .page-title {
    font-size: 2rem;
    font-weight: 700;
    margin: 0;
    display: flex;
    align-items: center;
  }

  .page-subtitle {
    margin: 0.5rem 0 0 0;
    opacity: 0.9;
    font-size: 1.1rem;
  }

  .btn-create {
    background: rgba(255, 255, 255, 0.2);
    border: 2px solid rgba(255, 255, 255, 0.3);
    color: white;
    font-weight: 600;
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    transition: all 0.3s ease;

    &:hover {
      background: rgba(255, 255, 255, 0.3);
      border-color: rgba(255, 255, 255, 0.5);
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
    }
  }
}

// Search and Filter Section
.search-filter-card {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
  border: 1px solid #e9ecef;

  .search-container {
    .search-input-wrapper {
      position: relative;
      display: flex;
      align-items: center;

      .search-icon {
        position: absolute;
        left: 1rem;
        color: #6c757d;
        font-size: 1.1rem;
        z-index: 2;
      }

      .search-input {
        padding-left: 3rem;
        padding-right: 3rem;
        border: 2px solid #e9ecef;
        border-radius: 8px;
        font-size: 1rem;
        transition: all 0.3s ease;

        &:focus {
          border-color: #667eea;
          box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
          outline: none;
        }

        &::placeholder {
          color: #adb5bd;
        }
      }

      .btn-clear {
        position: absolute;
        right: 0.5rem;
        background: none;
        border: none;
        color: #6c757d;
        padding: 0.25rem;
        border-radius: 4px;
        transition: all 0.2s ease;

        &:hover {
          color: #dc3545;
          background: rgba(220, 53, 69, 0.1);
        }

        i {
          font-size: 1rem;
        }
      }
    }
  }

  .filter-container {
    .filter-label {
      font-size: 0.9rem;
      font-weight: 600;
      color: #495057;
      margin-bottom: 0.5rem;
      display: block;
    }

    .status-filter {
      border: 2px solid #e9ecef;
      border-radius: 8px;
      font-size: 0.95rem;
      transition: all 0.3s ease;

      &:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        outline: none;
      }
    }
  }

  .search-results {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;

    .results-text {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      padding: 0.5rem 1rem;
      border-radius: 20px;
      font-size: 0.9rem;
      font-weight: 500;
      display: flex;
      align-items: center;
    }
  }
}

// Loading Container
.loading-container {
  .card {
    border: none;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  }

  .spinner-border {
    width: 3rem;
    height: 3rem;
  }
}

// Stats Cards
.stats-card {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
  border: 1px solid #e9ecef;
  transition: all 0.3s ease;
  margin-bottom: 1rem;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.12);
  }

  .stats-icon {
    width: 50px;
    height: 50px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1rem;

    i {
      font-size: 1.5rem;
      color: white;
    }

    &.bg-primary { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
    &.bg-success { background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%); }
    &.bg-warning { background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); }
    &.bg-info { background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); }
  }

  .stats-content {
    h3 {
      font-size: 2rem;
      font-weight: 700;
      margin: 0;
      color: #2c3e50;
    }

    p {
      margin: 0;
      color: #6c757d;
      font-weight: 500;
    }
  }
}

// Question Cards
.question-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
  border: 1px solid #e9ecef;
  margin-bottom: 1.5rem;
  transition: all 0.3s ease;
  overflow: hidden;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.12);
  }

  &.inactive {
    opacity: 0.7;
    border-left: 4px solid #ffc107;
  }

  &:not(.inactive) {
    border-left: 4px solid #28a745;
  }
}

.question-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem 1.5rem 0 1.5rem;

  .question-meta {
    display: flex;
    align-items: center;
    gap: 1rem;
  }

  .question-number {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-weight: 600;
    font-size: 0.9rem;
  }

  .question-status {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-weight: 500;
    font-size: 0.85rem;

    &.active {
      background: rgba(40, 167, 69, 0.1);
      color: #28a745;
    }

    &.inactive {
      background: rgba(255, 193, 7, 0.1);
      color: #ffc107;
    }
  }

  .question-actions {
    display: flex;
    gap: 0.5rem;

    .btn {
      border-radius: 8px;
      transition: all 0.2s ease;

      &:hover {
        transform: translateY(-1px);
      }
    }
  }
}

// Question Content
.question-content {
  padding: 0 1.5rem 1.5rem 1.5rem;

  .question-text {
    font-size: 1.25rem;
    font-weight: 600;
    color: #2c3e50;
    margin: 1rem 0 1.5rem 0;
    line-height: 1.4;
  }

  .options-container {
    .options-title {
      font-size: 1rem;
      font-weight: 600;
      color: #495057;
      margin-bottom: 1rem;
      display: flex;
      align-items: center;
    }

    .options-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 0.75rem;
    }

    .option-item {
      background: #f8f9fa;
      border: 1px solid #e9ecef;
      border-radius: 8px;
      padding: 0.75rem 1rem;
      transition: all 0.2s ease;

      &:hover {
        background: #e9ecef;
        border-color: #dee2e6;
      }

      .option-content {
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex-wrap: wrap;
        gap: 0.5rem;

        .option-text {
          font-weight: 500;
          color: #495057;
          flex: 1;
        }

        .option-value {
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          color: white;
          padding: 0.25rem 0.5rem;
          border-radius: 12px;
          font-size: 0.85rem;
          font-weight: 600;
          min-width: 30px;
          text-align: center;
        }

        .option-category {
          color: white;
          padding: 0.2rem 0.6rem;
          border-radius: 10px;
          font-size: 0.75rem;
          font-weight: 500;
          text-transform: uppercase;
          letter-spacing: 0.5px;
          border: none;
          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
          transition: all 0.2s ease;

          &:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
          }
        }
      }
    }
  }
}

// Question Footer
.question-footer {
  padding: 0 1.5rem 1.5rem 1.5rem;
  border-top: 1px solid #f1f3f4;
  margin-top: 1rem;
  padding-top: 1rem;

  .question-dates {
    i {
      font-size: 0.85rem;
    }
  }
}

// Empty State
.empty-state {
  .card {
    border: none;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  }

  .empty-icon {
    font-size: 4rem;
    color: #dee2e6;
    margin-bottom: 1rem;
  }

  h4 {
    color: #6c757d;
    margin-bottom: 0.5rem;
  }
}

// Responsive Design
@media (max-width: 768px) {
  .questions-container {
    padding: 10px;
  }

  .page-header {
    padding: 1.5rem;

    .page-title {
      font-size: 1.5rem;
    }

    .d-flex {
      flex-direction: column;
      align-items: flex-start !important;
      gap: 1rem;
    }

    .btn-create {
      width: 100%;
      justify-content: center;
    }
  }

  .stats-card {
    margin-bottom: 1rem;
  }

  .question-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;

    .question-actions {
      width: 100%;
      justify-content: flex-end;
    }
  }

  .options-grid {
    grid-template-columns: 1fr;
  }

  .search-filter-card {
    padding: 1rem;

    .row {
      row-gap: 1rem;
    }

    .search-results {
      justify-content: flex-start;

      .results-text {
        font-size: 0.8rem;
        padding: 0.4rem 0.8rem;
      }
    }
  }

  .pagination-container {
    padding: 1rem;

    .row {
      flex-direction: column;
      gap: 1rem;
    }

    .page-size-selector {
      justify-content: center;
    }

    .pagination {
      justify-content: center !important;

      .page-item .page-link {
        padding: 0.4rem 0.6rem;
        min-width: 35px;
        height: 35px;
        font-size: 0.85rem;
      }
    }

    .pagination-info {
      text-align: center;

      small {
        font-size: 0.8rem;
        padding: 0.4rem 0.8rem;
      }
    }
  }
}

// Animation for smooth transitions
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.question-card {
  animation: fadeInUp 0.3s ease-out;
}

.stats-card {
  animation: fadeInUp 0.3s ease-out;
}

// Search highlighting
.search-highlight {
  background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
  color: #856404;
  padding: 0.1rem 0.2rem;
  border-radius: 3px;
  font-weight: 600;
  border: 1px solid #ffeaa7;
}

// Pagination Styles
.pagination-container {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
  border: 1px solid #e9ecef;
  margin-top: 2rem;

  .page-size-selector {
    display: flex;
    align-items: center;
    gap: 0.5rem;

    .form-label {
      font-weight: 600;
      color: #495057;
      margin-bottom: 0;
      font-size: 0.9rem;
    }

    .form-select {
      border: 2px solid #e9ecef;
      border-radius: 8px;
      font-size: 0.9rem;
      transition: all 0.3s ease;
      cursor: pointer;

      &:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        outline: none;
      }
    }
  }

  .pagination {
    margin-bottom: 0;

    .page-item {
      .page-link {
        border: none;
        color: #6c757d;
        padding: 0.5rem 0.75rem;
        margin: 0 0.125rem;
        border-radius: 8px;
        transition: all 0.2s ease;
        font-weight: 500;
        display: flex;
        align-items: center;
        justify-content: center;
        min-width: 40px;
        height: 40px;

        &:hover {
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          color: white;
          transform: translateY(-1px);
          box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
        }

        &:focus {
          box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }

        i {
          font-size: 0.9rem;
        }
      }

      &.active .page-link {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        box-shadow: 0 2px 8px rgba(102, 126, 234, 0.4);
        transform: translateY(-1px);

        &:hover {
          background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
        }
      }

      &.disabled .page-link {
        color: #adb5bd;
        cursor: not-allowed;
        background: #f8f9fa;

        &:hover {
          background: #f8f9fa;
          color: #adb5bd;
          transform: none;
          box-shadow: none;
        }
      }
    }
  }

  .pagination-info {
    color: #6c757d;
    font-size: 0.875rem;
    font-weight: 500;

    small {
      background: rgba(102, 126, 234, 0.1);
      padding: 0.5rem 1rem;
      border-radius: 20px;
      color: #667eea;
      font-weight: 600;
    }
  }
}

// Custom scrollbar for better UX
.questions-container {
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;

    &:hover {
      background: #a8a8a8;
    }
  }
}
