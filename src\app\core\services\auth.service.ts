import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { BehaviorSubject, Observable, throwError } from 'rxjs';
import { catchError, map, tap } from 'rxjs/operators';
import { environment } from '../../../environments/environment';

@Injectable({
  providedIn: 'root'
})
export class AuthService {
  private currentUserSubject = new BehaviorSubject<any>(null);
  public currentUser$ = this.currentUserSubject.asObservable();
  private refreshTokenTimeout: any;

  constructor(private http: HttpClient) {
    const user = this.getUserFromStorage();
    if (user) {
      this.currentUserSubject.next(user);
      this.startRefreshTokenTimer();
    }
  }

  public get currentUserValue(): any {
    return this.currentUserSubject.value;
  }

  login(email: string, password: string): Observable<any> {
    return this.http.post<any>(`${environment.apiUrl}/product-owner-auth/login`, { email, password })
      .pipe(
        tap(response => {
          this.storeUserData(response);
          this.currentUserSubject.next(response.user);
          this.startRefreshTokenTimer();
        }),
        catchError(error => {
          console.error('Login error:', error);
          return throwError(() => error);
        })
      );
  }

  logout() {
    localStorage.removeItem('user');
    localStorage.removeItem('tokens');
    this.currentUserSubject.next(null);
    this.stopRefreshTokenTimer();
  }

  refreshToken(): Observable<any> {
    const tokens = this.getTokensFromStorage();
    if (!tokens?.refresh_token) {
      return throwError(() => new Error('No refresh token available'));
    }

    return this.http.post<any>(`${environment.apiUrl}/product-owner-auth/refresh-token`, {
      refreshToken: tokens.refresh_token
    }).pipe(
      tap(response => {
        this.storeUserData(response);
        this.currentUserSubject.next(response.user);
        this.startRefreshTokenTimer();
      }),
      catchError(error => {
        this.logout();
        return throwError(() => error);
      })
    );
  }

  isLoggedIn(): boolean {
    return !!this.currentUserValue;
  }

  hasRole(role: string): boolean {
    return this.currentUserValue?.role === role;
  }

  getAccessToken(): string | null {
    const tokens = this.getTokensFromStorage();
    return tokens?.access_token || null;
  }

  private storeUserData(response: any) {
    if (response?.access_token && response?.refresh_token) {
      localStorage.setItem('tokens', JSON.stringify({
        access_token: response.access_token,
        refresh_token: response.refresh_token
      }));
    }

    if (response?.user) {
      localStorage.setItem('user', JSON.stringify(response.user));
    }
  }

  private getUserFromStorage(): any {
    const userStr = localStorage.getItem('user');
    return userStr ? JSON.parse(userStr) : null;
  }

  private getTokensFromStorage(): any {
    const tokensStr = localStorage.getItem('tokens');
    return tokensStr ? JSON.parse(tokensStr) : null;
  }

  private startRefreshTokenTimer() {
    const tokens = this.getTokensFromStorage();
    if (!tokens?.access_token) return;

    const jwtToken = JSON.parse(atob(tokens.access_token.split('.')[1]));
    const expires = new Date(jwtToken.exp * 1000);
    const timeout = expires.getTime() - Date.now() - (60 * 1000);

    this.refreshTokenTimeout = setTimeout(() => this.refreshToken().subscribe(), timeout);
  }

  private stopRefreshTokenTimer() {
    clearTimeout(this.refreshTokenTimeout);
  }
}
