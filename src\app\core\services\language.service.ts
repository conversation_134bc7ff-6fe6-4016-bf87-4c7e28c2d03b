import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';

export interface Language {
  code: string;
  name: string;
  flag: string;
  isRTL?: boolean;
}

@Injectable({
  providedIn: 'root'
})
export class LanguageService {
  private currentLanguageSubject = new BehaviorSubject<string>('en');
  public currentLanguage$ = this.currentLanguageSubject.asObservable();

  private supportedLanguages: Language[] = [
    { code: 'en', name: 'English', flag: '🇺🇸', isRTL: false },
    { code: 'es', name: 'Spanish', flag: '🇪🇸', isRTL: false }
  ];

  constructor() {
    // Load saved language preference
    const savedLanguage = localStorage.getItem('selectedLanguage');
    if (savedLanguage && this.isLanguageSupported(savedLanguage)) {
      this.currentLanguageSubject.next(savedLanguage);
    }
  }

  getSupportedLanguages(): Language[] {
    return this.supportedLanguages;
  }

  getCurrentLanguage(): string {
    return this.currentLanguageSubject.value;
  }

  setCurrentLanguage(languageCode: string): void {
    if (this.isLanguageSupported(languageCode)) {
      this.currentLanguageSubject.next(languageCode);
      localStorage.setItem('selectedLanguage', languageCode);
    }
  }

  isLanguageSupported(languageCode: string): boolean {
    return this.supportedLanguages.some(lang => lang.code === languageCode);
  }

  getLanguageByCode(code: string): Language | undefined {
    return this.supportedLanguages.find(lang => lang.code === code);
  }

  isRTL(languageCode?: string): boolean {
    const code = languageCode || this.getCurrentLanguage();
    const language = this.getLanguageByCode(code);
    return language?.isRTL || false;
  }

  // Helper method to get text in current language or fallback to English
  getLocalizedText(multiLangText: any, fallbackLanguage: string = 'en'): string {
    if (typeof multiLangText === 'string') {
      return multiLangText;
    }
    
    if (typeof multiLangText === 'object' && multiLangText !== null) {
      const currentLang = this.getCurrentLanguage();
      return multiLangText[currentLang] || 
             multiLangText[fallbackLanguage] || 
             multiLangText['en'] || 
             Object.values(multiLangText)[0] || 
             '';
    }
    
    return '';
  }

  // Helper method to validate multi-language object
  validateMultiLanguageText(multiLangText: any, requiredLanguages: string[] = ['en']): boolean {
    if (typeof multiLangText !== 'object' || multiLangText === null) {
      return false;
    }

    return requiredLanguages.every(lang => 
      multiLangText[lang] && 
      typeof multiLangText[lang] === 'string' && 
      multiLangText[lang].trim().length > 0
    );
  }
}
